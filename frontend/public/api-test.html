<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 API连接测试</h1>
    
    <button onclick="testProxy()">测试代理 (/api/auth/login)</button>
    <button onclick="testDirect()">测试直连 (8081端口)</button>
    <button onclick="clearResult()">清除结果</button>
    
    <div id="result"></div>

    <script>
        const loginData = {
            username: "testuser002",
            password: "Test123456",
            rememberMe: false,
            deviceInfo: "test-device",
            ipAddress: "127.0.0.1"
        };

        async function testProxy() {
            showResult('🔄 测试代理连接...', 'info');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.text();
                showResult(`✅ 代理测试结果:\n状态: ${response.status}\n响应: ${data}`, 
                          response.ok ? 'success' : 'error');
            } catch (error) {
                showResult(`❌ 代理测试失败:\n${error.message}`, 'error');
            }
        }

        async function testDirect() {
            showResult('🔄 测试直连...', 'info');
            try {
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData),
                    mode: 'cors'
                });
                
                const data = await response.text();
                showResult(`✅ 直连测试结果:\n状态: ${response.status}\n响应: ${data}`, 
                          response.ok ? 'success' : 'error');
            } catch (error) {
                showResult(`❌ 直连测试失败:\n${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const div = document.getElementById('result');
            div.textContent = message;
            div.className = `result ${type}`;
        }

        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }
    </script>
</body>
</html>
