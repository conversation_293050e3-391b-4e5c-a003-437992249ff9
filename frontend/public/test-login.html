<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"], input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus, input[type="url"]:focus {
            border-color: #007bff;
            outline: none;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin: 30px 0;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 2px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 2px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 2px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            text-align: center;
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 VisThink ERP 登录API测试</h1>
        
        <div class="form-group">
            <label for="apiUrl">API地址:</label>
            <input type="url" id="apiUrl" value="http://localhost:3000/api/auth/login" placeholder="输入API地址">
        </div>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="testuser002" placeholder="输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="Test123456" placeholder="输入密码">
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="rememberMe">
                <label for="rememberMe">记住我</label>
            </div>
        </div>
        
        <div class="button-group">
            <button class="btn-primary" onclick="testLogin()" id="loginBtn">🚀 测试登录</button>
            <button class="btn-secondary" onclick="testDirect()" id="directBtn">🔗 直连后端</button>
            <button class="btn-success" onclick="generateCurl()" id="curlBtn">📋 生成cURL</button>
            <button class="btn-secondary" onclick="clearResult()">🗑️ 清除结果</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let isLoading = false;

        function setLoading(loading) {
            isLoading = loading;
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = loading);
            
            if (loading) {
                showResult('⏳ 正在发送请求...', 'info');
            }
        }

        function getLoginData() {
            return {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                rememberMe: document.getElementById('rememberMe').checked,
                deviceInfo: getDeviceInfo(),
                ipAddress: "127.0.0.1"
            };
        }

        function getDeviceInfo() {
            return `${navigator.platform} - ${navigator.userAgent.substring(0, 100)}`;
        }

        async function testLogin() {
            if (isLoading) return;
            
            setLoading(true);
            const apiUrl = document.getElementById('apiUrl').value;
            const loginData = getLoginData();
            
            try {
                const startTime = Date.now();
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                let responseData;
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    responseData = await response.json();
                } else {
                    responseData = await response.text();
                }
                
                const result = {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    responseTime: `${responseTime}ms`,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData,
                    requestData: loginData,
                    requestUrl: apiUrl
                };
                
                if (response.ok) {
                    showResult(`✅ 登录成功！\n响应时间: ${responseTime}ms\n\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 登录失败！\n状态码: ${response.status}\n响应时间: ${responseTime}ms\n\n${JSON.stringify(result, null, 2)}`, 'error');
                }
                
            } catch (error) {
                showResult(`💥 请求异常！\n错误信息: ${error.message}\n\n请求数据:\n${JSON.stringify(loginData, null, 2)}`, 'error');
            } finally {
                setLoading(false);
            }
        }

        async function testDirect() {
            if (isLoading) return;
            
            setLoading(true);
            const directUrl = 'http://localhost:8081/api/auth/login';
            const loginData = getLoginData();
            
            try {
                const startTime = Date.now();
                const response = await fetch(directUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData),
                    mode: 'cors'
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                let responseData;
                try {
                    responseData = await response.json();
                } catch {
                    responseData = await response.text();
                }
                
                const result = {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    responseTime: `${responseTime}ms`,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData,
                    requestData: loginData,
                    requestUrl: directUrl
                };
                
                if (response.ok) {
                    showResult(`✅ 直连后端成功！\n响应时间: ${responseTime}ms\n\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 直连后端失败！\n状态码: ${response.status}\n响应时间: ${responseTime}ms\n\n${JSON.stringify(result, null, 2)}`, 'error');
                }
                
            } catch (error) {
                showResult(`💥 直连请求异常！\n错误信息: ${error.message}\n\n这可能是CORS跨域问题`, 'error');
            } finally {
                setLoading(false);
            }
        }

        function generateCurl() {
            const apiUrl = document.getElementById('apiUrl').value;
            const loginData = getLoginData();
            
            const curlCommand = `curl -X 'POST' \\
  '${apiUrl}' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -d '${JSON.stringify(loginData, null, 2)}'`;
            
            showResult(`📋 cURL命令已生成：\n\n${curlCommand}`, 'info');
            
            // 复制到剪贴板
            navigator.clipboard.writeText(curlCommand).then(() => {
                console.log('cURL命令已复制到剪贴板');
            }).catch(err => {
                console.log('复制失败:', err);
            });
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
        
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '';
            resultDiv.className = '';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('登录测试页面已加载');
            showResult('🎯 准备就绪，请点击按钮开始测试', 'info');
        });
    </script>
</body>
</html>
