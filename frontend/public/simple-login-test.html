<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            width: 100%;
            padding: 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录功能测试</h1>
        
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="testuser002">
        </div>
        
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="Test123456">
        </div>
        
        <button onclick="testLogin()" id="testBtn">🚀 测试登录</button>
        <button onclick="clearResult()">🗑️ 清除结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testLogin() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '⏳ 登录中...';
            
            const loginData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                rememberMe: false,
                deviceInfo: navigator.userAgent.substring(0, 50),
                ipAddress: "127.0.0.1"
            };
            
            showResult('📤 发送登录请求...', 'info');
            
            try {
                // 使用前端代理地址
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                const result = await response.text();
                let data;
                try {
                    data = JSON.parse(result);
                } catch {
                    data = result;
                }
                
                if (response.ok) {
                    showResult(`✅ 登录成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 登录失败！\n\n状态码: ${response.status}\n错误信息:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
                
            } catch (error) {
                showResult(`💥 请求异常！\n\n错误: ${error.message}\n\n请求数据:\n${JSON.stringify(loginData, null, 2)}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 测试登录';
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
        
        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            showResult('✨ 准备就绪！点击按钮开始测试登录功能', 'info');
        });
    </script>
</body>
</html>
