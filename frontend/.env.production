# 生产环境配置

# 应用标题
VITE_APP_TITLE=VisThink ERP

# API基础URL - 生产环境member-center微服务地址
VITE_API_BASE_URL=https://api.visthink-erp.com

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=15000

# 是否启用Mock数据
VITE_USE_MOCK=false

# 是否启用开发工具
VITE_DEV_TOOLS=false

# 是否显示性能监控信息
VITE_SHOW_PERFORMANCE=false

# 是否启用路由缓存
VITE_ROUTE_CACHE=true

# 上传文件大小限制（MB）
VITE_UPLOAD_SIZE_LIMIT=50

# 是否启用PWA
VITE_USE_PWA=true

# 构建时是否生成sourcemap
VITE_BUILD_SOURCEMAP=false

# 是否启用gzip压缩
VITE_BUILD_GZIP=true

# 是否启用分析工具
VITE_BUILD_ANALYZE=false

# 日志级别 (error | warn | info | debug)
VITE_LOG_LEVEL=warn

# 是否启用错误监控
VITE_ERROR_MONITORING=true

# WebSocket连接地址
VITE_WS_URL=wss://api.visthink-erp.com/ws

# 文件服务地址
VITE_FILE_SERVICE_URL=https://files.visthink-erp.com

# 是否启用多语言
VITE_I18N_ENABLED=true

# 默认语言
VITE_I18N_DEFAULT_LOCALE=zh-CN

# 主题配置
VITE_THEME_COLOR=#1890ff
VITE_THEME_MODE=light

# CDN配置
VITE_CDN_URL=https://cdn.visthink-erp.com

# 错误监控配置
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# 统计分析配置
VITE_GA_ID=GA-XXXXXXXXX
