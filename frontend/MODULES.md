# VisThink ERP 系统功能模块详解

## 📋 目录

- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [核心功能模块](#核心功能模块)
  - [1. 用户管理](#1-用户管理)
  - [2. 在线用户监控 ⭐️](#2-在线用户监控-⭐️)
  - [3. 角色管理](#3-角色管理)
  - [4. 菜单管理](#4-菜单管理)
  - [5. 部门管理](#5-部门管理)
  - [6. 岗位管理](#6-岗位管理)
  - [7. 租户管理 🚀](#7-租户管理-🚀)
  - [8. 租户套餐 🚀](#8-租户套餐-🚀)
  - [9. 字典管理](#9-字典管理)
  - [10. 短信管理 🚀](#10-短信管理-🚀)
  - [11. 邮件管理 🚀](#11-邮件管理-🚀)
  - [12. 站内信 🚀](#12-站内信-🚀)
  - [13. 操作日志 🚀](#13-操作日志-🚀)
  - [14. 登录日志 ⭐️](#14-登录日志-⭐️)
  - [15. 错误码管理 🚀](#15-错误码管理-🚀)
  - [16. 通知公告](#16-通知公告)
  - [17. 敏感词管理 🚀](#17-敏感词管理-🚀)
  - [18. 应用管理 🚀](#18-应用管理-🚀)
  - [19. 地区管理 🚀](#19-地区管理-🚀)

## 系统概述

VisThink ERP 是一套完整的企业资源规划系统，前端采用现代化的技术栈构建，提供直观易用的用户界面和丰富的功能模块。系统支持多租户SaaS模式，具备完善的权限管理、日志审计、消息通知等企业级功能。

### 核心特性

- 🎨 **现代化UI设计** - 基于Element Plus的美观界面
- 🔐 **完善权限体系** - 支持RBAC权限模型和数据权限控制
- 🏢 **多租户支持** - 完整的SaaS多租户解决方案
- 📱 **响应式设计** - 适配PC、平板、手机等多种设备
- 🚀 **高性能** - 基于Vue 3 Composition API和Vite构建
- 🔧 **可扩展** - 模块化设计，易于扩展和维护

## 技术架构

### 前端技术栈

- **框架**: Vue 3.x (Composition API)
- **语言**: TypeScript 4.x
- **构建工具**: Vite 5.x
- **UI组件库**: Element Plus 2.x
- **图标**: @element-plus/icons-vue
- **样式**: SCSS/Sass
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **HTTP客户端**: Axios
- **工具库**: Lodash, Day.js

### 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── layouts/           # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
│       ├── member/        # 用户权限管理
│       ├── system/        # 系统管理
│       ├── monitor/       # 系统监控
│       └── ...
├── package.json
└── vite.config.ts
```

## 核心功能模块

### 1. 用户管理

**功能概述**
用户管理模块是系统的核心模块之一，负责管理系统中所有操作者的账户信息，包括用户的基本信息维护、状态管理和角色分配等功能。

**主要特性**
- ✅ 用户信息的增删改查操作
- ✅ 用户状态管理（启用/禁用）
- ✅ 用户角色分配和权限控制
- ✅ 批量操作支持
- ✅ 高级搜索和筛选功能
- ✅ 数据导入导出功能

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/member/user/index.vue
<template>
  <div class="member-user-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="searchForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增用户
      </el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="!selectedUsers.length">
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
    </div>

    <!-- 用户列表表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column prop="realName" label="真实姓名" />
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { userApi } from '@/api/member/user'

// 响应式数据定义
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  email: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const response = await userApi.getList({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    })
    userList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 删除用户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      { type: 'warning' }
    )
    await userApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  fetchUserList()
})
</script>
```

*API接口定义*
```typescript
// src/api/member/user.ts
import request from '@/utils/request'

export interface UserQuery {
  username?: string
  email?: string
  status?: number
  page: number
  size: number
}

export interface UserForm {
  id?: number
  username: string
  email: string
  phone: string
  realName: string
  department: string
  status: number
  password?: string
}

export const userApi = {
  // 获取用户列表
  getList: (params: UserQuery) => {
    return request.get('/api/member/user/list', { params })
  },
  
  // 创建用户
  create: (data: UserForm) => {
    return request.post('/api/member/user', data)
  },
  
  // 更新用户
  update: (id: number, data: UserForm) => {
    return request.put(`/api/member/user/${id}`, data)
  },
  
  // 删除用户
  delete: (id: number) => {
    return request.delete(`/api/member/user/${id}`)
  },
  
  // 批量删除用户
  batchDelete: (ids: number[]) => {
    return request.delete('/api/member/user/batch', { data: { ids } })
  }
}
```

**用户界面说明**

1. **搜索区域**: 提供用户名、邮箱、状态等多维度搜索条件
2. **操作按钮**: 新增用户、批量删除等快捷操作
3. **数据表格**: 展示用户列表，支持排序、筛选、选择等功能
4. **分页组件**: 支持页码跳转、页面大小调整等功能
5. **编辑对话框**: 用户信息的新增和编辑表单

**用户操作流程**

1. **查看用户列表**: 进入页面自动加载用户数据
2. **搜索用户**: 输入搜索条件，点击搜索按钮
3. **新增用户**: 点击"新增用户"按钮，填写用户信息并提交
4. **编辑用户**: 点击表格中的"编辑"按钮，修改用户信息
5. **删除用户**: 点击"删除"按钮，确认后删除用户
6. **批量删除**: 选择多个用户，点击"批量删除"按钮

**注意事项**

- 用户名必须唯一，系统会自动校验
- 删除用户前需要确认该用户没有关联的业务数据
- 管理员用户不能被删除或禁用
- 用户密码采用加密存储，前端不显示明文密码

---

### 2. 在线用户监控 ⭐️

**功能概述**
在线用户监控模块提供实时的用户活跃状态监控功能，管理员可以查看当前在线的用户列表，并支持强制用户下线的管理功能。这是系统安全管理的重要组成部分。

**主要特性**
- ✅ 实时显示当前在线用户列表
- ✅ 显示用户登录时间、IP地址、设备信息
- ✅ 支持管理员强制用户下线功能
- ✅ 在线用户数量统计和趋势分析
- ✅ 异常登录检测和预警
- ✅ 用户会话管理和超时控制

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/monitor/online-user/index.vue
<template>
  <div class="online-user-container">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ onlineStats.total }}</div>
              <div class="stat-label">在线用户总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ onlineStats.today }}</div>
              <div class="stat-label">今日登录用户</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ onlineStats.peak }}</div>
              <div class="stat-label">今日峰值</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-item">
              <div class="stat-value">{{ onlineStats.avgTime }}</div>
              <div class="stat-label">平均在线时长</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input v-model="searchForm.ipAddress" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 在线用户列表 -->
    <el-table
      v-loading="loading"
      :data="onlineUserList"
      stripe
      border
    >
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="realName" label="真实姓名" />
      <el-table-column prop="ipAddress" label="IP地址" />
      <el-table-column prop="location" label="登录地点" />
      <el-table-column prop="browser" label="浏览器" />
      <el-table-column prop="os" label="操作系统" />
      <el-table-column prop="loginTime" label="登录时间" />
      <el-table-column label="在线时长">
        <template #default="{ row }">
          <el-tag type="info">{{ formatDuration(row.onlineDuration) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button
            type="danger"
            size="small"
            @click="handleForceLogout(row)"
            :disabled="row.username === currentUser.username"
          >
            强制下线
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { onlineUserApi } from '@/api/monitor/online-user'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const currentUser = computed(() => userStore.userInfo)

// 响应式数据
const loading = ref(false)
const onlineUserList = ref([])
const onlineStats = ref({
  total: 0,
  today: 0,
  peak: 0,
  avgTime: '0分钟'
})

// 搜索表单
const searchForm = reactive({
  username: '',
  ipAddress: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 获取在线用户列表
const fetchOnlineUserList = async () => {
  loading.value = true
  try {
    const response = await onlineUserApi.getList({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    })
    onlineUserList.value = response.data.records
    pagination.total = response.data.total
    onlineStats.value = response.data.stats
  } catch (error) {
    ElMessage.error('获取在线用户列表失败')
  } finally {
    loading.value = false
  }
}

// 强制用户下线
const handleForceLogout = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要强制用户 "${row.username}" 下线吗？`,
      '强制下线确认',
      { type: 'warning' }
    )
    await onlineUserApi.forceLogout(row.sessionId)
    ElMessage.success('强制下线成功')
    fetchOnlineUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('强制下线失败')
    }
  }
}

// 格式化在线时长
const formatDuration = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}小时${mins}分钟`
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    'online': 'success',
    'idle': 'warning',
    'busy': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'online': '在线',
    'idle': '空闲',
    'busy': '忙碌'
  }
  return statusMap[status] || '未知'
}

// 自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchOnlineUserList()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  fetchOnlineUserList()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>
```

*API接口定义*
```typescript
// src/api/monitor/online-user.ts
import request from '@/utils/request'

export interface OnlineUserQuery {
  username?: string
  ipAddress?: string
  page: number
  size: number
}

export const onlineUserApi = {
  // 获取在线用户列表
  getList: (params: OnlineUserQuery) => {
    return request.get('/api/monitor/online-user/list', { params })
  },

  // 强制用户下线
  forceLogout: (sessionId: string) => {
    return request.post(`/api/monitor/online-user/force-logout/${sessionId}`)
  },

  // 获取在线用户统计
  getStats: () => {
    return request.get('/api/monitor/online-user/stats')
  }
}
```

**用户界面说明**

1. **统计卡片**: 显示在线用户总数、今日登录用户、峰值等关键指标
2. **搜索区域**: 支持按用户名、IP地址等条件搜索在线用户
3. **用户列表**: 展示详细的在线用户信息，包括登录时间、地点、设备等
4. **操作按钮**: 提供强制下线功能，管理员可以强制指定用户下线
5. **自动刷新**: 每30秒自动刷新一次数据，保持信息实时性

**用户操作流程**

1. **查看在线用户**: 进入页面自动显示当前在线用户列表
2. **搜索用户**: 输入搜索条件筛选特定用户
3. **强制下线**: 选择需要下线的用户，点击"强制下线"按钮
4. **查看详情**: 查看用户的登录信息、设备信息等详细数据
5. **监控异常**: 关注异常登录和可疑活动

**注意事项**

- 管理员不能强制自己下线
- 强制下线操作会记录到操作日志中
- 系统会自动检测异常登录行为
- 支持设置用户会话超时时间

---

### 3. 角色管理

**功能概述**
角色管理模块是权限控制系统的核心，负责定义和管理系统中的各种角色，并为角色分配相应的菜单权限和数据权限。支持基于RBAC模型的权限管理。

**主要特性**
- ✅ 角色信息的增删改查操作
- ✅ 角色与菜单权限的关联配置
- ✅ 基于机构的数据范围权限设置
- ✅ 角色权限继承和覆盖机制
- ✅ 权限模板和快速配置
- ✅ 角色使用情况统计和分析

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/member/role/index.vue
<template>
  <div class="role-container">
    <!-- 角色列表 -->
    <div class="role-list">
      <div class="list-header">
        <el-button type="primary" @click="handleAddRole">
          <el-icon><Plus /></el-icon>
          新增角色
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="roleList"
        @current-change="handleRoleSelect"
        highlight-current-row
      >
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="code" label="角色编码" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEditRole(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handleSetPermission(row)">
              权限配置
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteRole(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 权限配置区域 -->
    <div class="permission-config" v-if="selectedRole">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ selectedRole.name }} - 权限配置</span>
            <el-button type="primary" @click="handleSavePermission">
              保存权限
            </el-button>
          </div>
        </template>

        <el-tabs v-model="activeTab">
          <!-- 菜单权限 -->
          <el-tab-pane label="菜单权限" name="menu">
            <el-tree
              ref="menuTreeRef"
              :data="menuTree"
              :props="menuTreeProps"
              :default-checked-keys="checkedMenus"
              show-checkbox
              node-key="id"
              check-strictly
              @check="handleMenuCheck"
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.icon">
                    <component :is="data.icon" />
                  </el-icon>
                  <span>{{ data.title }}</span>
                  <el-tag v-if="data.type === 'button'" size="small" type="info">
                    按钮
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </el-tab-pane>

          <!-- 数据权限 -->
          <el-tab-pane label="数据权限" name="data">
            <el-form :model="dataPermissionForm" label-width="120px">
              <el-form-item label="数据范围">
                <el-radio-group v-model="dataPermissionForm.dataScope">
                  <el-radio label="1">全部数据权限</el-radio>
                  <el-radio label="2">自定义数据权限</el-radio>
                  <el-radio label="3">本部门数据权限</el-radio>
                  <el-radio label="4">本部门及以下数据权限</el-radio>
                  <el-radio label="5">仅本人数据权限</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                label="部门权限"
                v-if="dataPermissionForm.dataScope === '2'"
              >
                <el-tree
                  ref="deptTreeRef"
                  :data="deptTree"
                  :props="deptTreeProps"
                  :default-checked-keys="checkedDepts"
                  show-checkbox
                  node-key="id"
                  @check="handleDeptCheck"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleFormRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="roleForm.code" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="roleForm.sort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="roleForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitRole">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { roleApi } from '@/api/member/role'
import { menuApi } from '@/api/system/menu'
import { deptApi } from '@/api/system/dept'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const activeTab = ref('menu')
const roleList = ref([])
const selectedRole = ref(null)
const menuTree = ref([])
const deptTree = ref([])
const checkedMenus = ref([])
const checkedDepts = ref([])

// 表单数据
const roleForm = reactive({
  id: null,
  name: '',
  code: '',
  sort: 0,
  status: 1,
  description: ''
})

const dataPermissionForm = reactive({
  dataScope: '1',
  deptIds: []
})

// 树形组件配置
const menuTreeProps = {
  children: 'children',
  label: 'title'
}

const deptTreeProps = {
  children: 'children',
  label: 'name'
}

// 表单验证规则
const roleFormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' }
  ]
}

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true
  try {
    const response = await roleApi.getList()
    roleList.value = response.data
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 获取菜单树
const fetchMenuTree = async () => {
  try {
    const response = await menuApi.getTree()
    menuTree.value = response.data
  } catch (error) {
    ElMessage.error('获取菜单树失败')
  }
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const response = await deptApi.getTree()
    deptTree.value = response.data
  } catch (error) {
    ElMessage.error('获取部门树失败')
  }
}

// 角色选择
const handleRoleSelect = async (row: any) => {
  selectedRole.value = row
  if (row) {
    // 获取角色权限
    try {
      const response = await roleApi.getPermission(row.id)
      checkedMenus.value = response.data.menuIds
      checkedDepts.value = response.data.deptIds
      dataPermissionForm.dataScope = response.data.dataScope
    } catch (error) {
      ElMessage.error('获取角色权限失败')
    }
  }
}

// 保存权限
const handleSavePermission = async () => {
  try {
    const menuIds = menuTreeRef.value.getCheckedKeys()
    const deptIds = dataPermissionForm.dataScope === '2'
      ? deptTreeRef.value.getCheckedKeys()
      : []

    await roleApi.savePermission(selectedRole.value.id, {
      menuIds,
      deptIds,
      dataScope: dataPermissionForm.dataScope
    })

    ElMessage.success('权限保存成功')
  } catch (error) {
    ElMessage.error('权限保存失败')
  }
}

onMounted(() => {
  fetchRoleList()
  fetchMenuTree()
  fetchDeptTree()
})
</script>
```

**用户界面说明**

1. **角色列表**: 展示所有角色信息，支持选择角色进行权限配置
2. **权限配置区域**: 分为菜单权限和数据权限两个标签页
3. **菜单权限树**: 以树形结构展示系统菜单，支持勾选分配权限
4. **数据权限配置**: 设置角色的数据访问范围和部门权限
5. **角色编辑对话框**: 用于新增和编辑角色基本信息

**用户操作流程**

1. **查看角色列表**: 进入页面显示所有角色
2. **新增角色**: 点击"新增角色"按钮，填写角色信息
3. **编辑角色**: 点击"编辑"按钮修改角色信息
4. **配置权限**: 选择角色后在右侧配置菜单权限和数据权限
5. **保存权限**: 配置完成后点击"保存权限"按钮

**注意事项**

- 角色编码必须唯一，不能重复
- 删除角色前需要确认没有用户使用该角色
- 超级管理员角色不能被删除或修改权限
- 数据权限的设置会影响用户能够访问的数据范围

---

### 4. 菜单管理

**功能概述**
菜单管理模块负责维护系统的导航菜单结构，支持多级菜单的创建和管理，同时提供操作权限和按钮权限的精细化控制。采用本地缓存机制优化性能。

**主要特性**
- ✅ 系统菜单结构的树形管理
- ✅ 支持多级菜单嵌套
- ✅ 操作权限和按钮权限标识设置
- ✅ 菜单图标和路由配置
- ✅ 本地缓存机制优化性能
- ✅ 菜单排序和显示控制

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/system/menu/index.vue
<template>
  <div class="menu-container">
    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增菜单
      </el-button>
      <el-button type="success" @click="handleExpandAll">
        <el-icon><Expand /></el-icon>
        展开全部
      </el-button>
      <el-button type="info" @click="handleCollapseAll">
        <el-icon><Fold /></el-icon>
        折叠全部
      </el-button>
    </div>

    <!-- 菜单树表格 -->
    <el-table
      v-loading="loading"
      :data="menuList"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :default-expand-all="false"
      ref="menuTableRef"
    >
      <el-table-column prop="title" label="菜单名称" min-width="200">
        <template #default="{ row }">
          <span class="menu-title">
            <el-icon v-if="row.icon" class="menu-icon">
              <component :is="row.icon" />
            </el-icon>
            {{ row.title }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="80">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)">
            {{ getTypeText(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="path" label="路由地址" min-width="150" />
      <el-table-column prop="component" label="组件路径" min-width="150" />
      <el-table-column prop="permission" label="权限标识" min-width="150" />
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '显示' : '隐藏' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleAddChild(row)">
            新增
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 菜单编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="menuFormRef"
        :model="menuForm"
        :rules="menuFormRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上级菜单" prop="parentId">
              <el-tree-select
                v-model="menuForm.parentId"
                :data="menuTreeOptions"
                :props="{ value: 'id', label: 'title', children: 'children' }"
                placeholder="选择上级菜单"
                check-strictly
                :render-after-expand="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单类型" prop="type">
              <el-radio-group v-model="menuForm.type">
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="F">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="title">
              <el-input v-model="menuForm.title" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number v-model="menuForm.sort" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="menuForm.type !== 'F'">
          <el-col :span="12">
            <el-form-item label="菜单图标" prop="icon">
              <el-input v-model="menuForm.icon" placeholder="请选择图标">
                <template #append>
                  <el-button @click="handleSelectIcon">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路由地址" prop="path">
              <el-input v-model="menuForm.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="menuForm.type === 'C'">
          <el-col :span="12">
            <el-form-item label="组件路径" prop="component">
              <el-input v-model="menuForm.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路由参数" prop="query">
              <el-input v-model="menuForm.query" placeholder="请输入路由参数" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权限标识" prop="permission">
              <el-input v-model="menuForm.permission" placeholder="请输入权限标识" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单状态" prop="status">
              <el-radio-group v-model="menuForm.status">
                <el-radio :label="1">显示</el-radio>
                <el-radio :label="0">隐藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="menuForm.type !== 'F'">
          <el-col :span="12">
            <el-form-item label="是否外链" prop="isFrame">
              <el-radio-group v-model="menuForm.isFrame">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否缓存" prop="isCache">
              <el-radio-group v-model="menuForm.isCache">
                <el-radio :label="1">缓存</el-radio>
                <el-radio :label="0">不缓存</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 图标选择对话框 -->
    <IconSelector v-model="iconSelectorVisible" @select="handleIconSelect" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Expand, Fold } from '@element-plus/icons-vue'
import { menuApi } from '@/api/system/menu'
import IconSelector from '@/components/IconSelector.vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const iconSelectorVisible = ref(false)
const dialogTitle = ref('')
const menuList = ref([])
const menuTreeOptions = ref([])
const menuTableRef = ref()
const menuFormRef = ref()

// 表单数据
const menuForm = reactive({
  id: null,
  parentId: 0,
  title: '',
  type: 'M',
  icon: '',
  path: '',
  component: '',
  permission: '',
  sort: 0,
  status: 1,
  isFrame: 0,
  isCache: 1,
  query: ''
})

// 表单验证规则
const menuFormRules = {
  title: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入显示排序', trigger: 'blur' }
  ]
}

// 获取菜单列表
const fetchMenuList = async () => {
  loading.value = true
  try {
    const response = await menuApi.getList()
    menuList.value = response.data
    menuTreeOptions.value = [
      { id: 0, title: '主类目', children: response.data }
    ]
  } catch (error) {
    ElMessage.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap = {
    'M': 'info',
    'C': 'success',
    'F': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    'M': '目录',
    'C': '菜单',
    'F': '按钮'
  }
  return typeMap[type] || '未知'
}

// 新增菜单
const handleAdd = () => {
  dialogTitle.value = '新增菜单'
  resetMenuForm()
  dialogVisible.value = true
}

// 新增子菜单
const handleAddChild = (row: any) => {
  dialogTitle.value = '新增子菜单'
  resetMenuForm()
  menuForm.parentId = row.id
  dialogVisible.value = true
}

// 编辑菜单
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑菜单'
  Object.assign(menuForm, { ...row })
  dialogVisible.value = true
}

// 删除菜单
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 "${row.title}" 吗？`,
      '删除确认',
      { type: 'warning' }
    )
    await menuApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchMenuList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await menuFormRef.value.validate()

    if (menuForm.id) {
      await menuApi.update(menuForm.id, menuForm)
      ElMessage.success('更新成功')
    } else {
      await menuApi.create(menuForm)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchMenuList()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('操作失败')
    }
  }
}

// 重置表单
const resetMenuForm = () => {
  Object.assign(menuForm, {
    id: null,
    parentId: 0,
    title: '',
    type: 'M',
    icon: '',
    path: '',
    component: '',
    permission: '',
    sort: 0,
    status: 1,
    isFrame: 0,
    isCache: 1,
    query: ''
  })
}

onMounted(() => {
  fetchMenuList()
})
</script>
```

**用户界面说明**

1. **操作按钮区域**: 提供新增菜单、展开/折叠全部等快捷操作
2. **菜单树表格**: 以树形表格展示菜单层级结构和详细信息
3. **菜单编辑对话框**: 支持菜单的新增和编辑，包含完整的配置选项
4. **图标选择器**: 提供图标选择功能，支持预览和搜索
5. **权限配置**: 支持设置菜单的权限标识和访问控制

**用户操作流程**

1. **查看菜单结构**: 进入页面显示完整的菜单树结构
2. **新增菜单**: 点击"新增菜单"或"新增子菜单"按钮
3. **编辑菜单**: 点击表格中的"编辑"按钮修改菜单信息
4. **删除菜单**: 点击"删除"按钮移除菜单项
5. **配置权限**: 设置菜单的权限标识和访问控制

**注意事项**

- 删除菜单时会同时删除其所有子菜单
- 菜单路由地址必须唯一，不能重复
- 按钮类型的菜单不需要配置路由和组件
- 外链菜单会在新窗口中打开

---

### 7. 租户管理 🚀

**功能概述**
租户管理是系统的核心SaaS功能，支持多租户模式下的租户配置管理，实现完整的数据隔离和权限控制。每个租户拥有独立的数据空间和配置环境。

**主要特性**
- ✅ 租户信息的完整生命周期管理
- ✅ 租户数据完全隔离
- ✅ 租户级别的功能权限控制
- ✅ 租户套餐和计费管理
- ✅ 租户状态监控和管理
- ✅ 租户数据备份和恢复

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/system/tenant/index.vue
<template>
  <div class="tenant-container">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ tenantStats.total }}</div>
              <div class="stat-label">租户总数</div>
              <el-icon class="stat-icon"><Office /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value active">{{ tenantStats.active }}</div>
              <div class="stat-label">活跃租户</div>
              <el-icon class="stat-icon"><Check /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value expired">{{ tenantStats.expired }}</div>
              <div class="stat-label">已过期</div>
              <el-icon class="stat-icon"><Warning /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ tenantStats.thisMonth }}</div>
              <div class="stat-label">本月新增</div>
              <el-icon class="stat-icon"><Plus /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="租户名称">
          <el-input v-model="searchForm.name" placeholder="请输入租户名称" />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input v-model="searchForm.contactName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="正常" value="1" />
            <el-option label="停用" value="0" />
            <el-option label="过期" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增租户
      </el-button>
      <el-button type="success" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>

    <!-- 租户列表 -->
    <el-table
      v-loading="loading"
      :data="tenantList"
      stripe
      border
    >
      <el-table-column prop="name" label="租户名称" min-width="150" />
      <el-table-column prop="code" label="租户编码" width="120" />
      <el-table-column prop="contactName" label="联系人" width="100" />
      <el-table-column prop="contactPhone" label="联系电话" width="120" />
      <el-table-column prop="packageName" label="套餐" width="100">
        <template #default="{ row }">
          <el-tag type="success">{{ row.packageName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="expireTime" label="到期时间" width="120" />
      <el-table-column prop="userCount" label="用户数" width="80" />
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleViewDetail(row)">
            详情
          </el-button>
          <el-button type="warning" size="small" @click="handleRenew(row)">
            续费
          </el-button>
          <el-dropdown @command="(command) => handleMoreAction(command, row)">
            <el-button type="info" size="small">
              更多<el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="reset-password">重置密码</el-dropdown-item>
                <el-dropdown-item command="backup">数据备份</el-dropdown-item>
                <el-dropdown-item command="disable" v-if="row.status === 1">停用</el-dropdown-item>
                <el-dropdown-item command="enable" v-if="row.status === 0">启用</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 租户编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="tenantFormRef"
        :model="tenantForm"
        :rules="tenantFormRules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="租户名称" prop="name">
                  <el-input v-model="tenantForm.name" placeholder="请输入租户名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户编码" prop="code">
                  <el-input v-model="tenantForm.code" placeholder="请输入租户编码" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactName">
                  <el-input v-model="tenantForm.contactName" placeholder="请输入联系人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input v-model="tenantForm.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="tenantForm.email" placeholder="请输入邮箱" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户套餐" prop="packageId">
                  <el-select v-model="tenantForm.packageId" placeholder="请选择套餐">
                    <el-option
                      v-for="pkg in packageList"
                      :key="pkg.id"
                      :label="pkg.name"
                      :value="pkg.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="到期时间" prop="expireTime">
                  <el-date-picker
                    v-model="tenantForm.expireTime"
                    type="datetime"
                    placeholder="选择到期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户数限制" prop="userLimit">
                  <el-input-number v-model="tenantForm.userLimit" :min="1" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="tenantForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 管理员账号 -->
          <el-tab-pane label="管理员账号" name="admin">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="管理员账号" prop="adminUsername">
                  <el-input v-model="tenantForm.adminUsername" placeholder="请输入管理员账号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理员密码" prop="adminPassword">
                  <el-input
                    v-model="tenantForm.adminPassword"
                    type="password"
                    placeholder="请输入管理员密码"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="管理员姓名" prop="adminRealName">
                  <el-input v-model="tenantForm.adminRealName" placeholder="请输入管理员姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理员邮箱" prop="adminEmail">
                  <el-input v-model="tenantForm.adminEmail" placeholder="请输入管理员邮箱" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Download, Office, Check, Warning, ArrowDown
} from '@element-plus/icons-vue'
import { tenantApi } from '@/api/system/tenant'
import { packageApi } from '@/api/system/package'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const activeTab = ref('basic')
const tenantList = ref([])
const packageList = ref([])
const tenantFormRef = ref()

// 统计数据
const tenantStats = ref({
  total: 0,
  active: 0,
  expired: 0,
  thisMonth: 0
})

// 搜索表单
const searchForm = reactive({
  name: '',
  contactName: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 租户表单
const tenantForm = reactive({
  id: null,
  name: '',
  code: '',
  contactName: '',
  contactPhone: '',
  email: '',
  packageId: null,
  expireTime: '',
  userLimit: 10,
  remark: '',
  adminUsername: '',
  adminPassword: '',
  adminRealName: '',
  adminEmail: ''
})

// 表单验证规则
const tenantFormRules = {
  name: [
    { required: true, message: '请输入租户名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入租户编码', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  packageId: [
    { required: true, message: '请选择租户套餐', trigger: 'change' }
  ],
  adminUsername: [
    { required: true, message: '请输入管理员账号', trigger: 'blur' }
  ],
  adminPassword: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' }
  ]
}

// 获取租户列表
const fetchTenantList = async () => {
  loading.value = true
  try {
    const response = await tenantApi.getList({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    })
    tenantList.value = response.data.records
    pagination.total = response.data.total
    tenantStats.value = response.data.stats
  } catch (error) {
    ElMessage.error('获取租户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐列表
const fetchPackageList = async () => {
  try {
    const response = await packageApi.getList()
    packageList.value = response.data
  } catch (error) {
    ElMessage.error('获取套餐列表失败')
  }
}

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap = {
    1: 'success',
    0: 'danger',
    2: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    1: '正常',
    0: '停用',
    2: '过期'
  }
  return statusMap[status] || '未知'
}

onMounted(() => {
  fetchTenantList()
  fetchPackageList()
})
</script>
```

**用户界面说明**

1. **统计卡片**: 显示租户总数、活跃租户、过期租户等关键指标
2. **搜索筛选**: 支持按租户名称、联系人、状态等条件搜索
3. **租户列表**: 展示租户详细信息，包括套餐、到期时间、用户数等
4. **操作功能**: 提供编辑、续费、重置密码、数据备份等管理功能
5. **租户编辑**: 分为基本信息和管理员账号两个标签页

**用户操作流程**

1. **查看租户列表**: 进入页面显示所有租户信息和统计数据
2. **新增租户**: 点击"新增租户"按钮，填写租户信息和管理员账号
3. **编辑租户**: 点击"编辑"按钮修改租户信息
4. **续费管理**: 点击"续费"按钮为租户延长使用期限
5. **状态管理**: 通过更多操作菜单启用、停用或删除租户

**注意事项**

- 租户编码必须唯一，创建后不能修改
- 删除租户会同时删除该租户下的所有数据
- 租户过期后用户无法正常登录使用系统
- 管理员账号是租户的超级管理员，拥有该租户的所有权限

---

### 10. 短信管理 🚀

**功能概述**
短信管理模块提供完整的短信服务解决方案，支持多种短信渠道配置、模板管理和发送日志记录。集成主流短信平台，满足各种业务场景的短信发送需求。

**主要特性**
- ✅ 多短信渠道配置和管理
- ✅ 短信模板的创建和审核
- ✅ 短信发送日志和统计分析
- ✅ 支持阿里云、腾讯云等主流平台
- ✅ 短信发送频率限制和防刷机制
- ✅ 短信余额监控和预警

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/system/sms/channel/index.vue
<template>
  <div class="sms-channel-container">
    <!-- 渠道统计 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ channelStats.total }}</div>
              <div class="stat-label">渠道总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value active">{{ channelStats.active }}</div>
              <div class="stat-label">启用渠道</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ channelStats.todaySent }}</div>
              <div class="stat-label">今日发送</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ channelStats.monthSent }}</div>
              <div class="stat-label">本月发送</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增渠道
      </el-button>
      <el-button type="success" @click="handleTestSms">
        <el-icon><Message /></el-icon>
        发送测试短信
      </el-button>
    </div>

    <!-- 渠道列表 -->
    <el-table
      v-loading="loading"
      :data="channelList"
      stripe
      border
    >
      <el-table-column prop="name" label="渠道名称" />
      <el-table-column prop="code" label="渠道编码" />
      <el-table-column label="渠道类型">
        <template #default="{ row }">
          <el-tag :type="getChannelTypeColor(row.type)">
            {{ getChannelTypeName(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="余额" />
      <el-table-column prop="todayCount" label="今日发送" />
      <el-table-column prop="totalCount" label="总发送量" />
      <el-table-column prop="successRate" label="成功率">
        <template #default="{ row }">
          <el-progress
            :percentage="row.successRate"
            :color="getProgressColor(row.successRate)"
            :stroke-width="6"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleTest(row)">
            测试
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 渠道编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
    >
      <el-form
        ref="channelFormRef"
        :model="channelForm"
        :rules="channelFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="渠道名称" prop="name">
              <el-input v-model="channelForm.name" placeholder="请输入渠道名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="渠道编码" prop="code">
              <el-input v-model="channelForm.code" placeholder="请输入渠道编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="渠道类型" prop="type">
              <el-select v-model="channelForm.type" placeholder="请选择渠道类型">
                <el-option label="阿里云" value="aliyun" />
                <el-option label="腾讯云" value="tencent" />
                <el-option label="华为云" value="huawei" />
                <el-option label="七牛云" value="qiniu" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number v-model="channelForm.priority" :min="1" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 渠道配置 -->
        <el-divider content-position="left">渠道配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Access Key" prop="accessKey">
              <el-input v-model="channelForm.accessKey" placeholder="请输入Access Key" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Secret Key" prop="secretKey">
              <el-input
                v-model="channelForm.secretKey"
                type="password"
                placeholder="请输入Secret Key"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签名" prop="signature">
              <el-input v-model="channelForm.signature" placeholder="请输入短信签名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回调地址" prop="callbackUrl">
              <el-input v-model="channelForm.callbackUrl" placeholder="请输入回调地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 限制配置 -->
        <el-divider content-position="left">限制配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日发送限制" prop="dailyLimit">
              <el-input-number v-model="channelForm.dailyLimit" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="余额预警" prop="balanceWarning">
              <el-input-number v-model="channelForm.balanceWarning" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="channelForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 测试短信对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="发送测试短信"
      width="500px"
    >
      <el-form
        ref="testFormRef"
        :model="testForm"
        :rules="testFormRules"
        label-width="100px"
      >
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="testForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="短信内容" prop="content">
          <el-input
            v-model="testForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入短信内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="testDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSendTest">发送</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Message } from '@element-plus/icons-vue'
import { smsChannelApi } from '@/api/system/sms'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const testDialogVisible = ref(false)
const dialogTitle = ref('')
const channelList = ref([])
const channelFormRef = ref()
const testFormRef = ref()

// 统计数据
const channelStats = ref({
  total: 0,
  active: 0,
  todaySent: 0,
  monthSent: 0
})

// 渠道表单
const channelForm = reactive({
  id: null,
  name: '',
  code: '',
  type: '',
  priority: 1,
  accessKey: '',
  secretKey: '',
  signature: '',
  callbackUrl: '',
  dailyLimit: 1000,
  balanceWarning: 100,
  remark: ''
})

// 测试表单
const testForm = reactive({
  mobile: '',
  content: '这是一条测试短信，请忽略。'
})

// 表单验证规则
const channelFormRules = {
  name: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入渠道编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  accessKey: [
    { required: true, message: '请输入Access Key', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入Secret Key', trigger: 'blur' }
  ]
}

const testFormRules = {
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入短信内容', trigger: 'blur' }
  ]
}

// 获取渠道列表
const fetchChannelList = async () => {
  loading.value = true
  try {
    const response = await smsChannelApi.getList()
    channelList.value = response.data.records
    channelStats.value = response.data.stats
  } catch (error) {
    ElMessage.error('获取渠道列表失败')
  } finally {
    loading.value = false
  }
}

// 获取渠道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    'aliyun': '阿里云',
    'tencent': '腾讯云',
    'huawei': '华为云',
    'qiniu': '七牛云'
  }
  return typeMap[type] || type
}

// 获取渠道类型颜色
const getChannelTypeColor = (type: string) => {
  const colorMap = {
    'aliyun': 'success',
    'tencent': 'primary',
    'huawei': 'warning',
    'qiniu': 'info'
  }
  return colorMap[type] || 'default'
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 95) return '#67c23a'
  if (percentage >= 90) return '#e6a23c'
  return '#f56c6c'
}

// 状态变更
const handleStatusChange = async (row: any) => {
  try {
    await smsChannelApi.updateStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    row.status = row.status === 1 ? 0 : 1 // 回滚状态
  }
}

// 发送测试短信
const handleSendTest = async () => {
  try {
    await testFormRef.value.validate()
    await smsChannelApi.sendTest(testForm)
    ElMessage.success('测试短信发送成功')
    testDialogVisible.value = false
  } catch (error) {
    if (error !== false) {
      ElMessage.error('测试短信发送失败')
    }
  }
}

onMounted(() => {
  fetchChannelList()
})
</script>
```

**用户界面说明**

1. **统计卡片**: 显示渠道总数、启用渠道、今日发送量等关键指标
2. **渠道列表**: 展示所有短信渠道的详细信息和发送统计
3. **渠道配置**: 支持配置各种短信平台的API密钥和参数
4. **测试功能**: 提供测试短信发送功能，验证渠道配置
5. **状态管理**: 支持启用/禁用渠道，实时切换

**用户操作流程**

1. **查看渠道列表**: 进入页面显示所有配置的短信渠道
2. **新增渠道**: 点击"新增渠道"按钮，配置短信平台信息
3. **编辑渠道**: 点击"编辑"按钮修改渠道配置
4. **测试渠道**: 点击"测试"按钮发送测试短信验证配置
5. **管理状态**: 通过开关控制渠道的启用状态

**注意事项**

- 短信渠道的API密钥信息需要妥善保管
- 建议配置多个渠道作为备用，提高发送成功率
- 定期检查渠道余额，避免因余额不足影响业务
- 合理设置发送频率限制，防止短信被滥用

---

### 13. 操作日志 🚀

**功能概述**
操作日志模块提供系统操作的完整审计功能，自动记录用户的所有操作行为，支持日志查询、分析和导出。集成Swagger自动生成日志内容，确保操作的可追溯性。

**主要特性**
- ✅ 自动记录所有用户操作行为
- ✅ 集成Swagger自动生成日志内容
- ✅ 支持多维度日志查询和筛选
- ✅ 操作日志统计分析和可视化
- ✅ 日志数据导出和备份功能
- ✅ 异常操作检测和预警

**技术实现要点**

*Vue组件实现*
```typescript
// src/views/monitor/operation-log/index.vue
<template>
  <div class="operation-log-container">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ logStats.total }}</div>
              <div class="stat-label">总操作数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ logStats.today }}</div>
              <div class="stat-label">今日操作</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value success">{{ logStats.success }}</div>
              <div class="stat-label">成功操作</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value error">{{ logStats.error }}</div>
              <div class="stat-label">异常操作</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="操作用户">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="操作模块">
          <el-select v-model="searchForm.module" placeholder="请选择模块">
            <el-option label="用户管理" value="user" />
            <el-option label="角色管理" value="role" />
            <el-option label="菜单管理" value="menu" />
            <el-option label="租户管理" value="tenant" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.type" placeholder="请选择类型">
            <el-option label="查询" value="SELECT" />
            <el-option label="新增" value="INSERT" />
            <el-option label="修改" value="UPDATE" />
            <el-option label="删除" value="DELETE" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="成功" value="1" />
            <el-option label="失败" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作日志列表 -->
    <el-table
      v-loading="loading"
      :data="logList"
      stripe
      border
    >
      <el-table-column prop="username" label="操作用户" width="100" />
      <el-table-column prop="realName" label="用户姓名" width="100" />
      <el-table-column prop="module" label="操作模块" width="100" />
      <el-table-column prop="title" label="操作功能" min-width="150" />
      <el-table-column prop="type" label="操作类型" width="80">
        <template #default="{ row }">
          <el-tag :type="getTypeColor(row.type)">
            {{ row.type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="method" label="请求方法" width="80">
        <template #default="{ row }">
          <el-tag :type="getMethodColor(row.method)" size="small">
            {{ row.method }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="requestUrl" label="请求地址" min-width="200" show-overflow-tooltip />
      <el-table-column prop="userAgent" label="用户代理" min-width="150" show-overflow-tooltip />
      <el-table-column prop="ip" label="操作IP" width="120" />
      <el-table-column prop="location" label="操作地点" width="100" />
      <el-table-column label="操作状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="执行时长" width="100">
        <template #default="{ row }">
          <span :class="getDurationClass(row.duration)">
            {{ row.duration }}ms
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="操作时间" width="160" />
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleViewDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作日志详情"
      width="800px"
    >
      <el-descriptions :column="2" border v-if="selectedLog">
        <el-descriptions-item label="操作用户">
          {{ selectedLog.username }} ({{ selectedLog.realName }})
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ selectedLog.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="操作模块">
          {{ selectedLog.module }}
        </el-descriptions-item>
        <el-descriptions-item label="操作功能">
          {{ selectedLog.title }}
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="getTypeColor(selectedLog.type)">
            {{ selectedLog.type }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="selectedLog.status === 1 ? 'success' : 'danger'">
            {{ selectedLog.status === 1 ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="请求方法">
          <el-tag :type="getMethodColor(selectedLog.method)" size="small">
            {{ selectedLog.method }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行时长">
          <span :class="getDurationClass(selectedLog.duration)">
            {{ selectedLog.duration }}ms
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="请求地址" :span="2">
          {{ selectedLog.requestUrl }}
        </el-descriptions-item>
        <el-descriptions-item label="操作IP">
          {{ selectedLog.ip }}
        </el-descriptions-item>
        <el-descriptions-item label="操作地点">
          {{ selectedLog.location }}
        </el-descriptions-item>
        <el-descriptions-item label="用户代理" :span="2">
          {{ selectedLog.userAgent }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 请求参数 -->
      <el-divider content-position="left">请求参数</el-divider>
      <el-input
        v-model="selectedLog.requestParams"
        type="textarea"
        :rows="4"
        readonly
        placeholder="无请求参数"
      />

      <!-- 返回结果 -->
      <el-divider content-position="left">返回结果</el-divider>
      <el-input
        v-model="selectedLog.responseResult"
        type="textarea"
        :rows="6"
        readonly
        placeholder="无返回结果"
      />

      <!-- 异常信息 -->
      <el-divider content-position="left" v-if="selectedLog.status === 0">异常信息</el-divider>
      <el-input
        v-if="selectedLog.status === 0"
        v-model="selectedLog.errorMsg"
        type="textarea"
        :rows="4"
        readonly
        placeholder="无异常信息"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { operationLogApi } from '@/api/monitor/operation-log'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const logList = ref([])
const selectedLog = ref(null)

// 统计数据
const logStats = ref({
  total: 0,
  today: 0,
  success: 0,
  error: 0
})

// 搜索表单
const searchForm = reactive({
  username: '',
  module: '',
  type: '',
  status: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取操作日志列表
const fetchLogList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      startTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1],
      page: pagination.current,
      size: pagination.size
    }
    delete params.dateRange

    const response = await operationLogApi.getList(params)
    logList.value = response.data.records
    pagination.total = response.data.total
    logStats.value = response.data.stats
  } catch (error) {
    ElMessage.error('获取操作日志失败')
  } finally {
    loading.value = false
  }
}

// 获取操作类型颜色
const getTypeColor = (type: string) => {
  const colorMap = {
    'SELECT': 'info',
    'INSERT': 'success',
    'UPDATE': 'warning',
    'DELETE': 'danger'
  }
  return colorMap[type] || 'default'
}

// 获取请求方法颜色
const getMethodColor = (method: string) => {
  const colorMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return colorMap[method] || 'info'
}

// 获取执行时长样式
const getDurationClass = (duration: number) => {
  if (duration > 3000) return 'duration-slow'
  if (duration > 1000) return 'duration-normal'
  return 'duration-fast'
}

// 查看详情
const handleViewDetail = (row: any) => {
  selectedLog.value = row
  detailDialogVisible.value = true
}

// 导出日志
const handleExport = async () => {
  try {
    const params = {
      ...searchForm,
      startTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1]
    }
    delete params.dateRange

    await operationLogApi.export(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

onMounted(() => {
  fetchLogList()
})
</script>

<style scoped lang="scss">
.duration-fast {
  color: #67c23a;
}

.duration-normal {
  color: #e6a23c;
}

.duration-slow {
  color: #f56c6c;
}

.stat-value {
  &.success {
    color: #67c23a;
  }

  &.error {
    color: #f56c6c;
  }
}
</style>
```

**用户界面说明**

1. **统计卡片**: 显示总操作数、今日操作、成功/异常操作等关键指标
2. **高级搜索**: 支持按用户、模块、类型、状态、时间等多维度搜索
3. **日志列表**: 展示详细的操作日志信息，包括执行时长、IP地址等
4. **日志详情**: 提供完整的操作详情查看，包括请求参数和返回结果
5. **数据导出**: 支持按条件导出操作日志数据

**用户操作流程**

1. **查看日志列表**: 进入页面显示最新的操作日志
2. **搜索日志**: 使用搜索条件筛选特定的操作记录
3. **查看详情**: 点击"详情"按钮查看完整的操作信息
4. **导出数据**: 点击"导出"按钮下载日志数据
5. **分析统计**: 通过统计卡片了解系统操作情况

**注意事项**

- 操作日志会自动记录，无需手动干预
- 日志数据量较大时建议使用时间范围筛选
- 敏感操作的日志会特别标记和保护
- 定期清理过期日志数据，避免占用过多存储空间

---

## 开发指南

### 组件开发规范

**1. 组件命名**
- 使用 PascalCase 命名组件文件
- 组件名称应该具有描述性
- 业务组件放在对应的业务模块目录下

**2. 代码结构**
```typescript
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 类型定义
interface FormData {
  // 定义接口
}

// 响应式数据
const loading = ref(false)
const formData = reactive<FormData>({})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法定义
const handleSubmit = async () => {
  // 处理逻辑
}
</script>

<style scoped lang="scss">
// 样式定义
</style>
```

**3. API调用规范**
```typescript
// API接口定义
export const userApi = {
  getList: (params: UserQuery) => {
    return request.get('/api/user/list', { params })
  },

  create: (data: UserForm) => {
    return request.post('/api/user', data)
  }
}

// 组件中使用
const fetchUserList = async () => {
  loading.value = true
  try {
    const response = await userApi.getList(searchForm)
    userList.value = response.data.records
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}
```

### 样式开发规范

**1. 样式组织**
```scss
.component-container {
  padding: 20px;

  .header-section {
    margin-bottom: 20px;

    .title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .content-section {
    // 内容样式
  }
}
```

**2. 响应式设计**
```scss
// 使用Element Plus的断点
@media (max-width: 768px) {
  .component-container {
    padding: 10px;
  }
}
```

### 状态管理规范

**1. Pinia Store定义**
```typescript
// stores/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  const permissions = ref([])

  const setUserInfo = (info: any) => {
    userInfo.value = info
  }

  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }

  return {
    userInfo,
    permissions,
    setUserInfo,
    hasPermission
  }
})
```

**2. 组件中使用**
```typescript
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const hasEditPermission = computed(() =>
  userStore.hasPermission('user:edit')
)
```

## 部署说明

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0 或 pnpm >= 8.0.0

### 构建部署
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 环境配置
```env
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=VisThink ERP
VITE_APP_VERSION=1.0.0
```

### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/visthink-erp;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
