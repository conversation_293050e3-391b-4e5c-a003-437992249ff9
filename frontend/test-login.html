<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>VisThink ERP 登录测试</h1>
    
    <div class="form-group">
        <label for="apiUrl">API地址:</label>
        <input type="text" id="apiUrl" value="http://localhost:8081/api/auth/login">
    </div>
    
    <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" value="testuser002">
    </div>
    
    <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" value="Test123456">
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" id="rememberMe"> 记住我
        </label>
    </div>
    
    <button onclick="testLogin()">测试登录</button>
    <button onclick="testCurl()">生成 cURL 命令</button>
    <button onclick="clearResult()">清除结果</button>
    
    <div id="result"></div>

    <script>
        async function testLogin() {
            const apiUrl = document.getElementById('apiUrl').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            const loginData = {
                username: username,
                password: password,
                rememberMe: rememberMe,
                deviceInfo: getDeviceInfo(),
                ipAddress: "127.0.0.1"
            };
            
            showResult('发送登录请求...', 'info');
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData,
                    requestData: loginData
                };
                
                if (response.ok) {
                    showResult('登录成功!\n\n' + JSON.stringify(result, null, 2), 'success');
                } else {
                    showResult('登录失败!\n\n' + JSON.stringify(result, null, 2), 'error');
                }
                
            } catch (error) {
                showResult('请求失败: ' + error.message + '\n\n请求数据:\n' + JSON.stringify(loginData, null, 2), 'error');
            }
        }
        
        function testCurl() {
            const apiUrl = document.getElementById('apiUrl').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            const loginData = {
                username: username,
                password: password,
                rememberMe: rememberMe,
                deviceInfo: getDeviceInfo(),
                ipAddress: "127.0.0.1"
            };
            
            const curlCommand = `curl -X 'POST' \\
  '${apiUrl}' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/json' \\
  -d '${JSON.stringify(loginData, null, 2)}'`;
            
            showResult('cURL 命令:\n\n' + curlCommand, 'info');
        }
        
        function getDeviceInfo() {
            return navigator.platform + ' - ' + navigator.userAgent.substring(0, 100);
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
        }
        
        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }
    </script>
</body>
</html>
