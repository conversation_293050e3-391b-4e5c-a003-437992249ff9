# VisThink ERP 前端项目

基于 Vue 3 + TypeScript + Vite + Ant Design Vue 的多租户电商 ERP 管理系统前端。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.5+
- **构建工具**: Vite 5.3+
- **UI 组件库**: Ant Design Vue 4.2+
- **路由**: Vue Router 4.4+
- **状态管理**: Pinia 2.1+
- **国际化**: Vue I18n 9.13+
- **HTTP 客户端**: Axios 1.7+
- **样式**: Less + CSS Modules
- **工具库**: Lodash-es, Day.js, VueUse

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   ├── assets/            # 资源文件
│   │   ├── icons/         # 图标
│   │   └── images/        # 图片
│   ├── components/        # 组件
│   │   ├── common/        # 通用组件
│   │   └── business/      # 业务组件
│   ├── layouts/           # 布局组件
│   ├── locales/           # 国际化
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── types/             # 类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── dashboard/     # 仪表盘
│   │   ├── member/        # 用户管理
│   │   ├── product/       # 商品管理
│   │   ├── inventory/     # 库存管理
│   │   ├── order/         # 订单管理
│   │   ├── platform/      # 平台集成
│   │   └── system/        # 系统管理
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── .env                   # 环境变量
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── index.html             # HTML 模板
├── package.json           # 依赖配置
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目文档
```

## 🛠️ 开发环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0 (推荐) 或 npm >= 9.0.0

## 📦 安装依赖

```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

## 🚀 启动开发服务器

```bash
# 启动开发服务器
pnpm dev

# 或
npm run dev
```

访问 http://localhost:3000

## 🏗️ 构建项目

```bash
# 构建生产版本
pnpm build

# 或
npm run build
```

## 🔧 其他命令

```bash
# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 样式检查
pnpm lint:stylelint

# 预览构建结果
pnpm preview

# 清理缓存
pnpm clean

# 重新安装依赖
pnpm reinstall
```

## 🌐 环境配置

### 开发环境 (.env.development)
```env
VITE_API_BASE_URL=http://localhost:8080
VITE_MEMBER_API_URL=http://localhost:8081
VITE_PRODUCT_API_URL=http://localhost:8082
VITE_INVENTORY_API_URL=http://localhost:8083
VITE_ORDER_API_URL=http://localhost:8084
```

### 生产环境 (.env.production)
```env
VITE_API_BASE_URL=https://api.visthink-erp.com
VITE_MEMBER_API_URL=https://member-api.visthink-erp.com
VITE_PRODUCT_API_URL=https://product-api.visthink-erp.com
VITE_INVENTORY_API_URL=https://inventory-api.visthink-erp.com
VITE_ORDER_API_URL=https://order-api.visthink-erp.com
```

## 🔗 API 代理配置

开发环境下，Vite 会自动代理 API 请求到对应的微服务：

- `/api/*` → `http://localhost:8080`
- `/member-api/*` → `http://localhost:8081`
- `/product-api/*` → `http://localhost:8082`
- `/inventory-api/*` → `http://localhost:8083`
- `/order-api/*` → `http://localhost:8084`

## 📱 功能模块

### 🏠 仪表盘
- 数据统计概览
- 销售趋势图表
- 快捷操作入口
- 最近订单列表
- 库存预警提醒

### 👥 用户管理
- 用户列表管理
- 角色权限配置
- 权限分配管理

### 🛍️ 商品管理
- 商品信息管理
- 商品分类管理
- 品牌信息管理

### 📦 库存管理
- 库存查询统计
- 库存预警管理
- 库存变动日志

### 📋 订单管理
- 订单列表查询
- 订单详情查看
- 订单状态跟踪

### 🔗 平台集成
- 电商平台配置
- 数据同步管理

### ⚙️ 系统管理
- 租户信息管理
- 系统配置管理

## 🎨 UI 设计规范

### 色彩规范
- 主色调: #1890ff (蓝色)
- 成功色: #52c41a (绿色)
- 警告色: #faad14 (橙色)
- 错误色: #f5222d (红色)
- 信息色: #1890ff (蓝色)

### 字体规范
- 基础字号: 14px
- 大字号: 16px
- 小字号: 12px
- 行高: 1.5715

### 间距规范
- 基础间距: 8px, 12px, 16px, 24px, 32px
- 组件内边距: 16px
- 页面边距: 24px

## 🌍 国际化支持

项目支持中文和英文两种语言：

- 中文 (zh-CN) - 默认语言
- 英文 (en-US)

语言文件位于 `src/locales/` 目录下。

## 📝 开发规范

### 组件命名
- 使用 PascalCase 命名组件文件
- 组件名称应该具有描述性
- 业务组件放在 `components/business/`
- 通用组件放在 `components/common/`

### 代码风格
- 使用 TypeScript 进行类型检查
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 使用 Composition API 编写组件

### Git 提交规范
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **项目负责人**: VisThink Team
- **前端开发**: Vue.js 开发团队
- **UI 设计**: 设计团队

## 📞 联系我们

如有问题或建议，请联系：

- 邮箱: <EMAIL>
- 官网: https://www.visthink.com
- 文档: https://docs.visthink.com
