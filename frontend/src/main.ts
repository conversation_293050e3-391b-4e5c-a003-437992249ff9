import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import Antd from 'ant-design-vue'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'
import { setupRouterGuards } from './router/guards'

// 样式导入
import 'ant-design-vue/dist/reset.css'
import './styles/index.less'

// 国际化配置
import { messages } from './locales'

// 创建应用实例
const app = createApp(App)

// 创建 Pinia 状态管理
const pinia = createPinia()

// 创建国际化实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages,
  globalInjection: true
})

// 注册插件
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(Antd)

// 设置路由守卫（在应用挂载前）
setupRouterGuards(router)

// 初始化认证状态
const initAuth = async () => {
  try {
    // 获取认证Store实例
    const authStore = useAuthStore()
    
    // 恢复认证状态（从localStorage）
    authStore.restoreAuthState()
    
    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.isLoggedIn && !authStore.userInfo) {
      console.log('检测到token，正在获取用户信息...')
      const success = await authStore.fetchUserInfo()
      if (!success) {
        console.warn('获取用户信息失败，清除认证状态')
        authStore.clearAuthInfo()
      }
    }
    
    console.log('认证状态初始化完成:', {
      isLoggedIn: authStore.isLoggedIn,
      user: authStore.userDisplayName,
      tenantId: authStore.userInfo?.tenantId
    })
    
  } catch (error) {
    console.error('认证状态初始化失败:', error)
  }
}

// 挂载应用
const mountApp = async () => {
  try {
    // 初始化认证状态
    await initAuth()
    
    // 挂载应用
    app.mount('#app')
    
    console.log('VisThink ERP 应用启动成功')
    
  } catch (error) {
    console.error('应用启动失败:', error)
    // 即使初始化失败也要挂载应用
    app.mount('#app')
  }
}

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err)
    console.error('Component:', vm)
    console.error('Info:', info)
  }

  // 性能监控
  app.config.performance = true
  
  // 添加全局调试方法
  window.__VUE_APP__ = app
  window.__AUTH_STORE__ = () => useAuthStore()
}

// 全局属性
app.config.globalProperties.$VITE_APP_TITLE = import.meta.env.VITE_APP_TITLE

// 启动应用
mountApp()

// 全局类型声明
declare global {
  interface Window {
    __VUE_APP__?: any
    __AUTH_STORE__?: () => any
  }
}
