// 主题色彩
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 中性色
@white: #ffffff;
@black: #000000;
@gray-1: #fafafa;
@gray-2: #f5f5f5;
@gray-3: #f0f0f0;
@gray-4: #d9d9d9;
@gray-5: #bfbfbf;
@gray-6: #8c8c8c;
@gray-7: #595959;
@gray-8: #434343;
@gray-9: #262626;
@gray-10: #1f1f1f;

// 文本颜色
@text-color: rgba(0, 0, 0, 0.85);
@text-color-secondary: rgba(0, 0, 0, 0.65);
@text-color-inverse: @white;
@text-color-dark: rgba(255, 255, 255, 0.85);

// 背景色
@body-background: #f0f2f5;
@component-background: @white;
@background-color-light: #fafafa;
@background-color-base: #f5f5f5;

// 边框
@border-color-base: #d9d9d9;
@border-color-split: #f0f0f0;
@border-width-base: 1px;
@border-style-base: solid;

// 圆角
@border-radius-base: 6px;
@border-radius-sm: 2px;
@border-radius-lg: 8px;

// 阴影
@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
@box-shadow-card: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);

// 字体
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;
@font-weight-base: 400;
@font-weight-bold: 500;

// 行高
@line-height-base: 1.5715;

// 间距
@padding-xs: 8px;
@padding-sm: 12px;
@padding-md: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

@margin-xs: 8px;
@margin-sm: 12px;
@margin-md: 16px;
@margin-lg: 24px;
@margin-xl: 32px;

// 布局
@layout-header-height: 64px;
@layout-footer-height: 48px;
@layout-sider-width: 200px;
@layout-sider-collapsed-width: 80px;

// 动画
@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);
@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);
@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);
@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);
@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);

// 动画持续时间
@animation-duration-slow: 0.3s;
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s;

// Z-index
@zindex-base: 0;
@zindex-affix: 10;
@zindex-back-top: 10;
@zindex-picker-panel: 10;
@zindex-popup-close: 10;
@zindex-modal: 1000;
@zindex-modal-mask: 1000;
@zindex-message: 1010;
@zindex-notification: 1010;
@zindex-tooltip: 1060;
@zindex-dropdown: 1050;

// 屏幕断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 表格
@table-header-bg: @background-color-light;
@table-header-color: @text-color;
@table-header-sort-bg: @background-color-base;
@table-body-sort-bg: rgba(0, 0, 0, 0.01);
@table-row-hover-bg: @background-color-light;
@table-selected-row-color: inherit;
@table-selected-row-bg: #ffeaa7;
@table-body-selected-sort-bg: rgba(0, 0, 0, 0.01);
@table-selected-row-hover-bg: darken(@table-selected-row-bg, 2%);
@table-expanded-row-bg: #fbfbfb;
@table-padding-vertical: 16px;
@table-padding-horizontal: 16px;
@table-padding-vertical-md: 12px;
@table-padding-horizontal-md: 8px;
@table-padding-vertical-sm: 8px;
@table-padding-horizontal-sm: 8px;
@table-border-radius-base: @border-radius-base;
@table-footer-bg: @background-color-light;
@table-footer-color: @text-color;
@table-header-bg-sm: @table-header-bg;
@table-font-size: @font-size-base;
@table-font-size-md: @table-font-size;
@table-font-size-sm: @table-font-size;
