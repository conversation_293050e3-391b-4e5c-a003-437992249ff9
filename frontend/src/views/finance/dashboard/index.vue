<!--
  财务管理看板
  @description 财务数据统计、交易记录、返佣管理、财务报表
-->
<template>
  <div class="finance-dashboard">
    <!-- 财务概览卡片 -->
    <div class="finance-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic
              title="今日收入"
              :value="financeOverview.todayIncome"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#3f8600' }"
            >
              <template #suffix>
                <span class="trend-indicator">
                  <CaretUpOutlined v-if="financeOverview.incomeGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(financeOverview.incomeGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic
              title="今日支出"
              :value="financeOverview.todayExpense"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#cf1322' }"
            >
              <template #suffix>
                <span class="trend-indicator">
                  <CaretUpOutlined v-if="financeOverview.expenseGrowth > 0" style="color: #cf1322" />
                  <CaretDownOutlined v-else style="color: #3f8600" />
                  {{ Math.abs(financeOverview.expenseGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic
              title="净利润"
              :value="financeOverview.netProfit"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <span class="trend-indicator">
                  <CaretUpOutlined v-if="financeOverview.profitGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(financeOverview.profitGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="overview-card">
            <a-statistic
              title="待处理退款"
              :value="financeOverview.pendingRefunds"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <a-button type="link" size="small" @click="handleViewRefunds">
                  查看详情
                </a-button>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 收支趋势图 -->
        <a-col :span="12">
          <a-card title="收支趋势" :bordered="false">
            <template #extra>
              <a-radio-group v-model:value="incomeTrendPeriod" @change="loadIncomeTrend">
                <a-radio-button value="7">近7天</a-radio-button>
                <a-radio-button value="30">近30天</a-radio-button>
                <a-radio-button value="90">近90天</a-radio-button>
              </a-radio-group>
            </template>
            <EChartsComponent
              ref="incomeTrendChartRef"
              :option="incomeTrendOption"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 支付方式分布 -->
        <a-col :span="12">
          <a-card title="支付方式分布" :bordered="false">
            <EChartsComponent
              ref="paymentMethodChartRef"
              :option="paymentMethodOption"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- 利润分析 -->
        <a-col :span="12">
          <a-card title="利润分析" :bordered="false">
            <EChartsComponent
              ref="profitAnalysisChartRef"
              :option="profitAnalysisOption"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 佣金统计 -->
        <a-col :span="12">
          <a-card title="佣金统计" :bordered="false">
            <template #extra>
              <a-select v-model:value="brokerageStatsType" @change="loadBrokerageStats" style="width: 120px">
                <a-select-option value="daily">按日统计</a-select-option>
                <a-select-option value="monthly">按月统计</a-select-option>
              </a-select>
            </template>
            <EChartsComponent
              ref="brokerageStatsChartRef"
              :option="brokerageStatsOption"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <a-card title="快捷操作" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small" class="action-card" @click="handleViewTransactions">
              <div class="action-content">
                <TransactionOutlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">交易记录</div>
                  <div class="action-desc">查看所有交易记录</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="action-card" @click="handleViewRefunds">
              <div class="action-content">
                <UndoOutlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">退款管理</div>
                  <div class="action-desc">处理退款申请</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="action-card" @click="handleViewBrokerage">
              <div class="action-content">
                <GiftOutlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">佣金管理</div>
                  <div class="action-desc">分销佣金发放</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="action-card" @click="handleViewReconciliation">
              <div class="action-content">
                <ReconciliationOutlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">对账管理</div>
                  <div class="action-desc">第三方平台对账</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 实时数据 -->
    <div class="realtime-data">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="最近交易" :bordered="false">
            <template #extra>
              <a-badge :count="recentTransactions.length" :overflow-count="99">
                <ReloadOutlined @click="loadRecentTransactions" />
              </a-badge>
            </template>
            <a-list
              :data-source="recentTransactions"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <span>{{ getTransactionTypeText(item.type) }}</span>
                      <a-tag :color="getTransactionStatusColor(item.status)" style="margin-left: 8px">
                        {{ getTransactionStatusText(item.status) }}
                      </a-tag>
                    </template>
                    <template #description>
                      <div>
                        <span>金额：¥{{ item.amount }}</span>
                        <span style="margin-left: 16px">时间：{{ item.createTime }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="异常交易" :bordered="false">
            <template #extra>
              <a-badge :count="abnormalTransactions.length" status="error">
                <ReloadOutlined @click="loadAbnormalTransactions" />
              </a-badge>
            </template>
            <a-list
              :data-source="abnormalTransactions"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-button type="link" size="small" @click="handleAbnormalTransaction(item)">
                      处理
                    </a-button>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <span>{{ item.transactionNo }}</span>
                      <a-tag color="red" style="margin-left: 8px">异常</a-tag>
                    </template>
                    <template #description>
                      <div>
                        <span>金额：¥{{ item.amount }}</span>
                        <span style="margin-left: 16px">{{ item.description }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 异常交易处理弹窗 -->
    <AbnormalTransactionModal
      v-model:open="abnormalModalVisible"
      :transaction="currentAbnormalTransaction"
      @success="handleAbnormalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  CaretUpOutlined,
  CaretDownOutlined,
  ReloadOutlined,
  TransactionOutlined,
  UndoOutlined,
  GiftOutlined,
  ReconciliationOutlined
} from '@ant-design/icons-vue'
import EChartsComponent from '@/components/ECharts/EChartsComponent.vue'
import AbnormalTransactionModal from './components/AbnormalTransactionModal.vue'
import type { EChartsOption } from 'echarts'

// 路由
const router = useRouter()

// 响应式数据
const financeOverview = reactive({
  todayIncome: 125680.50,
  incomeGrowth: 12.5,
  todayExpense: 45230.80,
  expenseGrowth: 8.3,
  netProfit: 80449.70,
  profitGrowth: 15.2,
  pendingRefunds: 23
})

const incomeTrendPeriod = ref('30')
const brokerageStatsType = ref('daily')
const recentTransactions = ref<any[]>([])
const abnormalTransactions = ref<any[]>([])
const abnormalModalVisible = ref(false)
const currentAbnormalTransaction = ref<any>(null)

// 图表引用
const incomeTrendChartRef = ref()
const paymentMethodChartRef = ref()
const profitAnalysisChartRef = ref()
const brokerageStatsChartRef = ref()

// 图表配置
const incomeTrendOption = ref<EChartsOption>({})
const paymentMethodOption = ref<EChartsOption>({})
const profitAnalysisOption = ref<EChartsOption>({})
const brokerageStatsOption = ref<EChartsOption>({})

// 定时器
let realtimeTimer: NodeJS.Timeout | null = null

/**
 * 获取交易类型文本
 */
const getTransactionTypeText = (type: number): string => {
  const textMap: Record<number, string> = {
    1: '订单支付',
    2: '余额充值',
    3: '积分消费',
    4: '退款',
    5: '佣金发放',
    6: '提现'
  }
  return textMap[type] || '未知'
}

/**
 * 获取交易状态颜色
 */
const getTransactionStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    1: 'green',   // 成功
    2: 'red',     // 失败
    3: 'orange'   // 处理中
  }
  return colorMap[status] || 'default'
}

/**
 * 获取交易状态文本
 */
const getTransactionStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    1: '成功',
    2: '失败',
    3: '处理中'
  }
  return textMap[status] || '未知'
}

/**
 * 加载收支趋势数据
 */
const loadIncomeTrend = () => {
  const days = parseInt(incomeTrendPeriod.value)
  const dates = []
  const incomeData = []
  const expenseData = []
  const profitData = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    
    const income = Math.floor(Math.random() * 50000) + 80000
    const expense = Math.floor(Math.random() * 30000) + 20000
    incomeData.push(income)
    expenseData.push(expense)
    profitData.push(income - expense)
  }

  incomeTrendOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['收入', '支出', '利润']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '金额(元)'
    },
    series: [
      {
        name: '收入',
        type: 'line',
        data: incomeData,
        smooth: true,
        itemStyle: {
          color: '#52c41a'
        }
      },
      {
        name: '支出',
        type: 'line',
        data: expenseData,
        smooth: true,
        itemStyle: {
          color: '#f5222d'
        }
      },
      {
        name: '利润',
        type: 'bar',
        data: profitData,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
}

/**
 * 加载支付方式分布
 */
const loadPaymentMethodStats = () => {
  paymentMethodOption.value = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '支付方式',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '微信支付' },
          { value: 735, name: '支付宝' },
          { value: 580, name: '银行卡' },
          { value: 484, name: '余额支付' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

/**
 * 加载利润分析
 */
const loadProfitAnalysis = () => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月']
  const grossProfitData = [2340, 1980, 1654, 1432, 1234, 1567]
  const netProfitData = [1890, 1580, 1254, 1032, 934, 1267]

  profitAnalysisOption.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['毛利润', '净利润']
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      name: '利润(万元)'
    },
    series: [
      {
        name: '毛利润',
        type: 'bar',
        data: grossProfitData,
        itemStyle: {
          color: '#52c41a'
        }
      },
      {
        name: '净利润',
        type: 'bar',
        data: netProfitData,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
}

/**
 * 加载佣金统计
 */
const loadBrokerageStats = () => {
  const isDaily = brokerageStatsType.value === 'daily'
  const xAxisData = isDaily 
    ? ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    : ['1月', '2月', '3月', '4月', '5月', '6月']
  
  const brokerageData = isDaily
    ? [1200, 1500, 1800, 1300, 1600, 2100, 1900]
    : [15000, 18000, 22000, 19000, 21000, 25000]

  brokerageStatsOption.value = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      name: '佣金(元)'
    },
    series: [
      {
        name: '佣金支出',
        type: 'line',
        data: brokerageData,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ]
          }
        },
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
}

/**
 * 加载最近交易
 */
const loadRecentTransactions = () => {
  recentTransactions.value = [
    {
      id: 1,
      type: 1,
      amount: 299.00,
      status: 1,
      createTime: '10:30:25'
    },
    {
      id: 2,
      type: 4,
      amount: 156.80,
      status: 1,
      createTime: '10:28:15'
    },
    {
      id: 3,
      type: 2,
      amount: 1000.00,
      status: 1,
      createTime: '10:25:42'
    }
  ]
}

/**
 * 加载异常交易
 */
const loadAbnormalTransactions = () => {
  abnormalTransactions.value = [
    {
      id: 1,
      transactionNo: 'T202401010001',
      amount: 299.00,
      description: '支付超时未回调'
    },
    {
      id: 2,
      transactionNo: 'T202401010002',
      amount: 156.80,
      description: '重复支付'
    }
  ]
}

/**
 * 处理异常交易
 */
const handleAbnormalTransaction = (transaction: any) => {
  currentAbnormalTransaction.value = transaction
  abnormalModalVisible.value = true
}

/**
 * 异常交易处理成功
 */
const handleAbnormalSuccess = () => {
  loadAbnormalTransactions()
}

/**
 * 查看交易记录
 */
const handleViewTransactions = () => {
  router.push('/finance/transaction')
}

/**
 * 查看退款管理
 */
const handleViewRefunds = () => {
  router.push('/finance/refund')
}

/**
 * 查看佣金管理
 */
const handleViewBrokerage = () => {
  router.push('/finance/brokerage')
}

/**
 * 查看对账管理
 */
const handleViewReconciliation = () => {
  router.push('/finance/reconciliation')
}

/**
 * 启动实时数据更新
 */
const startRealtimeUpdate = () => {
  realtimeTimer = setInterval(() => {
    loadRecentTransactions()
    // 更新财务概览数据
    financeOverview.todayIncome += Math.random() * 1000
    financeOverview.todayExpense += Math.random() * 500
    financeOverview.netProfit = financeOverview.todayIncome - financeOverview.todayExpense
  }, 30000) // 30秒更新一次
}

/**
 * 停止实时数据更新
 */
const stopRealtimeUpdate = () => {
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
    realtimeTimer = null
  }
}

// 页面加载时初始化数据
onMounted(() => {
  loadIncomeTrend()
  loadPaymentMethodStats()
  loadProfitAnalysis()
  loadBrokerageStats()
  loadRecentTransactions()
  loadAbnormalTransactions()
  startRealtimeUpdate()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopRealtimeUpdate()
})
</script>

<style scoped lang="less">
.finance-dashboard {
  .finance-overview {
    margin-bottom: 24px;
    
    .overview-card {
      text-align: center;
      
      .trend-indicator {
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
  
  .charts-section {
    margin-bottom: 24px;
  }
  
  .quick-actions {
    margin-bottom: 24px;
    
    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .action-content {
        display: flex;
        align-items: center;
        
        .action-icon {
          font-size: 24px;
          color: #1890ff;
          margin-right: 12px;
        }
        
        .action-text {
          .action-title {
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }
          
          .action-desc {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
  
  .realtime-data {
    .ant-list-item {
      padding: 8px 0;
    }
  }
}
</style>
