<!--
  分销员管理页面
  @description 分销员申请审核、等级管理、业绩统计等功能
-->
<template>
  <div class="distributor-management">
    <a-card :bordered="false">
      <!-- 分销员统计卡片 -->
      <div class="distributor-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="总分销员数"
                :value="distributorStats.totalCount"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <TeamOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="今日新增"
                :value="distributorStats.todayCount"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <UserAddOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="活跃分销员"
                :value="distributorStats.activeCount"
                :value-style="{ color: '#fa8c16' }"
              >
                <template #prefix>
                  <FireOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="待审核申请"
                :value="distributorStats.pendingCount"
                :value-style="{ color: '#f5222d' }"
              >
                <template #prefix>
                  <ClockCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="用户信息" name="keyword">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="请输入手机号/昵称/邀请码"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="分销员等级" name="levelId">
            <a-select
              v-model:value="searchForm.levelId"
              placeholder="请选择分销员等级"
              allow-clear
              style="width: 150px"
            >
              <a-select-option
                v-for="level in distributorLevels"
                :key="level.id"
                :value="level.id"
              >
                {{ level.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="状态" name="status">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="0">申请中</a-select-option>
              <a-select-option :value="1">正常</a-select-option>
              <a-select-option :value="2">禁用</a-select-option>
              <a-select-option :value="3">已拒绝</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请时间" name="applyTime">
            <a-range-picker
              v-model:value="searchForm.applyTime"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchApprove"
          >
            <CheckOutlined />
            批量通过
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchReject"
          >
            <CloseOutlined />
            批量拒绝
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchDisable"
          >
            <StopOutlined />
            批量禁用
          </a-button>
          <a-button @click="handleExport">
            <ExportOutlined />
            导出
          </a-button>
        </a-space>
      </div>

      <!-- 分销员表格 -->
      <a-table
        ref="tableRef"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1600 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 分销员信息 -->
        <template #distributorInfo="{ record }">
          <div class="distributor-info">
            <div class="distributor-avatar">
              <a-avatar :size="48" :src="record.avatar">
                {{ record.nickname?.charAt(0) || 'U' }}
              </a-avatar>
            </div>
            <div class="distributor-details">
              <div class="distributor-nickname">{{ record.nickname || '未设置' }}</div>
              <div class="distributor-mobile">{{ record.mobile || '未绑定' }}</div>
              <div class="distributor-invite-code">邀请码: {{ record.inviteCode }}</div>
            </div>
          </div>
        </template>

        <!-- 分销员等级 -->
        <template #level="{ record }">
          <a-tag v-if="record.levelName" :color="getLevelColor(record.levelId)">
            {{ record.levelName }}
          </a-tag>
          <span v-else>-</span>
        </template>

        <!-- 业绩统计 -->
        <template #performance="{ record }">
          <div class="performance-stats">
            <div class="stats-item">
              <span class="label">推广用户:</span>
              <span class="value">{{ record.totalUserCount || 0 }}</span>
            </div>
            <div class="stats-item">
              <span class="label">订单数:</span>
              <span class="value">{{ record.totalOrderCount || 0 }}</span>
            </div>
            <div class="stats-item">
              <span class="label">累计佣金:</span>
              <span class="value">¥{{ record.totalBrokerage || 0 }}</span>
            </div>
          </div>
        </template>

        <!-- 佣金信息 -->
        <template #brokerageInfo="{ record }">
          <div class="brokerage-info">
            <div class="brokerage-item">
              <span class="label">可提现:</span>
              <span class="value available">¥{{ record.availableBrokerage || 0 }}</span>
            </div>
            <div class="brokerage-item">
              <span class="label">已冻结:</span>
              <span class="value frozen">¥{{ record.frozenBrokerage || 0 }}</span>
            </div>
            <div class="brokerage-item">
              <span class="label">已提现:</span>
              <span class="value withdrawn">¥{{ record.totalWithdraw || 0 }}</span>
            </div>
          </div>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-if="record.status === 0"
                    @click="handleApprove(record)"
                  >
                    <CheckOutlined />
                    通过申请
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status === 0"
                    @click="handleReject(record)"
                  >
                    <CloseOutlined />
                    拒绝申请
                  </a-menu-item>
                  <a-menu-item @click="handleViewPerformance(record)">
                    <BarChartOutlined />
                    查看业绩
                  </a-menu-item>
                  <a-menu-item @click="handleViewTeam(record)">
                    <TeamOutlined />
                    查看团队
                  </a-menu-item>
                  <a-menu-item @click="handleManageBrokerage(record)">
                    <WalletOutlined />
                    佣金管理
                  </a-menu-item>
                  <a-menu-item @click="handleUpdateLevel(record)">
                    <CrownOutlined />
                    调整等级
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item
                    v-if="record.status === 1"
                    @click="handleDisable(record)"
                  >
                    <StopOutlined />
                    禁用
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status === 2"
                    @click="handleEnable(record)"
                  >
                    <CheckOutlined />
                    启用
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 分销员详情抽屉 -->
    <DistributorDetailDrawer
      v-model:open="detailDrawerVisible"
      :distributor-id="currentDistributorId"
    />

    <!-- 业绩统计抽屉 -->
    <DistributorPerformanceDrawer
      v-model:open="performanceDrawerVisible"
      :distributor-id="currentDistributorId"
    />

    <!-- 团队管理抽屉 -->
    <DistributorTeamDrawer
      v-model:open="teamDrawerVisible"
      :distributor-id="currentDistributorId"
    />

    <!-- 佣金管理弹窗 -->
    <DistributorBrokerageModal
      v-model:open="brokerageModalVisible"
      :distributor-id="currentDistributorId"
      @success="handleBrokerageSuccess"
    />

    <!-- 等级调整弹窗 -->
    <DistributorLevelModal
      v-model:open="levelModalVisible"
      :distributor-id="currentDistributorId"
      @success="handleLevelSuccess"
    />

    <!-- 审核弹窗 -->
    <DistributorAuditModal
      v-model:open="auditModalVisible"
      :distributor="currentDistributor"
      @success="handleAuditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  CheckOutlined,
  CloseOutlined,
  StopOutlined,
  ExportOutlined,
  DownOutlined,
  TeamOutlined,
  UserAddOutlined,
  FireOutlined,
  ClockCircleOutlined,
  BarChartOutlined,
  WalletOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'

// 模拟组件导入
const DistributorDetailDrawer = defineAsyncComponent(() => import('./components/DistributorDetailDrawer.vue'))
const DistributorPerformanceDrawer = defineAsyncComponent(() => import('./components/DistributorPerformanceDrawer.vue'))
const DistributorTeamDrawer = defineAsyncComponent(() => import('./components/DistributorTeamDrawer.vue'))
const DistributorBrokerageModal = defineAsyncComponent(() => import('./components/DistributorBrokerageModal.vue'))
const DistributorLevelModal = defineAsyncComponent(() => import('./components/DistributorLevelModal.vue'))
const DistributorAuditModal = defineAsyncComponent(() => import('./components/DistributorAuditModal.vue'))

// 响应式数据
const loading = ref(false)
const dataSource = ref<any[]>([])
const selectedRowKeys = ref<string[]>([])
const distributorLevels = ref<any[]>([])
const distributorStats = ref<any>({})
const detailDrawerVisible = ref(false)
const performanceDrawerVisible = ref(false)
const teamDrawerVisible = ref(false)
const brokerageModalVisible = ref(false)
const levelModalVisible = ref(false)
const auditModalVisible = ref(false)
const currentDistributorId = ref<number | null>(null)
const currentDistributor = ref<any>(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  keyword: '',
  levelId: undefined,
  status: undefined,
  applyTime: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '分销员信息',
    key: 'distributorInfo',
    width: 250,
    fixed: 'left',
    slots: { customRender: 'distributorInfo' }
  },
  {
    title: '分销员等级',
    key: 'level',
    width: 120,
    slots: { customRender: 'level' }
  },
  {
    title: '业绩统计',
    key: 'performance',
    width: 200,
    slots: { customRender: 'performance' }
  },
  {
    title: '佣金信息',
    key: 'brokerageInfo',
    width: 180,
    slots: { customRender: 'brokerageInfo' }
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    width: 180
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

/**
 * 获取等级颜色
 */
const getLevelColor = (levelId: number): string => {
  const colorMap: Record<number, string> = {
    1: 'default',  // 初级分销员
    2: 'blue',     // 中级分销员
    3: 'green',    // 高级分销员
    4: 'gold',     // 金牌分销员
    5: 'red'       // 钻石分销员
  }
  return colorMap[levelId] || 'default'
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'orange',   // 申请中
    1: 'green',    // 正常
    2: 'red',      // 禁用
    3: 'default'   // 已拒绝
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    0: '申请中',
    1: '正常',
    2: '禁用',
    3: '已拒绝'
  }
  return textMap[status] || '未知'
}

// 模拟数据加载函数
const loadData = async () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    dataSource.value = [
      {
        id: 1,
        userId: 1,
        nickname: '张三',
        mobile: '13800138000',
        avatar: '',
        inviteCode: 'ABC123',
        levelId: 2,
        levelName: '中级分销员',
        status: 1,
        totalUserCount: 50,
        totalOrderCount: 120,
        totalBrokerage: 5000.00,
        availableBrokerage: 3000.00,
        frozenBrokerage: 500.00,
        totalWithdraw: 1500.00,
        applyTime: '2024-01-01 10:00:00'
      }
    ]
    pagination.total = 1
    loading.value = false
  }, 1000)
}

const loadDistributorLevels = async () => {
  distributorLevels.value = [
    { id: 1, name: '初级分销员' },
    { id: 2, name: '中级分销员' },
    { id: 3, name: '高级分销员' },
    { id: 4, name: '金牌分销员' },
    { id: 5, name: '钻石分销员' }
  ]
}

const loadDistributorStats = async () => {
  distributorStats.value = {
    totalCount: 1000,
    todayCount: 25,
    activeCount: 800,
    pendingCount: 15
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  loadData()
}

const handleView = (record: any) => {
  currentDistributorId.value = record.id
  detailDrawerVisible.value = true
}

const handleApprove = (record: any) => {
  currentDistributor.value = { ...record, auditType: 'approve' }
  auditModalVisible.value = true
}

const handleReject = (record: any) => {
  currentDistributor.value = { ...record, auditType: 'reject' }
  auditModalVisible.value = true
}

const handleAuditSuccess = () => {
  loadData()
  loadDistributorStats()
}

const handleViewPerformance = (record: any) => {
  currentDistributorId.value = record.id
  performanceDrawerVisible.value = true
}

const handleViewTeam = (record: any) => {
  currentDistributorId.value = record.id
  teamDrawerVisible.value = true
}

const handleManageBrokerage = (record: any) => {
  currentDistributorId.value = record.id
  brokerageModalVisible.value = true
}

const handleBrokerageSuccess = () => {
  loadData()
}

const handleUpdateLevel = (record: any) => {
  currentDistributorId.value = record.id
  levelModalVisible.value = true
}

const handleLevelSuccess = () => {
  loadData()
}

const handleDisable = (record: any) => {
  Modal.confirm({
    title: '确认禁用',
    content: `确定要禁用分销员"${record.nickname}"吗？`,
    onOk: () => {
      message.success('禁用成功')
      loadData()
    }
  })
}

const handleEnable = (record: any) => {
  message.success('启用成功')
  loadData()
}

const handleBatchApprove = () => {
  message.info('批量通过功能开发中...')
}

const handleBatchReject = () => {
  message.info('批量拒绝功能开发中...')
}

const handleBatchDisable = () => {
  message.info('批量禁用功能开发中...')
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

// 页面加载时初始化数据
onMounted(() => {
  loadData()
  loadDistributorLevels()
  loadDistributorStats()
})
</script>

<style scoped lang="less">
.distributor-management {
  .distributor-stats {
    margin-bottom: 24px;
    
    .stat-card {
      text-align: center;
    }
  }
  
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .action-buttons {
    margin-bottom: 16px;
  }
  
  .distributor-info {
    display: flex;
    align-items: center;
    
    .distributor-avatar {
      margin-right: 12px;
    }
    
    .distributor-details {
      flex: 1;
      
      .distributor-nickname {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }
      
      .distributor-mobile {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 2px;
      }
      
      .distributor-invite-code {
        font-size: 12px;
        color: #595959;
      }
    }
  }
  
  .performance-stats {
    .stats-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;
      
      .label {
        color: #8c8c8c;
      }
      
      .value {
        color: #262626;
        font-weight: 500;
      }
    }
  }
  
  .brokerage-info {
    .brokerage-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;
      
      .label {
        color: #8c8c8c;
      }
      
      .value {
        font-weight: 500;
        
        &.available {
          color: #52c41a;
        }
        
        &.frozen {
          color: #fa8c16;
        }
        
        &.withdrawn {
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
