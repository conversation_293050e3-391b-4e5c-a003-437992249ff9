/**
 * ID生成器工具
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
}

/**
 * 生成节点ID
 */
export function generateNodeId(): string {
  return `node_${generateId()}`
}

/**
 * 生成连线ID
 */
export function generateEdgeId(): string {
  return `edge_${generateId()}`
}

/**
 * 生成任务ID
 */
export function generateTaskId(): string {
  return `task_${generateId()}`
}

/**
 * 生成流程实例ID
 */
export function generateProcessInstanceId(): string {
  return `process_${generateId()}`
}

/**
 * 生成表单字段ID
 */
export function generateFormFieldId(): string {
  return `field_${generateId()}`
}

/**
 * 生成UUID（简化版）
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 生成短ID（8位）
 */
export function generateShortId(): string {
  return Math.random().toString(36).substr(2, 8).toUpperCase()
}

/**
 * 生成业务键
 */
export function generateBusinessKey(prefix: string = 'BIZ'): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substr(2, 4).toUpperCase()
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 生成流程键
 */
export function generateProcessKey(name: string): string {
  // 将中文名称转换为拼音或英文标识
  const key = name
    .replace(/[\u4e00-\u9fa5]/g, '') // 移除中文字符
    .replace(/[^a-zA-Z0-9]/g, '_') // 替换特殊字符为下划线
    .toLowerCase()
    .replace(/_{2,}/g, '_') // 合并多个下划线
    .replace(/^_|_$/g, '') // 移除首尾下划线
  
  return key || `process_${generateShortId().toLowerCase()}`
}

/**
 * 检查ID是否有效
 */
export function isValidId(id: string): boolean {
  return typeof id === 'string' && id.length > 0 && /^[a-zA-Z0-9_-]+$/.test(id)
}

/**
 * 检查ID是否唯一
 */
export function isUniqueId(id: string, existingIds: string[]): boolean {
  return !existingIds.includes(id)
}

/**
 * 生成唯一ID（确保在给定列表中唯一）
 */
export function generateUniqueId(prefix: string, existingIds: string[]): string {
  let id: string
  let counter = 1
  
  do {
    id = `${prefix}_${counter}`
    counter++
  } while (existingIds.includes(id))
  
  return id
}

/**
 * 生成唯一节点ID
 */
export function generateUniqueNodeId(existingIds: string[]): string {
  return generateUniqueId('node', existingIds)
}

/**
 * 生成唯一连线ID
 */
export function generateUniqueEdgeId(existingIds: string[]): string {
  return generateUniqueId('edge', existingIds)
}

/**
 * 批量生成ID
 */
export function generateBatchIds(count: number, prefix: string = ''): string[] {
  const ids: string[] = []
  for (let i = 0; i < count; i++) {
    ids.push(prefix ? `${prefix}_${generateId()}` : generateId())
  }
  return ids
}

/**
 * 从名称生成ID
 */
export function generateIdFromName(name: string, prefix: string = ''): string {
  const cleanName = name
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '')
  
  const id = cleanName || generateShortId().toLowerCase()
  return prefix ? `${prefix}_${id}` : id
}

/**
 * 验证并修复ID
 */
export function validateAndFixId(id: string, fallbackPrefix: string = 'item'): string {
  if (!id || typeof id !== 'string') {
    return `${fallbackPrefix}_${generateShortId().toLowerCase()}`
  }
  
  // 移除无效字符
  const cleanId = id.replace(/[^a-zA-Z0-9_-]/g, '_')
  
  // 确保以字母开头
  if (!/^[a-zA-Z]/.test(cleanId)) {
    return `${fallbackPrefix}_${cleanId}`
  }
  
  return cleanId || `${fallbackPrefix}_${generateShortId().toLowerCase()}`
}

/**
 * ID工具类
 */
export class IdGenerator {
  private static counters: Map<string, number> = new Map()
  
  /**
   * 获取下一个序号
   */
  static getNextSequence(prefix: string): number {
    const current = this.counters.get(prefix) || 0
    const next = current + 1
    this.counters.set(prefix, next)
    return next
  }
  
  /**
   * 重置序号
   */
  static resetSequence(prefix: string): void {
    this.counters.set(prefix, 0)
  }
  
  /**
   * 生成序列ID
   */
  static generateSequenceId(prefix: string): string {
    const sequence = this.getNextSequence(prefix)
    return `${prefix}_${sequence.toString().padStart(3, '0')}`
  }
  
  /**
   * 生成时间戳ID
   */
  static generateTimestampId(prefix: string = ''): string {
    const timestamp = Date.now()
    return prefix ? `${prefix}_${timestamp}` : timestamp.toString()
  }
  
  /**
   * 生成日期ID
   */
  static generateDateId(prefix: string = ''): string {
    const date = new Date()
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStr = date.toTimeString().slice(0, 8).replace(/:/g, '')
    const id = `${dateStr}_${timeStr}`
    return prefix ? `${prefix}_${id}` : id
  }
}
