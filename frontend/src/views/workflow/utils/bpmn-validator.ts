/**
 * BPMN验证器
 * <AUTHOR>
 * @since 1.0.0
 */

import type { ValidationResult, ValidationError, ValidationWarning } from './workflow-validator'

/**
 * 验证BPMN XML
 */
export function validateBpmnXml(xml: string): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []
  
  try {
    // 基础XML格式验证
    validateXmlFormat(xml, errors)
    
    // BPMN结构验证
    validateBpmnStructure(xml, errors, warnings)
    
    // 业务规则验证
    validateBusinessRules(xml, errors, warnings)
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  } catch (error) {
    errors.push({
      type: 'error',
      code: 'VALIDATION_ERROR',
      message: `验证过程中发生错误: ${error}`
    })
    
    return {
      valid: false,
      errors,
      warnings
    }
  }
}

/**
 * 验证XML格式
 */
function validateXmlFormat(xml: string, errors: ValidationError[]): void {
  if (!xml || xml.trim().length === 0) {
    errors.push({
      type: 'error',
      code: 'EMPTY_XML',
      message: 'BPMN XML不能为空'
    })
    return
  }
  
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xml, 'application/xml')
    
    // 检查解析错误
    const parseError = doc.querySelector('parsererror')
    if (parseError) {
      errors.push({
        type: 'error',
        code: 'XML_PARSE_ERROR',
        message: `XML格式错误: ${parseError.textContent}`
      })
    }
  } catch (error) {
    errors.push({
      type: 'error',
      code: 'XML_PARSE_ERROR',
      message: `XML解析失败: ${error}`
    })
  }
}

/**
 * 验证BPMN结构
 */
function validateBpmnStructure(xml: string, errors: ValidationError[], warnings: ValidationWarning[]): void {
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xml, 'application/xml')
    
    // 检查根元素
    const definitions = doc.querySelector('definitions')
    if (!definitions) {
      errors.push({
        type: 'error',
        code: 'MISSING_DEFINITIONS',
        message: '缺少BPMN definitions根元素'
      })
      return
    }
    
    // 检查命名空间
    validateNamespaces(definitions, errors, warnings)
    
    // 检查流程元素
    validateProcessElements(doc, errors, warnings)
    
    // 检查图形元素
    validateDiagramElements(doc, errors, warnings)
    
  } catch (error) {
    errors.push({
      type: 'error',
      code: 'STRUCTURE_VALIDATION_ERROR',
      message: `结构验证失败: ${error}`
    })
  }
}

/**
 * 验证命名空间
 */
function validateNamespaces(definitions: Element, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const requiredNamespaces = [
    'http://www.omg.org/spec/BPMN/20100524/MODEL',
    'http://www.omg.org/spec/BPMN/20100524/DI'
  ]
  
  const attributes = definitions.attributes
  const namespaces: string[] = []
  
  for (let i = 0; i < attributes.length; i++) {
    const attr = attributes[i]
    if (attr.name.startsWith('xmlns')) {
      namespaces.push(attr.value)
    }
  }
  
  requiredNamespaces.forEach(ns => {
    if (!namespaces.includes(ns)) {
      warnings.push({
        type: 'warning',
        code: 'MISSING_NAMESPACE',
        message: `缺少必需的命名空间: ${ns}`
      })
    }
  })
}

/**
 * 验证流程元素
 */
function validateProcessElements(doc: Document, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const processes = doc.querySelectorAll('process')
  
  if (processes.length === 0) {
    errors.push({
      type: 'error',
      code: 'NO_PROCESS',
      message: '至少需要一个流程元素'
    })
    return
  }
  
  processes.forEach((process, index) => {
    validateSingleProcess(process, errors, warnings, index)
  })
}

/**
 * 验证单个流程
 */
function validateSingleProcess(process: Element, errors: ValidationError[], warnings: ValidationWarning[], index: number): void {
  const processId = process.getAttribute('id')
  if (!processId) {
    errors.push({
      type: 'error',
      code: 'MISSING_PROCESS_ID',
      message: `流程${index + 1}缺少ID属性`
    })
  }
  
  // 检查开始事件
  const startEvents = process.querySelectorAll('startEvent')
  if (startEvents.length === 0) {
    errors.push({
      type: 'error',
      code: 'MISSING_START_EVENT',
      message: `流程${processId || index + 1}缺少开始事件`
    })
  } else if (startEvents.length > 1) {
    warnings.push({
      type: 'warning',
      code: 'MULTIPLE_START_EVENTS',
      message: `流程${processId || index + 1}有多个开始事件`
    })
  }
  
  // 检查结束事件
  const endEvents = process.querySelectorAll('endEvent')
  if (endEvents.length === 0) {
    warnings.push({
      type: 'warning',
      code: 'MISSING_END_EVENT',
      message: `流程${processId || index + 1}建议添加结束事件`
    })
  }
  
  // 验证任务元素
  validateTasks(process, errors, warnings, processId || `${index + 1}`)
  
  // 验证网关元素
  validateGateways(process, errors, warnings, processId || `${index + 1}`)
  
  // 验证序列流
  validateSequenceFlows(process, errors, warnings, processId || `${index + 1}`)
}

/**
 * 验证任务元素
 */
function validateTasks(process: Element, errors: ValidationError[], warnings: ValidationWarning[], processId: string): void {
  // 验证用户任务
  const userTasks = process.querySelectorAll('userTask')
  userTasks.forEach(task => {
    const taskId = task.getAttribute('id')
    const taskName = task.getAttribute('name')
    
    if (!taskName) {
      warnings.push({
        type: 'warning',
        code: 'MISSING_TASK_NAME',
        message: `用户任务${taskId}建议设置名称`,
        nodeId: taskId || undefined
      })
    }
    
    // 检查处理人配置
    const assignee = task.getAttribute('assignee')
    const candidateUsers = task.getAttribute('candidateUsers')
    const candidateGroups = task.getAttribute('candidateGroups')
    
    if (!assignee && !candidateUsers && !candidateGroups) {
      warnings.push({
        type: 'warning',
        code: 'MISSING_TASK_ASSIGNEE',
        message: `用户任务${taskId}未配置处理人`,
        nodeId: taskId || undefined
      })
    }
  })
  
  // 验证服务任务
  const serviceTasks = process.querySelectorAll('serviceTask')
  serviceTasks.forEach(task => {
    const taskId = task.getAttribute('id')
    const implementation = task.getAttribute('class') || 
                          task.getAttribute('expression') || 
                          task.getAttribute('delegateExpression')
    
    if (!implementation) {
      errors.push({
        type: 'error',
        code: 'MISSING_SERVICE_IMPLEMENTATION',
        message: `服务任务${taskId}未配置实现`,
        nodeId: taskId || undefined
      })
    }
  })
}

/**
 * 验证网关元素
 */
function validateGateways(process: Element, errors: ValidationError[], warnings: ValidationWarning[], processId: string): void {
  const gateways = process.querySelectorAll('exclusiveGateway, parallelGateway, inclusiveGateway')
  
  gateways.forEach(gateway => {
    const gatewayId = gateway.getAttribute('id')
    const gatewayType = gateway.tagName
    
    // 检查网关的入边和出边
    const incoming = process.querySelectorAll(`sequenceFlow[targetRef="${gatewayId}"]`)
    const outgoing = process.querySelectorAll(`sequenceFlow[sourceRef="${gatewayId}"]`)
    
    if (incoming.length === 0) {
      warnings.push({
        type: 'warning',
        code: 'GATEWAY_NO_INCOMING',
        message: `${gatewayType}${gatewayId}没有入边`,
        nodeId: gatewayId || undefined
      })
    }
    
    if (outgoing.length === 0) {
      warnings.push({
        type: 'warning',
        code: 'GATEWAY_NO_OUTGOING',
        message: `${gatewayType}${gatewayId}没有出边`,
        nodeId: gatewayId || undefined
      })
    }
    
    // 排他网关特殊验证
    if (gatewayType === 'exclusiveGateway' && outgoing.length > 1) {
      const hasDefault = gateway.getAttribute('default')
      let hasConditions = false
      
      outgoing.forEach(flow => {
        const condition = flow.querySelector('conditionExpression')
        if (condition) {
          hasConditions = true
        }
      })
      
      if (!hasDefault && !hasConditions) {
        warnings.push({
          type: 'warning',
          code: 'EXCLUSIVE_GATEWAY_NO_CONDITIONS',
          message: `排他网关${gatewayId}建议配置条件或默认流`,
          nodeId: gatewayId || undefined
        })
      }
    }
  })
}

/**
 * 验证序列流
 */
function validateSequenceFlows(process: Element, errors: ValidationError[], warnings: ValidationWarning[], processId: string): void {
  const sequenceFlows = process.querySelectorAll('sequenceFlow')
  
  sequenceFlows.forEach(flow => {
    const flowId = flow.getAttribute('id')
    const sourceRef = flow.getAttribute('sourceRef')
    const targetRef = flow.getAttribute('targetRef')
    
    if (!sourceRef) {
      errors.push({
        type: 'error',
        code: 'MISSING_FLOW_SOURCE',
        message: `序列流${flowId}缺少源节点引用`,
        edgeId: flowId || undefined
      })
    }
    
    if (!targetRef) {
      errors.push({
        type: 'error',
        code: 'MISSING_FLOW_TARGET',
        message: `序列流${flowId}缺少目标节点引用`,
        edgeId: flowId || undefined
      })
    }
    
    // 检查引用的节点是否存在
    if (sourceRef) {
      const sourceElement = process.querySelector(`[id="${sourceRef}"]`)
      if (!sourceElement) {
        errors.push({
          type: 'error',
          code: 'INVALID_FLOW_SOURCE',
          message: `序列流${flowId}的源节点${sourceRef}不存在`,
          edgeId: flowId || undefined
        })
      }
    }
    
    if (targetRef) {
      const targetElement = process.querySelector(`[id="${targetRef}"]`)
      if (!targetElement) {
        errors.push({
          type: 'error',
          code: 'INVALID_FLOW_TARGET',
          message: `序列流${flowId}的目标节点${targetRef}不存在`,
          edgeId: flowId || undefined
        })
      }
    }
  })
}

/**
 * 验证图形元素
 */
function validateDiagramElements(doc: Document, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const diagrams = doc.querySelectorAll('BPMNDiagram')
  
  if (diagrams.length === 0) {
    warnings.push({
      type: 'warning',
      code: 'MISSING_DIAGRAM',
      message: '缺少BPMN图形信息'
    })
    return
  }
  
  diagrams.forEach((diagram, index) => {
    const plane = diagram.querySelector('BPMNPlane')
    if (!plane) {
      warnings.push({
        type: 'warning',
        code: 'MISSING_DIAGRAM_PLANE',
        message: `图形${index + 1}缺少BPMNPlane元素`
      })
    }
  })
}

/**
 * 验证业务规则
 */
function validateBusinessRules(xml: string, errors: ValidationError[], warnings: ValidationWarning[]): void {
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xml, 'application/xml')
    
    // 检查流程可执行性
    const processes = doc.querySelectorAll('process')
    processes.forEach(process => {
      const isExecutable = process.getAttribute('isExecutable')
      if (isExecutable !== 'true') {
        warnings.push({
          type: 'warning',
          code: 'PROCESS_NOT_EXECUTABLE',
          message: `流程${process.getAttribute('id')}未标记为可执行`
        })
      }
    })
    
    // 检查ID唯一性
    validateIdUniqueness(doc, errors)
    
    // 检查循环引用
    validateCircularReferences(doc, warnings)
    
  } catch (error) {
    errors.push({
      type: 'error',
      code: 'BUSINESS_RULE_VALIDATION_ERROR',
      message: `业务规则验证失败: ${error}`
    })
  }
}

/**
 * 验证ID唯一性
 */
function validateIdUniqueness(doc: Document, errors: ValidationError[]): void {
  const elements = doc.querySelectorAll('[id]')
  const ids = new Set<string>()
  
  elements.forEach(element => {
    const id = element.getAttribute('id')
    if (id) {
      if (ids.has(id)) {
        errors.push({
          type: 'error',
          code: 'DUPLICATE_ID',
          message: `ID重复: ${id}`
        })
      }
      ids.add(id)
    }
  })
}

/**
 * 验证循环引用
 */
function validateCircularReferences(doc: Document, warnings: ValidationWarning[]): void {
  const processes = doc.querySelectorAll('process')
  
  processes.forEach(process => {
    const sequenceFlows = process.querySelectorAll('sequenceFlow')
    const graph = new Map<string, string[]>()
    
    // 构建图
    sequenceFlows.forEach(flow => {
      const source = flow.getAttribute('sourceRef')
      const target = flow.getAttribute('targetRef')
      
      if (source && target) {
        if (!graph.has(source)) {
          graph.set(source, [])
        }
        graph.get(source)!.push(target)
      }
    })
    
    // 检测环
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    
    function hasCycle(nodeId: string): boolean {
      if (recursionStack.has(nodeId)) {
        return true
      }
      
      if (visited.has(nodeId)) {
        return false
      }
      
      visited.add(nodeId)
      recursionStack.add(nodeId)
      
      const neighbors = graph.get(nodeId) || []
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) {
          return true
        }
      }
      
      recursionStack.delete(nodeId)
      return false
    }
    
    for (const [nodeId] of graph) {
      if (!visited.has(nodeId)) {
        if (hasCycle(nodeId)) {
          warnings.push({
            type: 'warning',
            code: 'CIRCULAR_REFERENCE',
            message: '检测到潜在的循环引用'
          })
          break
        }
      }
    }
  })
}

/**
 * 快速验证BPMN XML
 */
export function quickValidateBpmn(xml: string): boolean {
  if (!xml || xml.trim().length === 0) {
    return false
  }
  
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xml, 'application/xml')
    
    // 检查解析错误
    const parseError = doc.querySelector('parsererror')
    if (parseError) {
      return false
    }
    
    // 检查基本结构
    const definitions = doc.querySelector('definitions')
    if (!definitions) {
      return false
    }
    
    const processes = doc.querySelectorAll('process')
    if (processes.length === 0) {
      return false
    }
    
    return true
  } catch (error) {
    return false
  }
}
