/**
 * 工作流验证器
 * <AUTHOR>
 * @since 1.0.0
 */

import type { FlowDefinition, FlowNode, FlowEdge } from '../types/workflow'

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

/**
 * 验证错误
 */
export interface ValidationError {
  type: 'error'
  code: string
  message: string
  nodeId?: string
  edgeId?: string
}

/**
 * 验证警告
 */
export interface ValidationWarning {
  type: 'warning'
  code: string
  message: string
  nodeId?: string
  edgeId?: string
}

/**
 * 验证流程定义
 */
export function validateFlowDefinition(definition: FlowDefinition): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []
  
  // 基础验证
  validateBasicStructure(definition, errors)
  
  // 节点验证
  validateNodes(definition.nodes, errors, warnings)
  
  // 连线验证
  validateEdges(definition.edges, definition.nodes, errors, warnings)
  
  // 流程完整性验证
  validateFlowIntegrity(definition, errors, warnings)
  
  // 业务规则验证
  validateBusinessRules(definition, errors, warnings)
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证基础结构
 */
function validateBasicStructure(definition: FlowDefinition, errors: ValidationError[]): void {
  if (!definition) {
    errors.push({
      type: 'error',
      code: 'MISSING_DEFINITION',
      message: '流程定义不能为空'
    })
    return
  }
  
  if (!Array.isArray(definition.nodes)) {
    errors.push({
      type: 'error',
      code: 'INVALID_NODES',
      message: '节点列表必须是数组'
    })
  }
  
  if (!Array.isArray(definition.edges)) {
    errors.push({
      type: 'error',
      code: 'INVALID_EDGES',
      message: '连线列表必须是数组'
    })
  }
  
  if (!definition.settings) {
    errors.push({
      type: 'error',
      code: 'MISSING_SETTINGS',
      message: '流程设置不能为空'
    })
  }
}

/**
 * 验证节点
 */
function validateNodes(nodes: FlowNode[], errors: ValidationError[], warnings: ValidationWarning[]): void {
  if (nodes.length === 0) {
    errors.push({
      type: 'error',
      code: 'NO_NODES',
      message: '流程至少需要一个节点'
    })
    return
  }
  
  const nodeIds = new Set<string>()
  let hasStartNode = false
  let hasEndNode = false
  
  nodes.forEach(node => {
    // 检查节点ID唯一性
    if (nodeIds.has(node.id)) {
      errors.push({
        type: 'error',
        code: 'DUPLICATE_NODE_ID',
        message: `节点ID重复: ${node.id}`,
        nodeId: node.id
      })
    }
    nodeIds.add(node.id)
    
    // 检查必填字段
    if (!node.id) {
      errors.push({
        type: 'error',
        code: 'MISSING_NODE_ID',
        message: '节点ID不能为空'
      })
    }
    
    if (!node.name) {
      errors.push({
        type: 'error',
        code: 'MISSING_NODE_NAME',
        message: '节点名称不能为空',
        nodeId: node.id
      })
    }
    
    if (!node.type) {
      errors.push({
        type: 'error',
        code: 'MISSING_NODE_TYPE',
        message: '节点类型不能为空',
        nodeId: node.id
      })
    }
    
    // 检查节点类型
    if (node.type === 'START') {
      hasStartNode = true
    } else if (node.type === 'END') {
      hasEndNode = true
    }
    
    // 验证节点位置
    if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
      errors.push({
        type: 'error',
        code: 'INVALID_NODE_POSITION',
        message: '节点位置无效',
        nodeId: node.id
      })
    }
    
    // 验证节点大小
    if (!node.size || typeof node.size.width !== 'number' || typeof node.size.height !== 'number') {
      errors.push({
        type: 'error',
        code: 'INVALID_NODE_SIZE',
        message: '节点大小无效',
        nodeId: node.id
      })
    }
    
    // 验证用户任务节点
    if (node.type === 'USER_TASK') {
      validateUserTaskNode(node, errors, warnings)
    }
    
    // 验证网关节点
    if (node.type === 'GATEWAY') {
      validateGatewayNode(node, errors, warnings)
    }
    
    // 验证服务任务节点
    if (node.type === 'SERVICE_TASK') {
      validateServiceTaskNode(node, errors, warnings)
    }
  })
  
  // 检查开始和结束节点
  if (!hasStartNode) {
    errors.push({
      type: 'error',
      code: 'MISSING_START_NODE',
      message: '流程必须有一个开始节点'
    })
  }
  
  if (!hasEndNode) {
    warnings.push({
      type: 'warning',
      code: 'MISSING_END_NODE',
      message: '建议添加结束节点'
    })
  }
}

/**
 * 验证用户任务节点
 */
function validateUserTaskNode(node: FlowNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const properties = node.properties || {}
  
  if (!properties.assigneeType) {
    errors.push({
      type: 'error',
      code: 'MISSING_ASSIGNEE_TYPE',
      message: '用户任务节点必须指定处理人类型',
      nodeId: node.id
    })
  }
  
  if (!properties.assigneeValue) {
    errors.push({
      type: 'error',
      code: 'MISSING_ASSIGNEE_VALUE',
      message: '用户任务节点必须指定处理人',
      nodeId: node.id
    })
  }
  
  if (properties.approvalType === 'MULTI_AND' || properties.approvalType === 'MULTI_OR') {
    if (!properties.candidateUsers && !properties.candidateRoles) {
      errors.push({
        type: 'error',
        code: 'MISSING_CANDIDATES',
        message: '多人审批节点必须指定候选人或候选角色',
        nodeId: node.id
      })
    }
  }
}

/**
 * 验证网关节点
 */
function validateGatewayNode(node: FlowNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const properties = node.properties || {}
  
  if (!properties.gatewayType) {
    errors.push({
      type: 'error',
      code: 'MISSING_GATEWAY_TYPE',
      message: '网关节点必须指定网关类型',
      nodeId: node.id
    })
  }
  
  if (properties.gatewayType === 'EXCLUSIVE' && !properties.conditions) {
    warnings.push({
      type: 'warning',
      code: 'MISSING_GATEWAY_CONDITIONS',
      message: '排他网关建议配置分支条件',
      nodeId: node.id
    })
  }
}

/**
 * 验证服务任务节点
 */
function validateServiceTaskNode(node: FlowNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const properties = node.properties || {}
  
  if (!properties.serviceType) {
    errors.push({
      type: 'error',
      code: 'MISSING_SERVICE_TYPE',
      message: '服务任务节点必须指定服务类型',
      nodeId: node.id
    })
  }
  
  if (!properties.serviceConfig) {
    errors.push({
      type: 'error',
      code: 'MISSING_SERVICE_CONFIG',
      message: '服务任务节点必须配置服务参数',
      nodeId: node.id
    })
  }
}

/**
 * 验证连线
 */
function validateEdges(edges: FlowEdge[], nodes: FlowNode[], errors: ValidationError[], warnings: ValidationWarning[]): void {
  const nodeIds = new Set(nodes.map(node => node.id))
  const edgeIds = new Set<string>()
  
  edges.forEach(edge => {
    // 检查连线ID唯一性
    if (edgeIds.has(edge.id)) {
      errors.push({
        type: 'error',
        code: 'DUPLICATE_EDGE_ID',
        message: `连线ID重复: ${edge.id}`,
        edgeId: edge.id
      })
    }
    edgeIds.add(edge.id)
    
    // 检查必填字段
    if (!edge.id) {
      errors.push({
        type: 'error',
        code: 'MISSING_EDGE_ID',
        message: '连线ID不能为空'
      })
    }
    
    if (!edge.source) {
      errors.push({
        type: 'error',
        code: 'MISSING_EDGE_SOURCE',
        message: '连线源节点不能为空',
        edgeId: edge.id
      })
    }
    
    if (!edge.target) {
      errors.push({
        type: 'error',
        code: 'MISSING_EDGE_TARGET',
        message: '连线目标节点不能为空',
        edgeId: edge.id
      })
    }
    
    // 检查节点是否存在
    if (edge.source && !nodeIds.has(edge.source)) {
      errors.push({
        type: 'error',
        code: 'INVALID_EDGE_SOURCE',
        message: `连线源节点不存在: ${edge.source}`,
        edgeId: edge.id
      })
    }
    
    if (edge.target && !nodeIds.has(edge.target)) {
      errors.push({
        type: 'error',
        code: 'INVALID_EDGE_TARGET',
        message: `连线目标节点不存在: ${edge.target}`,
        edgeId: edge.id
      })
    }
    
    // 检查自环
    if (edge.source === edge.target) {
      warnings.push({
        type: 'warning',
        code: 'SELF_LOOP_EDGE',
        message: '连线不应该指向自己',
        edgeId: edge.id
      })
    }
  })
}

/**
 * 验证流程完整性
 */
function validateFlowIntegrity(definition: FlowDefinition, errors: ValidationError[], warnings: ValidationWarning[]): void {
  const { nodes, edges } = definition
  
  // 构建图结构
  const graph = buildGraph(nodes, edges)
  
  // 检查连通性
  validateConnectivity(graph, nodes, errors, warnings)
  
  // 检查死锁
  validateDeadlock(graph, nodes, errors, warnings)
  
  // 检查无限循环
  validateInfiniteLoop(graph, nodes, errors, warnings)
}

/**
 * 构建图结构
 */
function buildGraph(nodes: FlowNode[], edges: FlowEdge[]): Map<string, string[]> {
  const graph = new Map<string, string[]>()
  
  // 初始化节点
  nodes.forEach(node => {
    graph.set(node.id, [])
  })
  
  // 添加边
  edges.forEach(edge => {
    const outgoing = graph.get(edge.source) || []
    outgoing.push(edge.target)
    graph.set(edge.source, outgoing)
  })
  
  return graph
}

/**
 * 验证连通性
 */
function validateConnectivity(graph: Map<string, string[]>, nodes: FlowNode[], errors: ValidationError[], warnings: ValidationWarning[]): void {
  const startNodes = nodes.filter(node => node.type === 'START')
  const endNodes = nodes.filter(node => node.type === 'END')
  
  if (startNodes.length === 0) return
  
  // 从开始节点进行DFS
  const visited = new Set<string>()
  const stack = [startNodes[0].id]
  
  while (stack.length > 0) {
    const nodeId = stack.pop()!
    if (visited.has(nodeId)) continue
    
    visited.add(nodeId)
    const neighbors = graph.get(nodeId) || []
    stack.push(...neighbors)
  }
  
  // 检查不可达节点
  nodes.forEach(node => {
    if (!visited.has(node.id) && node.type !== 'START') {
      warnings.push({
        type: 'warning',
        code: 'UNREACHABLE_NODE',
        message: '节点不可达',
        nodeId: node.id
      })
    }
  })
  
  // 检查是否能到达结束节点
  if (endNodes.length > 0) {
    const canReachEnd = endNodes.some(endNode => visited.has(endNode.id))
    if (!canReachEnd) {
      warnings.push({
        type: 'warning',
        code: 'CANNOT_REACH_END',
        message: '无法到达结束节点'
      })
    }
  }
}

/**
 * 验证死锁
 */
function validateDeadlock(graph: Map<string, string[]>, nodes: FlowNode[], errors: ValidationError[], warnings: ValidationWarning[]): void {
  // 检查没有出边的非结束节点
  nodes.forEach(node => {
    if (node.type !== 'END') {
      const outgoing = graph.get(node.id) || []
      if (outgoing.length === 0) {
        warnings.push({
          type: 'warning',
          code: 'POTENTIAL_DEADLOCK',
          message: '节点可能导致死锁（没有出边）',
          nodeId: node.id
        })
      }
    }
  })
}

/**
 * 验证无限循环
 */
function validateInfiniteLoop(graph: Map<string, string[]>, nodes: FlowNode[], errors: ValidationError[], warnings: ValidationWarning[]): void {
  // 使用DFS检测环
  const visited = new Set<string>()
  const recursionStack = new Set<string>()
  
  function hasCycle(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) {
      return true
    }
    
    if (visited.has(nodeId)) {
      return false
    }
    
    visited.add(nodeId)
    recursionStack.add(nodeId)
    
    const neighbors = graph.get(nodeId) || []
    for (const neighbor of neighbors) {
      if (hasCycle(neighbor)) {
        return true
      }
    }
    
    recursionStack.delete(nodeId)
    return false
  }
  
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (hasCycle(node.id)) {
        warnings.push({
          type: 'warning',
          code: 'POTENTIAL_INFINITE_LOOP',
          message: '检测到潜在的无限循环'
        })
        break
      }
    }
  }
}

/**
 * 验证业务规则
 */
function validateBusinessRules(definition: FlowDefinition, errors: ValidationError[], warnings: ValidationWarning[]): void {
  // 检查流程名称
  if (!definition.settings.name || definition.settings.name.trim().length === 0) {
    errors.push({
      type: 'error',
      code: 'MISSING_PROCESS_NAME',
      message: '流程名称不能为空'
    })
  }
  
  // 检查表单字段
  const formFields = definition.settings.formSettings?.fields || []
  const fieldNames = new Set<string>()
  
  formFields.forEach(field => {
    if (fieldNames.has(field.name)) {
      errors.push({
        type: 'error',
        code: 'DUPLICATE_FIELD_NAME',
        message: `表单字段名称重复: ${field.name}`
      })
    }
    fieldNames.add(field.name)
    
    if (!field.name) {
      errors.push({
        type: 'error',
        code: 'MISSING_FIELD_NAME',
        message: '表单字段名称不能为空'
      })
    }
    
    if (!field.label) {
      errors.push({
        type: 'error',
        code: 'MISSING_FIELD_LABEL',
        message: '表单字段标签不能为空'
      })
    }
  })
}

/**
 * 快速验证（只检查关键错误）
 */
export function quickValidate(definition: FlowDefinition): boolean {
  if (!definition || !definition.nodes || !definition.edges) {
    return false
  }
  
  if (definition.nodes.length === 0) {
    return false
  }
  
  const hasStartNode = definition.nodes.some(node => node.type === 'START')
  if (!hasStartNode) {
    return false
  }
  
  return true
}

/**
 * 验证节点配置
 */
export function validateNodeConfig(node: FlowNode): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []
  
  if (node.type === 'USER_TASK') {
    validateUserTaskNode(node, errors, warnings)
  } else if (node.type === 'GATEWAY') {
    validateGatewayNode(node, errors, warnings)
  } else if (node.type === 'SERVICE_TASK') {
    validateServiceTaskNode(node, errors, warnings)
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}
