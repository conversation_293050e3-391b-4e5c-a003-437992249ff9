/**
 * BPMN转换器 - 在BPMN XML和通用流程定义格式之间转换
 * <AUTHOR>
 * @since 1.0.0
 */

import type { FlowDefinition, FlowNode, FlowEdge, NodeType } from '../types/workflow'
import { generateNodeId, generateEdgeId } from './id-generator'

/**
 * 将BPMN XML转换为通用流程定义格式
 */
export function convertBpmnToFlowDefinition(bpmnXml: string): FlowDefinition {
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(bpmnXml, 'application/xml')
    
    const nodes: FlowNode[] = []
    const edges: FlowEdge[] = []
    
    // 解析流程元素
    const processes = doc.querySelectorAll('process')
    processes.forEach(process => {
      parseProcessElements(process, doc, nodes, edges)
    })
    
    return {
      nodes,
      edges,
      variables: {},
      settings: {
        name: getProcessName(doc) || '',
        description: getProcessDocumentation(doc) || '',
        formSettings: {
          fields: []
        }
      }
    }
  } catch (error) {
    console.error('BPMN转换失败:', error)
    return {
      nodes: [],
      edges: [],
      variables: {},
      settings: {
        name: '',
        formSettings: {
          fields: []
        }
      }
    }
  }
}

/**
 * 解析流程元素
 */
function parseProcessElements(process: Element, doc: Document, nodes: FlowNode[], edges: FlowEdge[]): void {
  // 解析所有流程元素
  const elements = process.querySelectorAll('*')
  
  elements.forEach(element => {
    const tagName = element.tagName
    const id = element.getAttribute('id')
    
    if (!id) return
    
    // 解析节点
    if (isFlowNode(tagName)) {
      const node = parseFlowNode(element, doc)
      if (node) {
        nodes.push(node)
      }
    }
    
    // 解析连线
    if (tagName === 'sequenceFlow') {
      const edge = parseSequenceFlow(element)
      if (edge) {
        edges.push(edge)
      }
    }
  })
}

/**
 * 判断是否为流程节点
 */
function isFlowNode(tagName: string): boolean {
  const flowNodeTypes = [
    'startEvent', 'endEvent', 'intermediateThrowEvent', 'intermediateCatchEvent',
    'userTask', 'serviceTask', 'scriptTask', 'manualTask', 'businessRuleTask',
    'exclusiveGateway', 'parallelGateway', 'inclusiveGateway', 'eventBasedGateway',
    'subProcess', 'callActivity'
  ]
  return flowNodeTypes.includes(tagName)
}

/**
 * 解析流程节点
 */
function parseFlowNode(element: Element, doc: Document): FlowNode | null {
  const id = element.getAttribute('id')
  const name = element.getAttribute('name') || ''
  const tagName = element.tagName
  
  if (!id) return null
  
  // 获取图形信息
  const bounds = getBounds(id, doc)
  
  const node: FlowNode = {
    id,
    type: mapBpmnTypeToNodeType(tagName),
    name,
    position: {
      x: bounds.x,
      y: bounds.y
    },
    size: {
      width: bounds.width,
      height: bounds.height
    },
    properties: parseElementProperties(element, tagName)
  }
  
  return node
}

/**
 * 映射BPMN类型到通用节点类型
 */
function mapBpmnTypeToNodeType(bpmnType: string): NodeType {
  const typeMap: Record<string, NodeType> = {
    'startEvent': 'START',
    'endEvent': 'END',
    'userTask': 'USER_TASK',
    'serviceTask': 'SERVICE_TASK',
    'scriptTask': 'SCRIPT_TASK',
    'exclusiveGateway': 'GATEWAY',
    'parallelGateway': 'GATEWAY',
    'inclusiveGateway': 'GATEWAY',
    'eventBasedGateway': 'GATEWAY'
  }
  
  return typeMap[bpmnType] || 'USER_TASK'
}

/**
 * 解析元素属性
 */
function parseElementProperties(element: Element, tagName: string): Record<string, any> {
  const properties: Record<string, any> = {
    bpmnType: tagName
  }
  
  // 通用属性
  const documentation = getElementDocumentation(element)
  if (documentation) {
    properties.documentation = documentation
  }
  
  // 根据类型解析特定属性
  switch (tagName) {
    case 'userTask':
      parseUserTaskProperties(element, properties)
      break
    case 'serviceTask':
      parseServiceTaskProperties(element, properties)
      break
    case 'exclusiveGateway':
    case 'parallelGateway':
    case 'inclusiveGateway':
      parseGatewayProperties(element, properties)
      break
    case 'scriptTask':
      parseScriptTaskProperties(element, properties)
      break
  }
  
  return properties
}

/**
 * 解析用户任务属性
 */
function parseUserTaskProperties(element: Element, properties: Record<string, any>): void {
  const assignee = element.getAttribute('assignee')
  const candidateUsers = element.getAttribute('candidateUsers')
  const candidateGroups = element.getAttribute('candidateGroups')
  const dueDate = element.getAttribute('dueDate')
  const priority = element.getAttribute('priority')
  
  if (assignee) properties.assignee = assignee
  if (candidateUsers) properties.candidateUsers = candidateUsers.split(',')
  if (candidateGroups) properties.candidateGroups = candidateGroups.split(',')
  if (dueDate) properties.dueDate = dueDate
  if (priority) properties.priority = parseInt(priority, 10)
  
  // 确定审批类型
  if (candidateUsers || candidateGroups) {
    properties.approvalType = 'MULTI_OR' // 默认为或签
  } else {
    properties.approvalType = 'SINGLE'
  }
}

/**
 * 解析服务任务属性
 */
function parseServiceTaskProperties(element: Element, properties: Record<string, any>): void {
  const className = element.getAttribute('class')
  const expression = element.getAttribute('expression')
  const delegateExpression = element.getAttribute('delegateExpression')
  const topic = element.getAttribute('topic')
  
  if (className) {
    properties.serviceType = 'JAVA_CLASS'
    properties.serviceConfig = { class: className }
  } else if (expression) {
    properties.serviceType = 'SCRIPT'
    properties.serviceConfig = { expression }
  } else if (delegateExpression) {
    properties.serviceType = 'SCRIPT'
    properties.serviceConfig = { delegateExpression }
  } else if (topic) {
    properties.serviceType = 'HTTP'
    properties.serviceConfig = { topic }
  }
  
  const async = element.getAttribute('async')
  if (async === 'true') {
    properties.async = true
  }
}

/**
 * 解析网关属性
 */
function parseGatewayProperties(element: Element, properties: Record<string, any>): void {
  const tagName = element.tagName
  const defaultFlow = element.getAttribute('default')
  
  let gatewayType = 'EXCLUSIVE'
  if (tagName === 'parallelGateway') {
    gatewayType = 'PARALLEL'
  } else if (tagName === 'inclusiveGateway') {
    gatewayType = 'INCLUSIVE'
  }
  
  properties.gatewayType = gatewayType
  
  if (defaultFlow) {
    properties.defaultFlow = defaultFlow
  }
}

/**
 * 解析脚本任务属性
 */
function parseScriptTaskProperties(element: Element, properties: Record<string, any>): void {
  const scriptFormat = element.getAttribute('scriptFormat')
  const script = element.querySelector('script')?.textContent
  
  if (scriptFormat) properties.scriptFormat = scriptFormat
  if (script) properties.script = script
}

/**
 * 解析序列流
 */
function parseSequenceFlow(element: Element): FlowEdge | null {
  const id = element.getAttribute('id')
  const name = element.getAttribute('name')
  const sourceRef = element.getAttribute('sourceRef')
  const targetRef = element.getAttribute('targetRef')
  
  if (!id || !sourceRef || !targetRef) return null
  
  const edge: FlowEdge = {
    id,
    source: sourceRef,
    target: targetRef
  }
  
  if (name) {
    edge.label = name
  }
  
  // 解析条件表达式
  const conditionExpression = element.querySelector('conditionExpression')
  if (conditionExpression) {
    edge.condition = conditionExpression.textContent || ''
  }
  
  return edge
}

/**
 * 获取元素边界信息
 */
function getBounds(elementId: string, doc: Document): { x: number; y: number; width: number; height: number } {
  const shape = doc.querySelector(`BPMNShape[bpmnElement="${elementId}"]`)
  if (shape) {
    const bounds = shape.querySelector('Bounds')
    if (bounds) {
      return {
        x: parseFloat(bounds.getAttribute('x') || '0'),
        y: parseFloat(bounds.getAttribute('y') || '0'),
        width: parseFloat(bounds.getAttribute('width') || '100'),
        height: parseFloat(bounds.getAttribute('height') || '80')
      }
    }
  }
  
  // 默认值
  return { x: 100, y: 100, width: 100, height: 80 }
}

/**
 * 获取元素文档说明
 */
function getElementDocumentation(element: Element): string | null {
  const documentation = element.querySelector('documentation')
  return documentation?.textContent || null
}

/**
 * 获取流程名称
 */
function getProcessName(doc: Document): string | null {
  const process = doc.querySelector('process')
  return process?.getAttribute('name') || null
}

/**
 * 获取流程文档说明
 */
function getProcessDocumentation(doc: Document): string | null {
  const process = doc.querySelector('process')
  const documentation = process?.querySelector('documentation')
  return documentation?.textContent || null
}

/**
 * 将通用流程定义格式转换为BPMN XML
 */
export function convertFlowDefinitionToBpmn(definition: FlowDefinition): string {
  try {
    const processId = 'Process_' + generateNodeId().substring(5)
    const diagramId = 'BPMNDiagram_' + generateNodeId().substring(5)
    const planeId = 'BPMNPlane_' + generateNodeId().substring(5)
    
    let bpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="${processId}" isExecutable="true">`
    
    if (definition.settings.name) {
      bpmnXml += ` name="${escapeXml(definition.settings.name)}"`
    }
    
    bpmnXml += '>\n'
    
    // 添加文档说明
    if (definition.settings.description) {
      bpmnXml += `    <bpmn:documentation>${escapeXml(definition.settings.description)}</bpmn:documentation>\n`
    }
    
    // 转换节点
    definition.nodes.forEach(node => {
      bpmnXml += convertNodeToBpmn(node)
    })
    
    // 转换连线
    definition.edges.forEach(edge => {
      bpmnXml += convertEdgeToBpmn(edge)
    })
    
    bpmnXml += `  </bpmn:process>
  <bpmndi:BPMNDiagram id="${diagramId}">
    <bpmndi:BPMNPlane id="${planeId}" bpmnElement="${processId}">\n`
    
    // 添加图形信息
    definition.nodes.forEach(node => {
      bpmnXml += convertNodeShape(node)
    })
    
    definition.edges.forEach(edge => {
      bpmnXml += convertEdgeShape(edge, definition.nodes)
    })
    
    bpmnXml += `    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
    
    return bpmnXml
  } catch (error) {
    console.error('流程定义转BPMN失败:', error)
    throw error
  }
}

/**
 * 转换节点为BPMN元素
 */
function convertNodeToBpmn(node: FlowNode): string {
  const bpmnType = mapNodeTypeToBpmn(node.type, node.properties)
  let xml = `    <bpmn:${bpmnType} id="${node.id}"`
  
  if (node.name) {
    xml += ` name="${escapeXml(node.name)}"`
  }
  
  // 添加特定属性
  const attributes = getNodeAttributes(node)
  Object.entries(attributes).forEach(([key, value]) => {
    xml += ` ${key}="${escapeXml(String(value))}"`
  })
  
  xml += '>\n'
  
  // 添加文档说明
  if (node.properties?.documentation) {
    xml += `      <bpmn:documentation>${escapeXml(node.properties.documentation)}</bpmn:documentation>\n`
  }
  
  // 添加特定子元素
  xml += getNodeSubElements(node)
  
  xml += `    </bpmn:${bpmnType}>\n`
  
  return xml
}

/**
 * 映射通用节点类型到BPMN类型
 */
function mapNodeTypeToBpmn(nodeType: NodeType, properties?: Record<string, any>): string {
  switch (nodeType) {
    case 'START':
      return 'startEvent'
    case 'END':
      return 'endEvent'
    case 'USER_TASK':
      return 'userTask'
    case 'SERVICE_TASK':
      return 'serviceTask'
    case 'SCRIPT_TASK':
      return 'scriptTask'
    case 'GATEWAY':
      return properties?.gatewayType === 'PARALLEL' ? 'parallelGateway' : 
             properties?.gatewayType === 'INCLUSIVE' ? 'inclusiveGateway' : 
             'exclusiveGateway'
    default:
      return 'userTask'
  }
}

/**
 * 获取节点属性
 */
function getNodeAttributes(node: FlowNode): Record<string, any> {
  const attributes: Record<string, any> = {}
  const props = node.properties || {}
  
  if (node.type === 'USER_TASK') {
    if (props.assignee) attributes.assignee = props.assignee
    if (props.candidateUsers) attributes.candidateUsers = props.candidateUsers.join(',')
    if (props.candidateGroups) attributes.candidateGroups = props.candidateGroups.join(',')
    if (props.dueDate) attributes.dueDate = props.dueDate
    if (props.priority) attributes.priority = props.priority
  } else if (node.type === 'SERVICE_TASK') {
    const config = props.serviceConfig || {}
    if (config.class) attributes.class = config.class
    if (config.expression) attributes.expression = config.expression
    if (config.delegateExpression) attributes.delegateExpression = config.delegateExpression
    if (config.topic) attributes.topic = config.topic
    if (props.async) attributes.async = 'true'
  } else if (node.type === 'GATEWAY') {
    if (props.defaultFlow) attributes.default = props.defaultFlow
  }
  
  return attributes
}

/**
 * 获取节点子元素
 */
function getNodeSubElements(node: FlowNode): string {
  let xml = ''
  const props = node.properties || {}
  
  if (node.type === 'SCRIPT_TASK') {
    if (props.script) {
      xml += `      <bpmn:script>${escapeXml(props.script)}</bpmn:script>\n`
    }
  }
  
  return xml
}

/**
 * 转换连线为BPMN序列流
 */
function convertEdgeToBpmn(edge: FlowEdge): string {
  let xml = `    <bpmn:sequenceFlow id="${edge.id}" sourceRef="${edge.source}" targetRef="${edge.target}"`
  
  if (edge.label) {
    xml += ` name="${escapeXml(edge.label)}"`
  }
  
  if (edge.condition) {
    xml += '>\n'
    xml += `      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${escapeXml(edge.condition)}</bpmn:conditionExpression>\n`
    xml += '    </bpmn:sequenceFlow>\n'
  } else {
    xml += ' />\n'
  }
  
  return xml
}

/**
 * 转换节点图形信息
 */
function convertNodeShape(node: FlowNode): string {
  return `      <bpmndi:BPMNShape id="_BPMNShape_${node.id}" bpmnElement="${node.id}">
        <dc:Bounds x="${node.position.x}" y="${node.position.y}" width="${node.size.width}" height="${node.size.height}" />
      </bpmndi:BPMNShape>\n`
}

/**
 * 转换连线图形信息
 */
function convertEdgeShape(edge: FlowEdge, nodes: FlowNode[]): string {
  const sourceNode = nodes.find(n => n.id === edge.source)
  const targetNode = nodes.find(n => n.id === edge.target)
  
  if (!sourceNode || !targetNode) return ''
  
  const sourceX = sourceNode.position.x + sourceNode.size.width / 2
  const sourceY = sourceNode.position.y + sourceNode.size.height / 2
  const targetX = targetNode.position.x + targetNode.size.width / 2
  const targetY = targetNode.position.y + targetNode.size.height / 2
  
  return `      <bpmndi:BPMNEdge id="_BPMNEdge_${edge.id}" bpmnElement="${edge.id}">
        <di:waypoint x="${sourceX}" y="${sourceY}" />
        <di:waypoint x="${targetX}" y="${targetY}" />
      </bpmndi:BPMNEdge>\n`
}

/**
 * XML转义
 */
function escapeXml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}
