/**
 * 工作流相关类型定义
 * <AUTHOR>
 * @since 1.0.0
 */

// 设计器类型
export type DesignerType = 'SIMPLE' | 'BPMN'

// 流程状态
export type ProcessStatus = 'DRAFT' | 'ACTIVE' | 'SUSPENDED' | 'DELETED'

// 流程实例状态
export type ProcessInstanceStatus = 'RUNNING' | 'COMPLETED' | 'TERMINATED' | 'SUSPENDED'

// 任务状态
export type TaskStatus = 'CREATED' | 'CLAIMED' | 'COMPLETED' | 'CANCELLED' | 'SUSPENDED'

// 节点类型
export type NodeType = 'START' | 'USER_TASK' | 'SERVICE_TASK' | 'SCRIPT_TASK' | 'GATEWAY' | 'END'

// 审批类型
export type ApprovalType = 'SINGLE' | 'MULTI_AND' | 'MULTI_OR' | 'SEQUENTIAL'

// 网关类型
export type GatewayType = 'EXCLUSIVE' | 'PARALLEL' | 'INCLUSIVE'

// 表单字段类型
export type FormFieldType = 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'upload' | 'number'

// 字段权限类型
export type FieldPermission = 'HIDDEN' | 'READ_ONLY' | 'EDITABLE'

// 流程定义
export interface ProcessDefinition {
  id?: number
  tenantId?: string
  processKey: string
  processName: string
  processVersion?: number
  category?: string
  description?: string
  designerType: DesignerType
  processDefinitionJson: string
  bpmnXml?: string
  formDefinition?: string
  status?: ProcessStatus
  isLatest?: boolean
  createdBy?: number
  createdByName?: string
  createdTime?: string
  updatedBy?: number
  updatedByName?: string
  updatedTime?: string
}

// 流程实例
export interface ProcessInstance {
  id: number
  tenantId: string
  processDefinitionId: number
  processKey: string
  processName: string
  businessKey?: string
  title: string
  initiatorId: number
  initiatorName: string
  currentNodeKeys?: string
  status: ProcessInstanceStatus
  priority: number
  formData?: string
  variables?: string
  startTime: string
  endTime?: string
  duration?: number
  createdTime: string
  updatedTime?: string
}

// 任务实例
export interface TaskInstance {
  id: number
  tenantId: string
  processInstanceId: number
  processDefinitionId: number
  nodeKey: string
  nodeName: string
  nodeType: NodeType
  taskKey: string
  taskName: string
  assigneeId?: number
  assigneeName?: string
  candidateUsers?: string
  candidateGroups?: string
  approvalType: ApprovalType
  status: TaskStatus
  priority: number
  dueDate?: string
  claimTime?: string
  completeTime?: string
  formData?: string
  variables?: string
  createdTime: string
  updatedTime?: string
}

// 流程节点
export interface FlowNode {
  id: string
  type: NodeType
  name: string
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
  properties: Record<string, any>
  style?: Record<string, any>
}

// 流程连线
export interface FlowEdge {
  id: string
  source: string
  target: string
  label?: string
  condition?: string
  style?: Record<string, any>
  points?: Array<{ x: number; y: number }>
}

// 流程定义结构
export interface FlowDefinition {
  nodes: FlowNode[]
  edges: FlowEdge[]
  variables: Record<string, any>
  settings: FlowSettings
}

// 流程设置
export interface FlowSettings {
  name: string
  description?: string
  category?: string
  formSettings?: FormSettings
  notificationSettings?: NotificationSettings
  timeoutSettings?: TimeoutSettings
}

// 表单设置
export interface FormSettings {
  fields: FormField[]
  layout?: FormLayout
  validation?: FormValidation
}

// 表单字段
export interface FormField {
  id: string
  name: string
  label: string
  type: FormFieldType
  placeholder?: string
  required: boolean
  validation?: ValidationRule[]
  options?: Array<{ label: string; value: any }>
  defaultValue?: any
  permissions?: Array<{
    nodeKey: string
    permission: FieldPermission
  }>
}

// 表单布局
export interface FormLayout {
  columns: number
  labelWidth?: number
  fieldSpacing?: number
}

// 表单验证
export interface FormValidation {
  rules: ValidationRule[]
}

// 验证规则
export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
}

// 通知设置
export interface NotificationSettings {
  email?: boolean
  sms?: boolean
  system?: boolean
  templates?: Record<string, string>
}

// 超时设置
export interface TimeoutSettings {
  enabled: boolean
  duration?: number
  action?: 'AUTO_APPROVE' | 'AUTO_REJECT' | 'ESCALATE' | 'NOTIFY'
  escalateToUserId?: number
}

// 节点属性
export interface NodeProperty {
  key: string
  label: string
  type: 'input' | 'select' | 'textarea' | 'user-selector' | 'role-selector' | 'switch' | 'number'
  required: boolean
  options?: Array<{ label: string; value: any }>
  defaultValue?: any
  description?: string
}

// 用户任务节点属性
export interface UserTaskProperties {
  assigneeType: 'USER' | 'ROLE' | 'EXPRESSION'
  assigneeValue: string
  candidateUsers?: string[]
  candidateRoles?: string[]
  approvalType: ApprovalType
  dueDate?: string
  priority?: number
  formFields?: string[]
  notifications?: {
    onCreate?: boolean
    onComplete?: boolean
    onTimeout?: boolean
  }
}

// 网关节点属性
export interface GatewayProperties {
  gatewayType: GatewayType
  conditions?: Array<{
    edgeId: string
    expression: string
    name?: string
  }>
  defaultEdge?: string
}

// 服务任务节点属性
export interface ServiceTaskProperties {
  serviceType: 'HTTP' | 'SCRIPT' | 'JAVA_CLASS'
  serviceConfig: Record<string, any>
  async?: boolean
  timeout?: number
  retryCount?: number
}

// 任务操作
export interface TaskAction {
  action: 'APPROVE' | 'REJECT' | 'TRANSFER' | 'DELEGATE' | 'ADD_SIGN' | 'CANCEL'
  result?: 'APPROVED' | 'REJECTED' | 'TRANSFERRED' | 'DELEGATED'
  comment?: string
  formData?: Record<string, any>
  variables?: Record<string, any>
  targetUserId?: number
  targetNodeKey?: string
}

// 流程启动参数
export interface ProcessStartData {
  processKey: string
  businessKey?: string
  title: string
  formData: Record<string, any>
  variables?: Record<string, any>
}

// 查询参数
export interface QueryParams {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: string
  designerType?: DesignerType
  [key: string]: any
}

// 分页结果
export interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// API响应
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
  traceId?: string
}

// 流程统计
export interface ProcessStatistics {
  total: number
  running: number
  completed: number
  terminated: number
  avgDuration: number
  todayStarted: number
  todayCompleted: number
}

// 任务统计
export interface TaskStatistics {
  todo: number
  done: number
  overdue: number
  avgProcessTime: number
}

// 流程模板
export interface ProcessTemplate {
  id: number
  templateKey: string
  templateName: string
  category?: string
  description?: string
  designerType: DesignerType
  templateDefinition: string
  formTemplate?: string
  isPublic: boolean
  usageCount: number
  createdBy: number
  createdTime: string
}
