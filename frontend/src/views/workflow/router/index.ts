/**
 * 工作流模块路由配置
 * <AUTHOR>
 * @since 1.0.0
 */

import type { RouteRecordRaw } from 'vue-router'

const workflowRoutes: RouteRecordRaw[] = [
  {
    path: '/workflow',
    name: 'Workflow',
    component: () => import('../layouts/WorkflowLayout.vue'),
    meta: {
      title: '工作流管理',
      icon: 'apartment',
      requiresAuth: true
    },
    children: [
      // 工作流管理首页
      {
        path: '',
        name: 'WorkflowManagement',
        component: () => import('../pages/WorkflowManagement.vue'),
        meta: {
          title: '流程管理',
          icon: 'apartment'
        }
      },
      
      // SIMPLE设计器
      {
        path: 'designer/simple/:id?',
        name: 'SimpleDesigner',
        component: () => import('../pages/designer/SimpleDesignerPage.vue'),
        meta: {
          title: 'SIMPLE设计器',
          icon: 'drag',
          hideInMenu: true
        }
      },
      
      // BPMN设计器
      {
        path: 'designer/bpmn/:id?',
        name: 'BpmnDesigner',
        component: () => import('../pages/designer/BpmnDesignerPage.vue'),
        meta: {
          title: 'BPMN设计器',
          icon: 'share-alt',
          hideInMenu: true
        }
      },
      
      // 流程定义管理
      {
        path: 'definitions',
        name: 'ProcessDefinitions',
        component: () => import('../pages/process/ProcessDefinitionPage.vue'),
        meta: {
          title: '流程定义',
          icon: 'file-text'
        }
      },
      
      // 流程实例管理
      {
        path: 'instances',
        name: 'ProcessInstances',
        component: () => import('../pages/process/ProcessInstancePage.vue'),
        meta: {
          title: '流程实例',
          icon: 'play-circle'
        }
      },
      
      // 流程实例详情
      {
        path: 'instances/:id',
        name: 'ProcessInstanceDetail',
        component: () => import('../pages/process/ProcessInstanceDetail.vue'),
        meta: {
          title: '流程详情',
          hideInMenu: true
        }
      },
      
      // 任务管理
      {
        path: 'tasks',
        name: 'TaskManagement',
        redirect: '/workflow/tasks/todo',
        meta: {
          title: '任务管理',
          icon: 'check-circle'
        },
        children: [
          // 待办任务
          {
            path: 'todo',
            name: 'TodoTasks',
            component: () => import('../pages/task/TodoTaskPage.vue'),
            meta: {
              title: '待办任务',
              icon: 'clock-circle'
            }
          },
          
          // 已办任务
          {
            path: 'done',
            name: 'DoneTasks',
            component: () => import('../pages/task/DoneTaskPage.vue'),
            meta: {
              title: '已办任务',
              icon: 'check-circle'
            }
          },
          
          // 抄送任务
          {
            path: 'copy',
            name: 'CopyTasks',
            component: () => import('../pages/task/CopyTaskPage.vue'),
            meta: {
              title: '抄送任务',
              icon: 'copy'
            }
          }
        ]
      },
      
      // 任务详情
      {
        path: 'tasks/:id',
        name: 'TaskDetail',
        component: () => import('../pages/task/TaskDetail.vue'),
        meta: {
          title: '任务详情',
          hideInMenu: true
        }
      },
      
      // 我发起的流程
      {
        path: 'my-processes',
        name: 'MyProcesses',
        component: () => import('../pages/process/MyProcesses.vue'),
        meta: {
          title: '我发起的',
          icon: 'user'
        }
      },
      
      // 流程模板
      {
        path: 'templates',
        name: 'ProcessTemplates',
        component: () => import('../pages/template/ProcessTemplatePage.vue'),
        meta: {
          title: '流程模板',
          icon: 'file-add'
        }
      },
      
      // 模板详情
      {
        path: 'templates/:id',
        name: 'TemplateDetail',
        component: () => import('../pages/template/TemplateDetail.vue'),
        meta: {
          title: '模板详情',
          hideInMenu: true
        }
      },
      
      // 统计报表
      {
        path: 'statistics',
        name: 'WorkflowStatistics',
        component: () => import('../pages/monitor/ProcessStatisticsPage.vue'),
        meta: {
          title: '统计报表',
          icon: 'bar-chart'
        }
      },
      
      // 流程监控
      {
        path: 'monitor',
        name: 'ProcessMonitor',
        component: () => import('../pages/monitor/ProcessMonitorPage.vue'),
        meta: {
          title: '流程监控',
          icon: 'monitor'
        }
      },
      
      // 系统设置
      {
        path: 'settings',
        name: 'WorkflowSettings',
        redirect: '/workflow/settings/general',
        meta: {
          title: '系统设置',
          icon: 'setting'
        },
        children: [
          // 通用设置
          {
            path: 'general',
            name: 'GeneralSettings',
            component: () => import('../pages/settings/GeneralSettings.vue'),
            meta: {
              title: '通用设置',
              icon: 'setting'
            }
          },
          
          // 通知设置
          {
            path: 'notification',
            name: 'NotificationSettings',
            component: () => import('../pages/settings/NotificationSettings.vue'),
            meta: {
              title: '通知设置',
              icon: 'bell'
            }
          },
          
          // 权限设置
          {
            path: 'permission',
            name: 'PermissionSettings',
            component: () => import('../pages/settings/PermissionSettings.vue'),
            meta: {
              title: '权限设置',
              icon: 'safety'
            }
          },
          
          // 表单权限
          {
            path: 'form-permission',
            name: 'FormPermissionSettings',
            component: () => import('../pages/settings/FormPermissionSettings.vue'),
            meta: {
              title: '表单权限',
              icon: 'form'
            }
          }
        ]
      },
      
      // 帮助文档
      {
        path: 'help',
        name: 'WorkflowHelp',
        component: () => import('../pages/help/WorkflowHelp.vue'),
        meta: {
          title: '帮助文档',
          icon: 'question-circle'
        }
      }
    ]
  }
]

export default workflowRoutes

// 导出菜单配置
export const workflowMenus = [
  {
    key: '/workflow',
    icon: 'apartment',
    title: '工作流管理',
    children: [
      {
        key: '/workflow',
        icon: 'apartment',
        title: '流程管理'
      },
      {
        key: '/workflow/definitions',
        icon: 'file-text',
        title: '流程定义'
      },
      {
        key: '/workflow/instances',
        icon: 'play-circle',
        title: '流程实例'
      },
      {
        key: '/workflow/tasks',
        icon: 'check-circle',
        title: '任务管理',
        children: [
          {
            key: '/workflow/tasks/todo',
            icon: 'clock-circle',
            title: '待办任务'
          },
          {
            key: '/workflow/tasks/done',
            icon: 'check-circle',
            title: '已办任务'
          },
          {
            key: '/workflow/tasks/copy',
            icon: 'copy',
            title: '抄送任务'
          }
        ]
      },
      {
        key: '/workflow/my-processes',
        icon: 'user',
        title: '我发起的'
      },
      {
        key: '/workflow/templates',
        icon: 'file-add',
        title: '流程模板'
      },
      {
        key: '/workflow/statistics',
        icon: 'bar-chart',
        title: '统计报表'
      },
      {
        key: '/workflow/monitor',
        icon: 'monitor',
        title: '流程监控'
      },
      {
        key: '/workflow/settings',
        icon: 'setting',
        title: '系统设置',
        children: [
          {
            key: '/workflow/settings/general',
            icon: 'setting',
            title: '通用设置'
          },
          {
            key: '/workflow/settings/notification',
            icon: 'bell',
            title: '通知设置'
          },
          {
            key: '/workflow/settings/permission',
            icon: 'safety',
            title: '权限设置'
          },
          {
            key: '/workflow/settings/form-permission',
            icon: 'form',
            title: '表单权限'
          }
        ]
      },
      {
        key: '/workflow/help',
        icon: 'question-circle',
        title: '帮助文档'
      }
    ]
  }
]

// 导出权限配置
export const workflowPermissions = [
  // 流程定义权限
  'workflow:definition:view',
  'workflow:definition:create',
  'workflow:definition:edit',
  'workflow:definition:delete',
  'workflow:definition:deploy',
  'workflow:definition:suspend',
  
  // 流程实例权限
  'workflow:instance:view',
  'workflow:instance:start',
  'workflow:instance:terminate',
  'workflow:instance:suspend',
  
  // 任务权限
  'workflow:task:view',
  'workflow:task:claim',
  'workflow:task:complete',
  'workflow:task:reject',
  'workflow:task:transfer',
  'workflow:task:delegate',
  'workflow:task:add-sign',
  
  // 模板权限
  'workflow:template:view',
  'workflow:template:create',
  'workflow:template:edit',
  'workflow:template:delete',
  
  // 统计权限
  'workflow:statistics:view',
  
  // 监控权限
  'workflow:monitor:view',
  
  // 设置权限
  'workflow:settings:view',
  'workflow:settings:edit'
]
