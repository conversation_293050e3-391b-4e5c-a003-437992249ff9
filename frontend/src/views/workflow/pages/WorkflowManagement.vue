<template>
  <div class="workflow-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <ApartmentOutlined />
            工作流管理
          </h1>
          <p class="page-description">
            双设计器工作流引擎，支持SIMPLE和BPMN两种设计模式，提供完整的审批流程解决方案
          </p>
        </div>
        <div class="header-right">
          <a-space>
            <a-button type="primary" @click="showCreateModal = true">
              <template #icon>
                <PlusOutlined />
              </template>
              创建流程
            </a-button>
            <a-dropdown>
              <a-button>
                <template #icon>
                  <MoreOutlined />
                </template>
                更多操作
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="import" @click="handleImport">
                    <ImportOutlined />
                    导入流程
                  </a-menu-item>
                  <a-menu-item key="template" @click="handleTemplate">
                    <FileTextOutlined />
                    流程模板
                  </a-menu-item>
                  <a-menu-item key="statistics" @click="handleStatistics">
                    <BarChartOutlined />
                    统计报表
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="流程定义"
              :value="statistics.totalDefinitions"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="运行中实例"
              :value="statistics.runningInstances"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <PlayCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="待办任务"
              :value="statistics.todoTasks"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日完成"
              :value="statistics.todayCompleted"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <a-card>
        <!-- 搜索和筛选 -->
        <div class="search-section">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-input-search
                v-model:value="searchForm.keyword"
                placeholder="搜索流程名称、标识或描述"
                allow-clear
                @search="handleSearch"
              />
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.category"
                placeholder="选择分类"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="HR">人力资源</a-select-option>
                <a-select-option value="FINANCE">财务管理</a-select-option>
                <a-select-option value="PROCUREMENT">采购管理</a-select-option>
                <a-select-option value="PROJECT">项目管理</a-select-option>
                <a-select-option value="OTHER">其他</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.designerType"
                placeholder="设计器类型"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="SIMPLE">SIMPLE设计器</a-select-option>
                <a-select-option value="BPMN">BPMN设计器</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.status"
                placeholder="状态"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="DRAFT">草稿</a-select-option>
                <a-select-option value="ACTIVE">激活</a-select-option>
                <a-select-option value="SUSPENDED">挂起</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 流程定义列表 -->
        <div class="table-section">
          <a-table
            :columns="columns"
            :data-source="processDefinitions"
            :loading="loading"
            :pagination="pagination"
            row-key="id"
            @change="handleTableChange"
          >
            <!-- 设计器类型 -->
            <template #designerType="{ record }">
              <a-tag :color="record.designerType === 'SIMPLE' ? 'blue' : 'green'">
                {{ record.designerType === 'SIMPLE' ? 'SIMPLE设计器' : 'BPMN设计器' }}
              </a-tag>
            </template>

            <!-- 状态 -->
            <template #status="{ record }">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <!-- 操作 -->
            <template #action="{ record }">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="handleDesign(record)"
                >
                  设计
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="handleView(record)"
                >
                  查看
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item
                        v-if="record.status === 'DRAFT'"
                        key="deploy"
                        @click="handleDeploy(record)"
                      >
                        发布
                      </a-menu-item>
                      <a-menu-item
                        v-if="record.status === 'ACTIVE'"
                        key="suspend"
                        @click="handleSuspend(record)"
                      >
                        挂起
                      </a-menu-item>
                      <a-menu-item
                        v-if="record.status === 'SUSPENDED'"
                        key="activate"
                        @click="handleActivate(record)"
                      >
                        激活
                      </a-menu-item>
                      <a-menu-item key="copy" @click="handleCopy(record)">
                        复制
                      </a-menu-item>
                      <a-menu-item key="export" @click="handleExport(record)">
                        导出
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        key="delete"
                        danger
                        @click="handleDelete(record)"
                      >
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 创建流程弹窗 -->
    <CreateProcessModal
      v-model:visible="showCreateModal"
      @success="handleCreateSuccess"
    />

    <!-- 流程设计器弹窗 -->
    <ProcessDesignerModal
      v-model:visible="showDesignerModal"
      :process-definition="currentProcess"
      @success="handleDesignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  ApartmentOutlined,
  PlusOutlined,
  MoreOutlined,
  DownOutlined,
  ImportOutlined,
  FileTextOutlined,
  BarChartOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import type { ProcessDefinition, QueryParams } from '../types/workflow'
import { processDefinitionApi, statisticsApi } from '../api/workflow'
import CreateProcessModal from '../components/modals/CreateProcessModal.vue'
import ProcessDesignerModal from '../components/modals/ProcessDesignerModal.vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const showDesignerModal = ref(false)
const currentProcess = ref<ProcessDefinition | null>(null)
const processDefinitions = ref<ProcessDefinition[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: undefined,
  designerType: undefined,
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 统计数据
const statistics = reactive({
  totalDefinitions: 0,
  runningInstances: 0,
  todoTasks: 0,
  todayCompleted: 0
})

// 表格列配置
const columns = [
  {
    title: '流程名称',
    dataIndex: 'processName',
    key: 'processName',
    ellipsis: true
  },
  {
    title: '流程标识',
    dataIndex: 'processKey',
    key: 'processKey',
    width: 150
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    width: 100
  },
  {
    title: '设计器类型',
    dataIndex: 'designerType',
    key: 'designerType',
    width: 120,
    slots: { customRender: 'designerType' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '版本',
    dataIndex: 'processVersion',
    key: 'processVersion',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    slots: { customRender: 'action' }
  }
]

// 生命周期
onMounted(() => {
  loadProcessDefinitions()
  loadStatistics()
})

// 方法
const loadProcessDefinitions = async () => {
  loading.value = true
  try {
    const params: QueryParams = {
      page: pagination.current,
      size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await processDefinitionApi.getList(params)
    if (response.code === 200) {
      processDefinitions.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载流程定义失败:', error)
    message.error('加载流程定义失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const [overviewRes, taskRes] = await Promise.all([
      statisticsApi.getOverview(),
      statisticsApi.getTaskStatistics()
    ])
    
    if (overviewRes.code === 200) {
      Object.assign(statistics, overviewRes.data)
    }
    
    if (taskRes.code === 200) {
      statistics.todoTasks = taskRes.data.todo
      statistics.todayCompleted = taskRes.data.todayCompleted || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadProcessDefinitions()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadProcessDefinitions()
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  loadProcessDefinitions()
  loadStatistics()
}

const handleDesign = (record: ProcessDefinition) => {
  // 根据设计器类型跳转到对应的设计器页面
  if (record.designerType === 'SIMPLE') {
    router.push(`/workflow/designer/simple/${record.id}`)
  } else {
    router.push(`/workflow/designer/bpmn/${record.id}`)
  }
}

const handleView = (record: ProcessDefinition) => {
  router.push(`/workflow/process/${record.id}`)
}

const handleDeploy = async (record: ProcessDefinition) => {
  Modal.confirm({
    title: '确认发布',
    content: `确定要发布流程"${record.processName}"吗？发布后流程将生效。`,
    onOk: async () => {
      try {
        const response = await processDefinitionApi.deploy(record.id!)
        if (response.code === 200) {
          message.success('发布成功')
          loadProcessDefinitions()
          loadStatistics()
        }
      } catch (error) {
        console.error('发布失败:', error)
        message.error('发布失败')
      }
    }
  })
}

const handleSuspend = async (record: ProcessDefinition) => {
  try {
    const response = await processDefinitionApi.suspend(record.id!)
    if (response.code === 200) {
      message.success('挂起成功')
      loadProcessDefinitions()
    }
  } catch (error) {
    console.error('挂起失败:', error)
    message.error('挂起失败')
  }
}

const handleActivate = async (record: ProcessDefinition) => {
  try {
    const response = await processDefinitionApi.activate(record.id!)
    if (response.code === 200) {
      message.success('激活成功')
      loadProcessDefinitions()
    }
  } catch (error) {
    console.error('激活失败:', error)
    message.error('激活失败')
  }
}

const handleCopy = (record: ProcessDefinition) => {
  // TODO: 实现复制功能
  message.info('复制功能开发中...')
}

const handleExport = (record: ProcessDefinition) => {
  // TODO: 实现导出功能
  message.info('导出功能开发中...')
}

const handleDelete = (record: ProcessDefinition) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除流程"${record.processName}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await processDefinitionApi.delete(record.id!)
        if (response.code === 200) {
          message.success('删除成功')
          loadProcessDefinitions()
          loadStatistics()
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

const handleImport = () => {
  message.info('导入功能开发中...')
}

const handleTemplate = () => {
  router.push('/workflow/templates')
}

const handleStatistics = () => {
  router.push('/workflow/statistics')
}

const handleDesignSuccess = () => {
  showDesignerModal.value = false
  loadProcessDefinitions()
}

// 工具函数
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    DRAFT: 'default',
    ACTIVE: 'success',
    SUSPENDED: 'warning',
    DELETED: 'error'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    DRAFT: '草稿',
    ACTIVE: '激活',
    SUSPENDED: '挂起',
    DELETED: '已删除'
  }
  return textMap[status] || status
}
</script>

<style lang="less" scoped>
.workflow-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .statistics-cards {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .main-content {
    .search-section {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    .table-section {
      .ant-table {
        background: #fff;
      }
    }
  }
}
</style>
