<template>
  <div class="simple-designer-page">
    <!-- 页面加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中...">
        <div class="loading-content" />
      </a-spin>
    </div>

    <!-- SIMPLE设计器 -->
    <SimpleDesigner
      v-else
      ref="designerRef"
      :process-definition="processDefinition"
      :readonly="readonly"
      @save="handleSave"
      @deploy="handleDeploy"
      @change="handleChange"
    />

    <!-- 保存确认弹窗 -->
    <a-modal
      v-model:visible="saveModalVisible"
      title="保存流程定义"
      :confirm-loading="saving"
      @ok="confirmSave"
    >
      <a-form layout="vertical" :model="saveForm">
        <a-form-item
          label="流程标识"
          name="processKey"
          :rules="[{ required: true, message: '请输入流程标识' }]"
        >
          <a-input
            v-model:value="saveForm.processKey"
            placeholder="请输入流程标识"
            :disabled="!!processDefinition.id"
          />
        </a-form-item>
        
        <a-form-item
          label="流程名称"
          name="processName"
          :rules="[{ required: true, message: '请输入流程名称' }]"
        >
          <a-input
            v-model:value="saveForm.processName"
            placeholder="请输入流程名称"
          />
        </a-form-item>
        
        <a-form-item label="流程分类" name="category">
          <a-select
            v-model:value="saveForm.category"
            placeholder="请选择流程分类"
            allow-clear
          >
            <a-select-option value="HR">人力资源</a-select-option>
            <a-select-option value="FINANCE">财务管理</a-select-option>
            <a-select-option value="PROCUREMENT">采购管理</a-select-option>
            <a-select-option value="PROJECT">项目管理</a-select-option>
            <a-select-option value="OTHER">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="流程描述" name="description">
          <a-textarea
            v-model:value="saveForm.description"
            placeholder="请输入流程描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { ProcessDefinition, FlowDefinition } from '../../types/workflow'
import { processDefinitionApi } from '../../api/workflow'
import { generateProcessKey } from '../../utils/id-generator'
import SimpleDesigner from '../../components/designers/SimpleDesigner/index.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const saveModalVisible = ref(false)
const designerRef = ref()

// 流程定义
const processDefinition = reactive<ProcessDefinition>({
  processKey: '',
  processName: '',
  designerType: 'SIMPLE',
  processDefinitionJson: '',
  bpmnXml: ''
})

// 保存表单
const saveForm = reactive({
  processKey: '',
  processName: '',
  category: '',
  description: ''
})

// 计算属性
const readonly = computed(() => {
  // 根据权限或状态判断是否只读
  return processDefinition.status === 'ACTIVE'
})

// 生命周期
onMounted(async () => {
  await loadProcessDefinition()
})

// 方法
const loadProcessDefinition = async () => {
  const id = route.params.id as string
  
  if (id && id !== 'new') {
    try {
      const response = await processDefinitionApi.getById(Number(id))
      if (response.code === 200) {
        Object.assign(processDefinition, response.data)
        
        // 初始化保存表单
        Object.assign(saveForm, {
          processKey: response.data.processKey,
          processName: response.data.processName,
          category: response.data.category,
          description: response.data.description
        })
      } else {
        message.error('加载流程定义失败')
        router.push('/workflow')
      }
    } catch (error) {
      console.error('加载流程定义失败:', error)
      message.error('加载流程定义失败')
      router.push('/workflow')
    }
  } else {
    // 新建流程
    processDefinition.processKey = generateProcessKey('新建流程')
    processDefinition.processName = '新建流程'
    
    // 初始化保存表单
    Object.assign(saveForm, {
      processKey: processDefinition.processKey,
      processName: processDefinition.processName,
      category: '',
      description: ''
    })
  }
  
  loading.value = false
}

const handleSave = () => {
  // 更新保存表单
  Object.assign(saveForm, {
    processKey: processDefinition.processKey || generateProcessKey(processDefinition.processName),
    processName: processDefinition.processName,
    category: processDefinition.category,
    description: processDefinition.description
  })
  
  saveModalVisible.value = true
}

const confirmSave = async () => {
  if (!saveForm.processKey || !saveForm.processName) {
    message.warning('请填写必要信息')
    return
  }
  
  saving.value = true
  
  try {
    // 更新流程定义
    Object.assign(processDefinition, {
      processKey: saveForm.processKey,
      processName: saveForm.processName,
      category: saveForm.category,
      description: saveForm.description
    })
    
    let response
    if (processDefinition.id) {
      // 更新现有流程
      response = await processDefinitionApi.update(processDefinition.id, processDefinition)
    } else {
      // 创建新流程
      response = await processDefinitionApi.create(processDefinition)
    }
    
    if (response.code === 200) {
      message.success('保存成功')
      Object.assign(processDefinition, response.data)
      saveModalVisible.value = false
      
      // 如果是新建流程，跳转到编辑页面
      if (!route.params.id || route.params.id === 'new') {
        router.replace(`/workflow/designer/simple/${response.data.id}`)
      }
    } else {
      message.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleDeploy = async () => {
  if (!processDefinition.id) {
    message.warning('请先保存流程定义')
    return
  }
  
  try {
    const response = await processDefinitionApi.deploy(processDefinition.id)
    if (response.code === 200) {
      message.success('发布成功')
      Object.assign(processDefinition, response.data)
    } else {
      message.error(response.message || '发布失败')
    }
  } catch (error) {
    console.error('发布失败:', error)
    message.error('发布失败')
  }
}

const handleChange = (definition: FlowDefinition) => {
  processDefinition.processDefinitionJson = JSON.stringify(definition)
  
  // 自动保存（可选）
  // autoSave()
}

// 自动保存功能（可选）
let autoSaveTimer: NodeJS.Timeout | null = null

const autoSave = () => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  
  autoSaveTimer = setTimeout(async () => {
    if (processDefinition.id && processDefinition.processDefinitionJson) {
      try {
        await processDefinitionApi.update(processDefinition.id, processDefinition)
        console.log('自动保存成功')
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    }
  }, 5000) // 5秒后自动保存
}

// 页面离开前确认
window.addEventListener('beforeunload', (event) => {
  if (processDefinition.processDefinitionJson && !processDefinition.id) {
    event.preventDefault()
    event.returnValue = '您有未保存的更改，确定要离开吗？'
  }
})
</script>

<style lang="less" scoped>
.simple-designer-page {
  height: 100vh;
  background: #f5f5f5;

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;

    .loading-content {
      width: 200px;
      height: 100px;
    }
  }
}
</style>
