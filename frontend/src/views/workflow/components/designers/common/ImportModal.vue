<template>
  <a-modal
    v-model:visible="visible"
    title="导入流程"
    :confirm-loading="loading"
    @ok="handleImport"
    @cancel="handleCancel"
  >
    <div class="import-modal">
      <!-- 文件上传 -->
      <a-upload-dragger
        v-model:file-list="fileList"
        :accept="accept"
        :before-upload="beforeUpload"
        :show-upload-list="false"
        @change="handleFileChange"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持 {{ acceptText }} 格式文件
        </p>
      </a-upload-dragger>

      <!-- 文件信息 -->
      <div v-if="selectedFile" class="file-info">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="文件名">
            {{ selectedFile.name }}
          </a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ formatFileSize(selectedFile.size) }}
          </a-descriptions-item>
          <a-descriptions-item label="文件类型">
            {{ selectedFile.type || '未知' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 导入选项 -->
      <div v-if="selectedFile" class="import-options">
        <a-form layout="vertical" :model="importForm">
          <a-form-item label="导入模式">
            <a-radio-group v-model:value="importForm.mode">
              <a-radio value="replace">替换当前流程</a-radio>
              <a-radio value="merge">合并到当前流程</a-radio>
              <a-radio value="new">创建新流程</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item v-if="importForm.mode === 'new'" label="新流程名称">
            <a-input
              v-model:value="importForm.newProcessName"
              placeholder="请输入新流程名称"
            />
          </a-form-item>
          
          <a-form-item label="导入选项">
            <a-checkbox-group v-model:value="importForm.options">
              <a-checkbox value="preserveIds">保留原始ID</a-checkbox>
              <a-checkbox value="importVariables">导入流程变量</a-checkbox>
              <a-checkbox value="importFormData">导入表单数据</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>

      <!-- 预览内容 -->
      <div v-if="previewContent" class="preview-content">
        <a-collapse>
          <a-collapse-panel key="preview" header="预览内容">
            <pre class="preview-text">{{ previewContent }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import type { UploadFile } from 'ant-design-vue'

// Props
interface Props {
  visible: boolean
  accept?: string
}

const props = withDefaults(defineProps<Props>(), {
  accept: '.json,.bpmn,.xml'
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'import-success': [content: string, options: any]
}>()

// 响应式数据
const loading = ref(false)
const fileList = ref<UploadFile[]>([])
const selectedFile = ref<File | null>(null)
const previewContent = ref('')

// 导入表单
const importForm = reactive({
  mode: 'replace',
  newProcessName: '',
  options: ['preserveIds']
})

// 计算属性
const acceptText = computed(() => {
  const extensions = props.accept.split(',').map(ext => ext.trim().replace('.', '').toUpperCase())
  return extensions.join('、')
})

// 监听visible变化
watch(
  () => props.visible,
  (newVisible) => {
    if (!newVisible) {
      resetForm()
    }
  }
)

// 方法
const beforeUpload = (file: File) => {
  // 检查文件类型
  const acceptedTypes = props.accept.split(',').map(type => type.trim())
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  
  if (!acceptedTypes.includes(fileExtension)) {
    message.error(`不支持的文件类型，请选择 ${acceptText.value} 格式的文件`)
    return false
  }
  
  // 检查文件大小（限制为10MB）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  selectedFile.value = file
  readFileContent(file)
  
  return false // 阻止自动上传
}

const handleFileChange = (info: any) => {
  // 文件变化处理
}

const readFileContent = (file: File) => {
  const reader = new FileReader()
  
  reader.onload = (e) => {
    const content = e.target?.result as string
    previewContent.value = content
    
    // 验证文件内容
    validateFileContent(content, file.name)
  }
  
  reader.onerror = () => {
    message.error('文件读取失败')
  }
  
  reader.readAsText(file)
}

const validateFileContent = (content: string, fileName: string) => {
  try {
    const extension = fileName.split('.').pop()?.toLowerCase()
    
    if (extension === 'json') {
      // 验证JSON格式
      JSON.parse(content)
    } else if (extension === 'bpmn' || extension === 'xml') {
      // 验证XML格式
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'application/xml')
      const parseError = doc.querySelector('parsererror')
      
      if (parseError) {
        throw new Error('XML格式错误')
      }
      
      // 检查是否为BPMN文件
      if (extension === 'bpmn') {
        const definitions = doc.querySelector('definitions')
        if (!definitions) {
          throw new Error('不是有效的BPMN文件')
        }
      }
    }
    
    message.success('文件验证通过')
  } catch (error) {
    message.error(`文件格式错误: ${error}`)
    selectedFile.value = null
    previewContent.value = ''
  }
}

const handleImport = async () => {
  if (!selectedFile.value || !previewContent.value) {
    message.warning('请选择要导入的文件')
    return
  }
  
  if (importForm.mode === 'new' && !importForm.newProcessName) {
    message.warning('请输入新流程名称')
    return
  }
  
  loading.value = true
  
  try {
    // 处理导入逻辑
    const importOptions = {
      mode: importForm.mode,
      newProcessName: importForm.newProcessName,
      options: importForm.options,
      fileName: selectedFile.value.name
    }
    
    emit('import-success', previewContent.value, importOptions)
    emit('update:visible', false)
    
    message.success('导入成功')
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

const resetForm = () => {
  fileList.value = []
  selectedFile.value = null
  previewContent.value = ''
  
  Object.assign(importForm, {
    mode: 'replace',
    newProcessName: '',
    options: ['preserveIds']
  })
}

const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }
}
</script>

<style lang="less" scoped>
.import-modal {
  .file-info {
    margin: 16px 0;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .import-options {
    margin: 16px 0;
  }
  
  .preview-content {
    margin-top: 16px;
    
    .preview-text {
      max-height: 200px;
      overflow-y: auto;
      font-size: 12px;
      line-height: 1.4;
      background: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
    }
  }
}

:deep(.ant-upload-dragger) {
  .ant-upload-drag-icon {
    font-size: 48px;
    color: #1890ff;
  }
  
  .ant-upload-text {
    font-size: 16px;
    color: #666;
  }
  
  .ant-upload-hint {
    font-size: 14px;
    color: #999;
  }
}
</style>
