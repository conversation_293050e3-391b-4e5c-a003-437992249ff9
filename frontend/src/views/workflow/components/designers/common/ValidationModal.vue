<template>
  <a-modal
    v-model:visible="visible"
    title="流程验证结果"
    :footer="null"
    width="800px"
  >
    <div class="validation-modal">
      <!-- 验证概览 -->
      <div class="validation-summary">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="验证状态"
              :value="validationResult?.valid ? '通过' : '失败'"
              :value-style="{ 
                color: validationResult?.valid ? '#52c41a' : '#ff4d4f' 
              }"
            >
              <template #prefix>
                <CheckCircleOutlined v-if="validationResult?.valid" />
                <CloseCircleOutlined v-else />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="错误数量"
              :value="errorCount"
              :value-style="{ color: errorCount > 0 ? '#ff4d4f' : '#52c41a' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="警告数量"
              :value="warningCount"
              :value-style="{ color: warningCount > 0 ? '#faad14' : '#52c41a' }"
            >
              <template #prefix>
                <WarningOutlined />
              </template>
            </a-statistic>
          </a-col>
        </a-row>
      </div>

      <!-- 验证详情 -->
      <div class="validation-details">
        <a-tabs v-model:activeKey="activeTab">
          <!-- 错误列表 -->
          <a-tab-pane key="errors" :tab="`错误 (${errorCount})`">
            <div v-if="errors.length === 0" class="empty-state">
              <a-empty description="没有发现错误" />
            </div>
            <div v-else class="issue-list">
              <div
                v-for="(error, index) in errors"
                :key="index"
                class="issue-item error-item"
              >
                <div class="issue-header">
                  <ExclamationCircleOutlined class="issue-icon error-icon" />
                  <span class="issue-code">{{ error.code }}</span>
                  <a-tag color="red" size="small">错误</a-tag>
                </div>
                <div class="issue-message">{{ error.message }}</div>
                <div v-if="error.nodeId || error.edgeId" class="issue-location">
                  <span v-if="error.nodeId">节点: {{ error.nodeId }}</span>
                  <span v-if="error.edgeId">连线: {{ error.edgeId }}</span>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <!-- 警告列表 -->
          <a-tab-pane key="warnings" :tab="`警告 (${warningCount})`">
            <div v-if="warnings.length === 0" class="empty-state">
              <a-empty description="没有发现警告" />
            </div>
            <div v-else class="issue-list">
              <div
                v-for="(warning, index) in warnings"
                :key="index"
                class="issue-item warning-item"
              >
                <div class="issue-header">
                  <WarningOutlined class="issue-icon warning-icon" />
                  <span class="issue-code">{{ warning.code }}</span>
                  <a-tag color="orange" size="small">警告</a-tag>
                </div>
                <div class="issue-message">{{ warning.message }}</div>
                <div v-if="warning.nodeId || warning.edgeId" class="issue-location">
                  <span v-if="warning.nodeId">节点: {{ warning.nodeId }}</span>
                  <span v-if="warning.edgeId">连线: {{ warning.edgeId }}</span>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <!-- 验证规则 -->
          <a-tab-pane key="rules" tab="验证规则">
            <div class="validation-rules">
              <a-collapse>
                <a-collapse-panel key="structure" header="结构验证">
                  <ul>
                    <li>流程必须包含开始事件</li>
                    <li>建议包含结束事件</li>
                    <li>所有节点必须有唯一的ID</li>
                    <li>序列流必须连接有效的节点</li>
                    <li>不能存在孤立的节点</li>
                  </ul>
                </a-collapse-panel>
                
                <a-collapse-panel key="business" header="业务规则验证">
                  <ul>
                    <li>用户任务必须配置处理人</li>
                    <li>服务任务必须配置实现方式</li>
                    <li>排他网关建议配置条件表达式</li>
                    <li>流程应标记为可执行</li>
                    <li>避免无限循环</li>
                  </ul>
                </a-collapse-panel>
                
                <a-collapse-panel key="naming" header="命名规范">
                  <ul>
                    <li>节点名称应该清晰明确</li>
                    <li>ID应该遵循命名规范</li>
                    <li>避免使用特殊字符</li>
                    <li>建议使用英文或拼音</li>
                  </ul>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 操作按钮 -->
      <div class="validation-actions">
        <a-space>
          <a-button @click="handleExport">
            <template #icon>
              <ExportOutlined />
            </template>
            导出报告
          </a-button>
          <a-button type="primary" @click="handleClose">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import type { ValidationResult } from '../../../utils/workflow-validator'

// Props
interface Props {
  visible: boolean
  validationResult?: ValidationResult | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
}>()

// 响应式数据
const activeTab = ref('errors')

// 计算属性
const errors = computed(() => props.validationResult?.errors || [])
const warnings = computed(() => props.validationResult?.warnings || [])
const errorCount = computed(() => errors.value.length)
const warningCount = computed(() => warnings.value.length)

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const handleExport = () => {
  if (!props.validationResult) {
    message.warning('没有验证结果可导出')
    return
  }
  
  try {
    const report = generateValidationReport(props.validationResult)
    const dataStr = JSON.stringify(report, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `validation-report-${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    message.success('验证报告导出成功')
  } catch (error) {
    console.error('导出验证报告失败:', error)
    message.error('导出失败')
  }
}

const generateValidationReport = (result: ValidationResult) => {
  return {
    timestamp: new Date().toISOString(),
    summary: {
      valid: result.valid,
      errorCount: result.errors.length,
      warningCount: result.warnings.length
    },
    errors: result.errors.map(error => ({
      type: error.type,
      code: error.code,
      message: error.message,
      nodeId: error.nodeId,
      edgeId: error.edgeId
    })),
    warnings: result.warnings.map(warning => ({
      type: warning.type,
      code: warning.code,
      message: warning.message,
      nodeId: warning.nodeId,
      edgeId: warning.edgeId
    }))
  }
}
</script>

<style lang="less" scoped>
.validation-modal {
  .validation-summary {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .validation-details {
    margin-bottom: 24px;

    .empty-state {
      padding: 40px 0;
      text-align: center;
    }

    .issue-list {
      max-height: 400px;
      overflow-y: auto;

      .issue-item {
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 6px;
        border-left: 4px solid;

        &.error-item {
          background: #fff2f0;
          border-left-color: #ff4d4f;
        }

        &.warning-item {
          background: #fffbe6;
          border-left-color: #faad14;
        }

        .issue-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .issue-icon {
            font-size: 16px;

            &.error-icon {
              color: #ff4d4f;
            }

            &.warning-icon {
              color: #faad14;
            }
          }

          .issue-code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #666;
          }
        }

        .issue-message {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
        }

        .issue-location {
          font-size: 12px;
          color: #999;

          span {
            margin-right: 12px;
          }
        }
      }
    }

    .validation-rules {
      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
          color: #666;
        }
      }
    }
  }

  .validation-actions {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

:deep(.ant-statistic-content) {
  font-size: 18px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
}
</style>
