<template>
  <div class="node-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <h3>组件面板</h3>
      <a-button
        type="text"
        size="small"
        :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
        @click="toggleCollapse"
      />
    </div>

    <!-- 面板内容 -->
    <div v-show="!collapsed" class="panel-content">
      <!-- 搜索框 -->
      <div class="search-box">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索组件"
          allow-clear
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
      </div>

      <!-- 节点分组 -->
      <div class="node-groups">
        <!-- 基础节点 -->
        <div class="node-group">
          <div class="group-title">
            <span>基础节点</span>
          </div>
          <div class="group-content">
            <div
              v-for="nodeType in filteredBasicNodes"
              :key="nodeType.id"
              class="node-item"
              :draggable="true"
              @dragstart="handleDragStart($event, nodeType)"
              @click="handleNodeClick(nodeType)"
            >
              <div class="node-icon">
                <component :is="getNodeIcon(nodeType.icon)" />
              </div>
              <div class="node-info">
                <div class="node-name">{{ nodeType.name }}</div>
                <div class="node-desc">{{ nodeType.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 高级节点 -->
        <div class="node-group">
          <div class="group-title">
            <span>高级节点</span>
          </div>
          <div class="group-content">
            <div
              v-for="nodeType in filteredAdvancedNodes"
              :key="nodeType.id"
              class="node-item"
              :draggable="true"
              @dragstart="handleDragStart($event, nodeType)"
              @click="handleNodeClick(nodeType)"
            >
              <div class="node-icon">
                <component :is="getNodeIcon(nodeType.icon)" />
              </div>
              <div class="node-info">
                <div class="node-name">{{ nodeType.name }}</div>
                <div class="node-desc">{{ nodeType.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义节点 -->
        <div v-if="customNodes.length > 0" class="node-group">
          <div class="group-title">
            <span>自定义节点</span>
            <a-button
              type="text"
              size="small"
              @click="showCustomNodeDialog = true"
            >
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button>
          </div>
          <div class="group-content">
            <div
              v-for="nodeType in filteredCustomNodes"
              :key="nodeType.id"
              class="node-item"
              :draggable="true"
              @dragstart="handleDragStart($event, nodeType)"
              @click="handleNodeClick(nodeType)"
            >
              <div class="node-icon">
                <component :is="getNodeIcon(nodeType.icon)" />
              </div>
              <div class="node-info">
                <div class="node-name">{{ nodeType.name }}</div>
                <div class="node-desc">{{ nodeType.description }}</div>
              </div>
              <div class="node-actions">
                <a-button
                  type="text"
                  size="small"
                  @click.stop="editCustomNode(nodeType)"
                >
                  <EditOutlined />
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  danger
                  @click.stop="deleteCustomNode(nodeType.id)"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="usage-tips">
        <a-alert
          message="使用提示"
          description="拖拽节点到画布中创建流程，点击节点可查看详细信息"
          type="info"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <!-- 自定义节点对话框 -->
    <CustomNodeDialog
      v-model:visible="showCustomNodeDialog"
      :node-data="editingCustomNode"
      @save="handleCustomNodeSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  UserOutlined,
  ApiOutlined,
  ForkOutlined,
  StopOutlined,
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { NodeType } from '../../../../types/workflow'
import CustomNodeDialog from './CustomNodeDialog.vue'

// Props
interface Props {
  nodeTypes: Array<{
    id: string
    type: NodeType
    name: string
    icon: string
    description: string
    category?: string
  }>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  dragStart: [event: DragEvent, nodeType: any]
  nodeClick: [nodeType: any]
}>()

// 响应式数据
const collapsed = ref(false)
const searchKeyword = ref('')
const showCustomNodeDialog = ref(false)
const editingCustomNode = ref(null)
const customNodes = ref([
  {
    id: 'timer',
    type: 'SERVICE_TASK' as NodeType,
    name: '定时器',
    icon: 'clock-circle',
    description: '定时触发任务',
    category: 'custom'
  },
  {
    id: 'email',
    type: 'SERVICE_TASK' as NodeType,
    name: '邮件通知',
    icon: 'mail',
    description: '发送邮件通知',
    category: 'custom'
  },
  {
    id: 'sms',
    type: 'SERVICE_TASK' as NodeType,
    name: '短信通知',
    icon: 'phone',
    description: '发送短信通知',
    category: 'custom'
  },
  {
    id: 'document',
    type: 'SERVICE_TASK' as NodeType,
    name: '文档生成',
    icon: 'file-text',
    description: '自动生成文档',
    category: 'custom'
  }
])

// 计算属性
const filteredBasicNodes = computed(() => {
  return props.nodeTypes.filter(node => 
    !node.category || node.category === 'basic'
  ).filter(node =>
    !searchKeyword.value || 
    node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredAdvancedNodes = computed(() => {
  const advancedTypes = ['GATEWAY', 'SERVICE_TASK', 'SCRIPT_TASK']
  return props.nodeTypes.filter(node => 
    advancedTypes.includes(node.type)
  ).filter(node =>
    !searchKeyword.value || 
    node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredCustomNodes = computed(() => {
  return customNodes.value.filter(node =>
    !searchKeyword.value || 
    node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

const getNodeIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'play-circle': PlayCircleOutlined,
    'user': UserOutlined,
    'api': ApiOutlined,
    'fork': ForkOutlined,
    'stop': StopOutlined,
    'clock-circle': ClockCircleOutlined,
    'mail': MailOutlined,
    'phone': PhoneOutlined,
    'file-text': FileTextOutlined
  }
  return iconMap[iconName] || ApiOutlined
}

const handleDragStart = (event: DragEvent, nodeType: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('nodeType', nodeType.type)
    event.dataTransfer.setData('nodeData', JSON.stringify(nodeType))
    event.dataTransfer.effectAllowed = 'copy'
  }
  emit('dragStart', event, nodeType)
}

const handleNodeClick = (nodeType: any) => {
  emit('nodeClick', nodeType)
}

const editCustomNode = (nodeType: any) => {
  editingCustomNode.value = { ...nodeType }
  showCustomNodeDialog.value = true
}

const deleteCustomNode = (nodeId: string) => {
  const index = customNodes.value.findIndex(node => node.id === nodeId)
  if (index > -1) {
    customNodes.value.splice(index, 1)
    message.success('删除成功')
  }
}

const handleCustomNodeSave = (nodeData: any) => {
  if (editingCustomNode.value) {
    // 编辑现有节点
    const index = customNodes.value.findIndex(node => node.id === nodeData.id)
    if (index > -1) {
      customNodes.value[index] = nodeData
      message.success('更新成功')
    }
  } else {
    // 添加新节点
    customNodes.value.push({
      ...nodeData,
      id: `custom_${Date.now()}`,
      category: 'custom'
    })
    message.success('添加成功')
  }
  
  editingCustomNode.value = null
  showCustomNodeDialog.value = false
}
</script>

<style lang="less" scoped>
.node-panel {
  width: 240px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .search-box {
      margin-bottom: 16px;
    }

    .node-groups {
      .node-group {
        margin-bottom: 24px;

        .group-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          font-size: 12px;
          font-weight: 500;
          color: #666;
          text-transform: uppercase;
        }

        .group-content {
          .node-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.2s;
            position: relative;

            &:hover {
              border-color: #1890ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);

              .node-actions {
                opacity: 1;
              }
            }

            &:active {
              cursor: grabbing;
            }

            .node-icon {
              flex: 0 0 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 8px;
              color: #1890ff;
              font-size: 16px;
            }

            .node-info {
              flex: 1;
              min-width: 0;

              .node-name {
                font-size: 13px;
                font-weight: 500;
                color: #333;
                margin-bottom: 2px;
              }

              .node-desc {
                font-size: 11px;
                color: #999;
                line-height: 1.2;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .node-actions {
              position: absolute;
              top: 4px;
              right: 4px;
              opacity: 0;
              transition: opacity 0.2s;
              display: flex;
              gap: 2px;
            }
          }
        }
      }
    }

    .usage-tips {
      margin-top: 24px;
    }
  }
}

// 折叠状态
.node-panel.collapsed {
  width: 48px;

  .panel-content {
    display: none;
  }
}
</style>
