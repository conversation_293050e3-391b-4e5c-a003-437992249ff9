<template>
  <div class="designer-toolbar">
    <!-- 左侧：基本信息 -->
    <div class="toolbar-left">
      <div class="process-info">
        <a-input
          v-model:value="localProcessDefinition.processName"
          placeholder="请输入流程名称"
          class="process-name-input"
          :disabled="readonly"
          @blur="handleProcessNameChange"
        />
        <a-select
          v-model:value="localProcessDefinition.category"
          placeholder="选择分类"
          class="category-select"
          :disabled="readonly"
          allow-clear
          @change="handleCategoryChange"
        >
          <a-select-option value="HR">人力资源</a-select-option>
          <a-select-option value="FINANCE">财务管理</a-select-option>
          <a-select-option value="PROCUREMENT">采购管理</a-select-option>
          <a-select-option value="PROJECT">项目管理</a-select-option>
          <a-select-option value="OTHER">其他</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 中间：操作按钮 -->
    <div class="toolbar-center">
      <a-space>
        <!-- 撤销重做 -->
        <a-button-group>
          <a-button
            :disabled="!canUndo || readonly"
            @click="handleUndo"
          >
            <template #icon>
              <UndoOutlined />
            </template>
            撤销
          </a-button>
          <a-button
            :disabled="!canRedo || readonly"
            @click="handleRedo"
          >
            <template #icon>
              <RedoOutlined />
            </template>
            重做
          </a-button>
        </a-button-group>

        <!-- 缩放控制 -->
        <a-button-group>
          <a-button @click="handleZoomOut">
            <template #icon>
              <ZoomOutOutlined />
            </template>
          </a-button>
          <a-button @click="handleZoomReset">
            {{ Math.round(zoomLevel * 100) }}%
          </a-button>
          <a-button @click="handleZoomIn">
            <template #icon>
              <ZoomInOutlined />
            </template>
          </a-button>
        </a-button-group>

        <!-- 布局控制 -->
        <a-button-group>
          <a-button @click="handleAutoLayout">
            <template #icon>
              <PartitionOutlined />
            </template>
            自动布局
          </a-button>
          <a-button @click="handleFitView">
            <template #icon>
              <FullscreenOutlined />
            </template>
            适应画布
          </a-button>
        </a-button-group>
      </a-space>
    </div>

    <!-- 右侧：主要操作 -->
    <div class="toolbar-right">
      <a-space>
        <!-- 导入导出 -->
        <a-dropdown>
          <a-button>
            <template #icon>
              <MoreOutlined />
            </template>
            更多
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="import" @click="$emit('import')">
                <ImportOutlined />
                导入流程
              </a-menu-item>
              <a-menu-item key="export" @click="$emit('export')">
                <ExportOutlined />
                导出流程
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="clear" @click="handleClear" :disabled="readonly">
                <ClearOutlined />
                清空画布
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <!-- 预览 -->
        <a-button @click="$emit('preview')">
          <template #icon>
            <EyeOutlined />
          </template>
          预览
        </a-button>

        <!-- 保存 -->
        <a-button
          type="primary"
          :disabled="!canSave || readonly"
          :loading="saving"
          @click="handleSave"
        >
          <template #icon>
            <SaveOutlined />
          </template>
          保存
        </a-button>

        <!-- 发布 -->
        <a-button
          type="primary"
          :disabled="!canDeploy || readonly"
          :loading="deploying"
          @click="handleDeploy"
        >
          <template #icon>
            <RocketOutlined />
          </template>
          发布
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import {
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  PartitionOutlined,
  FullscreenOutlined,
  MoreOutlined,
  DownOutlined,
  ImportOutlined,
  ExportOutlined,
  ClearOutlined,
  EyeOutlined,
  SaveOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'
import type { ProcessDefinition } from '../../../../types/workflow'

// Props
interface Props {
  processDefinition: ProcessDefinition
  canSave?: boolean
  canDeploy?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canSave: false,
  canDeploy: false,
  readonly: false
})

// Emits
const emit = defineEmits<{
  save: []
  deploy: []
  preview: []
  export: []
  import: []
  clear: []
  undo: []
  redo: []
  zoomIn: []
  zoomOut: []
  zoomReset: []
  autoLayout: []
  fitView: []
  processChange: [definition: ProcessDefinition]
}>()

// 响应式数据
const saving = ref(false)
const deploying = ref(false)
const canUndo = ref(false)
const canRedo = ref(false)
const zoomLevel = ref(1)

// 本地流程定义副本
const localProcessDefinition = reactive<ProcessDefinition>({
  ...props.processDefinition
})

// 监听props变化
watch(
  () => props.processDefinition,
  (newDefinition) => {
    Object.assign(localProcessDefinition, newDefinition)
  },
  { deep: true }
)

// 事件处理函数
const handleProcessNameChange = () => {
  emit('processChange', localProcessDefinition)
}

const handleCategoryChange = () => {
  emit('processChange', localProcessDefinition)
}

const handleSave = async () => {
  if (!props.canSave) return
  
  saving.value = true
  try {
    emit('save')
  } finally {
    saving.value = false
  }
}

const handleDeploy = async () => {
  if (!props.canDeploy) return
  
  Modal.confirm({
    title: '确认发布',
    content: '发布后流程将生效，确定要发布吗？',
    onOk: async () => {
      deploying.value = true
      try {
        emit('deploy')
      } finally {
        deploying.value = false
      }
    }
  })
}

const handleClear = () => {
  Modal.confirm({
    title: '确认清空',
    content: '清空后将删除所有节点和连线，确定要清空吗？',
    onOk: () => {
      emit('clear')
    }
  })
}

const handleUndo = () => {
  emit('undo')
}

const handleRedo = () => {
  emit('redo')
}

const handleZoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
  emit('zoomIn')
}

const handleZoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
  emit('zoomOut')
}

const handleZoomReset = () => {
  zoomLevel.value = 1
  emit('zoomReset')
}

const handleAutoLayout = () => {
  emit('autoLayout')
}

const handleFitView = () => {
  emit('fitView')
}
</script>

<style lang="less" scoped>
.designer-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    flex: 1;
    min-width: 0;

    .process-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .process-name-input {
        width: 200px;
        font-size: 16px;
        font-weight: 500;
      }

      .category-select {
        width: 120px;
      }
    }
  }

  .toolbar-center {
    flex: 0 0 auto;
    margin: 0 24px;
  }

  .toolbar-right {
    flex: 0 0 auto;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-toolbar {
    .toolbar-center {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .designer-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;

    .toolbar-left,
    .toolbar-right {
      width: 100%;
    }

    .process-info {
      flex-direction: column;
      align-items: stretch;

      .process-name-input,
      .category-select {
        width: 100%;
      }
    }
  }
}
</style>
