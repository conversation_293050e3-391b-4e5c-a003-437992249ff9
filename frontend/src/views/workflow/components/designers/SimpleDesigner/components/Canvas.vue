<template>
  <div
    ref="canvasContainer"
    class="canvas-container"
    @click="handleCanvasClick"
    @drop="handleDrop"
    @dragover="handleDragOver"
  >
    <!-- SVG画布 -->
    <svg
      ref="svgCanvas"
      class="svg-canvas"
      :width="canvasSize.width"
      :height="canvasSize.height"
      :viewBox="`0 0 ${canvasSize.width} ${canvasSize.height}`"
    >
      <!-- 网格背景 -->
      <defs>
        <pattern
          id="grid"
          width="20"
          height="20"
          patternUnits="userSpaceOnUse"
        >
          <path
            d="M 20 0 L 0 0 0 20"
            fill="none"
            stroke="#f0f0f0"
            stroke-width="1"
          />
        </pattern>
      </defs>
      <rect
        width="100%"
        height="100%"
        fill="url(#grid)"
      />

      <!-- 连线层 -->
      <g class="edges-layer">
        <EdgeComponent
          v-for="edge in edges"
          :key="edge.id"
          :edge="edge"
          :selected="selectedElement?.id === edge.id"
          :nodes="nodeMap"
          @select="handleEdgeSelect"
          @delete="handleEdgeDelete"
        />
      </g>

      <!-- 临时连线 -->
      <g v-if="tempEdge" class="temp-edge-layer">
        <line
          :x1="tempEdge.start.x"
          :y1="tempEdge.start.y"
          :x2="tempEdge.end.x"
          :y2="tempEdge.end.y"
          stroke="#1890ff"
          stroke-width="2"
          stroke-dasharray="5,5"
        />
      </g>
    </svg>

    <!-- 节点层 -->
    <div class="nodes-layer">
      <NodeComponent
        v-for="node in nodes"
        :key="node.id"
        :node="node"
        :selected="selectedElement?.id === node.id"
        :zoom="zoom"
        @select="handleNodeSelect"
        @move="handleNodeMove"
        @delete="handleNodeDelete"
        @connect-start="handleConnectStart"
        @connect-end="handleConnectEnd"
        @connect-move="handleConnectMove"
      />
    </div>

    <!-- 选择框 -->
    <div
      v-if="selectionBox"
      class="selection-box"
      :style="{
        left: selectionBox.x + 'px',
        top: selectionBox.y + 'px',
        width: selectionBox.width + 'px',
        height: selectionBox.height + 'px'
      }"
    />

    <!-- 缩放控制 -->
    <div class="zoom-controls">
      <a-button-group>
        <a-button size="small" @click="zoomIn">
          <PlusOutlined />
        </a-button>
        <a-button size="small" @click="zoomOut">
          <MinusOutlined />
        </a-button>
        <a-button size="small" @click="resetZoom">
          <FullscreenOutlined />
        </a-button>
      </a-button-group>
      <div class="zoom-level">{{ Math.round(zoom * 100) }}%</div>
    </div>

    <!-- 小地图 -->
    <div v-if="showMinimap" class="minimap">
      <svg
        class="minimap-svg"
        :width="minimapSize.width"
        :height="minimapSize.height"
        :viewBox="`0 0 ${canvasSize.width} ${canvasSize.height}`"
      >
        <!-- 节点缩略图 -->
        <rect
          v-for="node in nodes"
          :key="node.id"
          :x="node.position.x"
          :y="node.position.y"
          :width="node.size.width"
          :height="node.size.height"
          fill="#1890ff"
          opacity="0.6"
        />
        
        <!-- 连线缩略图 -->
        <line
          v-for="edge in edges"
          :key="edge.id"
          :x1="getNodeCenter(edge.source).x"
          :y1="getNodeCenter(edge.source).y"
          :x2="getNodeCenter(edge.target).x"
          :y2="getNodeCenter(edge.target).y"
          stroke="#666"
          stroke-width="1"
        />
        
        <!-- 视口框 -->
        <rect
          class="viewport-box"
          :x="viewport.x"
          :y="viewport.y"
          :width="viewport.width"
          :height="viewport.height"
          fill="none"
          stroke="#ff4d4f"
          stroke-width="2"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { PlusOutlined, MinusOutlined, FullscreenOutlined } from '@ant-design/icons-vue'
import type { FlowNode, FlowEdge } from '../../../../types/workflow'
import NodeComponent from './nodes/NodeComponent.vue'
import EdgeComponent from './edges/EdgeComponent.vue'

// Props
interface Props {
  nodes: FlowNode[]
  edges: FlowEdge[]
  selectedElement?: FlowNode | FlowEdge | null
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits
const emit = defineEmits<{
  nodeSelect: [node: FlowNode]
  edgeSelect: [edge: FlowEdge]
  canvasClick: []
  canvasDrop: [event: DragEvent, position: { x: number; y: number }]
  nodeMove: [nodeId: string, position: { x: number; y: number }]
  edgeCreate: [sourceId: string, targetId: string]
  edgeDelete: [edgeId: string]
  nodeDelete: [nodeId: string]
}>()

// 响应式数据
const canvasContainer = ref<HTMLElement>()
const svgCanvas = ref<SVGElement>()
const zoom = ref(1)
const showMinimap = ref(true)
const canvasSize = reactive({ width: 2000, height: 1500 })
const minimapSize = reactive({ width: 200, height: 150 })
const viewport = reactive({ x: 0, y: 0, width: 800, height: 600 })
const selectionBox = ref<{ x: number; y: number; width: number; height: number } | null>(null)

// 连线相关
const tempEdge = ref<{ start: { x: number; y: number }; end: { x: number; y: number } } | null>(null)
const connectingNode = ref<string | null>(null)

// 计算属性
const nodeMap = computed(() => {
  const map = new Map<string, FlowNode>()
  props.nodes.forEach(node => map.set(node.id, node))
  return map
})

// 生命周期
onMounted(() => {
  updateViewport()
  window.addEventListener('resize', updateViewport)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateViewport)
})

// 方法
const updateViewport = () => {
  if (canvasContainer.value) {
    const rect = canvasContainer.value.getBoundingClientRect()
    viewport.width = rect.width / zoom.value
    viewport.height = rect.height / zoom.value
  }
}

const getNodeCenter = (nodeId: string) => {
  const node = nodeMap.value.get(nodeId)
  if (!node) return { x: 0, y: 0 }
  
  return {
    x: node.position.x + node.size.width / 2,
    y: node.position.y + node.size.height / 2
  }
}

const getCanvasPosition = (event: MouseEvent) => {
  if (!canvasContainer.value) return { x: 0, y: 0 }
  
  const rect = canvasContainer.value.getBoundingClientRect()
  return {
    x: (event.clientX - rect.left) / zoom.value,
    y: (event.clientY - rect.top) / zoom.value
  }
}

// 事件处理
const handleCanvasClick = (event: MouseEvent) => {
  if (event.target === canvasContainer.value || event.target === svgCanvas.value) {
    emit('canvasClick')
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const position = getCanvasPosition(event)
  emit('canvasDrop', event, position)
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

const handleNodeSelect = (node: FlowNode) => {
  emit('nodeSelect', node)
}

const handleEdgeSelect = (edge: FlowEdge) => {
  emit('edgeSelect', edge)
}

const handleNodeMove = (nodeId: string, position: { x: number; y: number }) => {
  emit('nodeMove', nodeId, position)
}

const handleNodeDelete = (nodeId: string) => {
  emit('nodeDelete', nodeId)
}

const handleEdgeDelete = (edgeId: string) => {
  emit('edgeDelete', edgeId)
}

const handleConnectStart = (nodeId: string, position: { x: number; y: number }) => {
  connectingNode.value = nodeId
  tempEdge.value = {
    start: position,
    end: position
  }
}

const handleConnectMove = (position: { x: number; y: number }) => {
  if (tempEdge.value) {
    tempEdge.value.end = position
  }
}

const handleConnectEnd = (targetNodeId: string) => {
  if (connectingNode.value && connectingNode.value !== targetNodeId) {
    emit('edgeCreate', connectingNode.value, targetNodeId)
  }
  
  connectingNode.value = null
  tempEdge.value = null
}

// 缩放控制
const zoomIn = () => {
  zoom.value = Math.min(zoom.value * 1.2, 3)
  updateViewport()
}

const zoomOut = () => {
  zoom.value = Math.max(zoom.value / 1.2, 0.3)
  updateViewport()
}

const resetZoom = () => {
  zoom.value = 1
  updateViewport()
}

// 自动布局
const autoLayout = () => {
  // TODO: 实现自动布局算法
  console.log('Auto layout')
}

// 适应画布
const fitToView = () => {
  if (props.nodes.length === 0) return
  
  // 计算所有节点的边界
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
  
  props.nodes.forEach(node => {
    minX = Math.min(minX, node.position.x)
    minY = Math.min(minY, node.position.y)
    maxX = Math.max(maxX, node.position.x + node.size.width)
    maxY = Math.max(maxY, node.position.y + node.size.height)
  })
  
  const contentWidth = maxX - minX
  const contentHeight = maxY - minY
  
  if (canvasContainer.value) {
    const containerRect = canvasContainer.value.getBoundingClientRect()
    const scaleX = containerRect.width / (contentWidth + 100)
    const scaleY = containerRect.height / (contentHeight + 100)
    zoom.value = Math.min(scaleX, scaleY, 1)
    updateViewport()
  }
}

// 暴露方法给父组件
defineExpose({
  zoomIn,
  zoomOut,
  resetZoom,
  autoLayout,
  fitToView
})
</script>

<style lang="less" scoped>
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #fff;
  cursor: default;

  .svg-canvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    
    .edges-layer,
    .temp-edge-layer {
      pointer-events: auto;
    }
  }

  .nodes-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    
    > * {
      pointer-events: auto;
    }
  }

  .selection-box {
    position: absolute;
    border: 2px dashed #1890ff;
    background: rgba(24, 144, 255, 0.1);
    pointer-events: none;
  }

  .zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    .zoom-level {
      font-size: 12px;
      color: #666;
      background: rgba(255, 255, 255, 0.9);
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
    }
  }

  .minimap {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;

    .minimap-svg {
      display: block;
    }

    .viewport-box {
      cursor: move;
    }
  }
}

// 拖拽状态
.canvas-container.dragging {
  cursor: grabbing;
}

// 连线状态
.canvas-container.connecting {
  cursor: crosshair;
}
</style>
