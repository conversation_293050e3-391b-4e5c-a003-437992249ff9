<template>
  <div class="simple-designer">
    <!-- 工具栏 -->
    <Toolbar
      :process-definition="processDefinition"
      :can-save="canSave"
      :can-deploy="canDeploy"
      @save="handleSave"
      @deploy="handleDeploy"
      @preview="handlePreview"
      @export="handleExport"
      @import="handleImport"
      @clear="handleClear"
    />

    <!-- 主要内容区 -->
    <div class="designer-content">
      <!-- 节点面板 -->
      <NodePanel
        :node-types="nodeTypes"
        @drag-start="handleDragStart"
      />

      <!-- 画布区域 -->
      <div class="canvas-container">
        <Canvas
          ref="canvasRef"
          :nodes="flowDefinition.nodes"
          :edges="flowDefinition.edges"
          :selected-element="selectedElement"
          @node-select="handleNodeSelect"
          @edge-select="handleEdgeSelect"
          @canvas-click="handleCanvasClick"
          @canvas-drop="handleCanvasDrop"
          @node-move="handleNodeMove"
          @edge-create="handleEdgeCreate"
          @edge-delete="handleEdgeDelete"
          @node-delete="handleNodeDelete"
        />
      </div>

      <!-- 属性面板 -->
      <PropertyPanel
        :selected-element="selectedElement"
        :form-fields="formFields"
        @property-change="handlePropertyChange"
        @form-field-add="handleFormFieldAdd"
        @form-field-update="handleFormFieldUpdate"
        @form-field-delete="handleFormFieldDelete"
      />
    </div>

    <!-- 预览弹窗 -->
    <PreviewModal
      v-model:visible="previewVisible"
      :process-definition="processDefinition"
      :flow-definition="flowDefinition"
    />

    <!-- 导入弹窗 -->
    <ImportModal
      v-model:visible="importVisible"
      @import-success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import type {
  ProcessDefinition,
  FlowDefinition,
  FlowNode,
  FlowEdge,
  FormField,
  NodeType
} from '../../../types/workflow'
import { useCanvas } from './hooks/useCanvas'
import { useDragDrop } from './hooks/useDragDrop'
import { useNodeEditor } from './hooks/useNodeEditor'
import { useFormDesigner } from './hooks/useFormDesigner'
import { validateFlowDefinition } from '../../../utils/workflow-validator'
import { generateNodeId, generateEdgeId } from '../../../utils/id-generator'

// 组件引入
import Toolbar from './components/Toolbar.vue'
import NodePanel from './components/NodePanel.vue'
import Canvas from './components/Canvas.vue'
import PropertyPanel from './components/PropertyPanel.vue'
import PreviewModal from './components/PreviewModal.vue'
import ImportModal from './components/ImportModal.vue'

// Props
interface Props {
  processDefinition?: ProcessDefinition
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits
const emit = defineEmits<{
  save: [definition: ProcessDefinition]
  deploy: [definition: ProcessDefinition]
  change: [definition: FlowDefinition]
}>()

// 响应式数据
const canvasRef = ref()
const selectedElement = ref<FlowNode | FlowEdge | null>(null)
const previewVisible = ref(false)
const importVisible = ref(false)

// 流程定义
const processDefinition = reactive<ProcessDefinition>({
  processKey: '',
  processName: '',
  designerType: 'SIMPLE',
  processDefinitionJson: '',
  ...props.processDefinition
})

// 流程定义结构
const flowDefinition = reactive<FlowDefinition>({
  nodes: [],
  edges: [],
  variables: {},
  settings: {
    name: processDefinition.processName || '',
    description: processDefinition.description || '',
    category: processDefinition.category || '',
    formSettings: {
      fields: []
    }
  }
})

// 节点类型定义
const nodeTypes = ref([
  {
    id: 'start',
    type: 'START' as NodeType,
    name: '开始',
    icon: 'play-circle',
    description: '流程开始节点'
  },
  {
    id: 'user-task',
    type: 'USER_TASK' as NodeType,
    name: '审批',
    icon: 'user',
    description: '用户审批任务'
  },
  {
    id: 'service-task',
    type: 'SERVICE_TASK' as NodeType,
    name: '服务',
    icon: 'api',
    description: '自动化服务任务'
  },
  {
    id: 'gateway',
    type: 'GATEWAY' as NodeType,
    name: '网关',
    icon: 'fork',
    description: '条件分支网关'
  },
  {
    id: 'end',
    type: 'END' as NodeType,
    name: '结束',
    icon: 'stop',
    description: '流程结束节点'
  }
])

// 使用组合式函数
const { initCanvas, updateCanvas } = useCanvas(canvasRef)
const { handleDragStart } = useDragDrop()
const { editNode, editEdge } = useNodeEditor()
const { formFields, addFormField, updateFormField, deleteFormField } = useFormDesigner()

// 计算属性
const canSave = computed(() => {
  return !props.readonly && 
         processDefinition.processKey && 
         processDefinition.processName &&
         flowDefinition.nodes.length > 0
})

const canDeploy = computed(() => {
  return canSave.value && validateFlowDefinition(flowDefinition)
})

// 初始化
onMounted(() => {
  initCanvas()
  
  // 如果有现有的流程定义，加载它
  if (props.processDefinition?.processDefinitionJson) {
    try {
      const definition = JSON.parse(props.processDefinition.processDefinitionJson)
      Object.assign(flowDefinition, definition)
      updateCanvas()
    } catch (error) {
      console.error('加载流程定义失败:', error)
      message.error('加载流程定义失败')
    }
  } else {
    // 创建默认的开始节点
    createDefaultStartNode()
  }
})

// 监听流程定义变化
watch(
  () => flowDefinition,
  (newDefinition) => {
    processDefinition.processDefinitionJson = JSON.stringify(newDefinition)
    emit('change', newDefinition)
  },
  { deep: true }
)

// 事件处理函数
const handleNodeSelect = (node: FlowNode) => {
  selectedElement.value = node
}

const handleEdgeSelect = (edge: FlowEdge) => {
  selectedElement.value = edge
}

const handleCanvasClick = () => {
  selectedElement.value = null
}

const handleCanvasDrop = (event: DragEvent, position: { x: number; y: number }) => {
  const nodeType = event.dataTransfer?.getData('nodeType') as NodeType
  if (nodeType) {
    createNode(nodeType, position)
  }
}

const handleNodeMove = (nodeId: string, position: { x: number; y: number }) => {
  const node = flowDefinition.nodes.find(n => n.id === nodeId)
  if (node) {
    node.position = position
  }
}

const handleEdgeCreate = (sourceId: string, targetId: string) => {
  const edge: FlowEdge = {
    id: generateEdgeId(),
    source: sourceId,
    target: targetId
  }
  flowDefinition.edges.push(edge)
}

const handleEdgeDelete = (edgeId: string) => {
  const index = flowDefinition.edges.findIndex(e => e.id === edgeId)
  if (index > -1) {
    flowDefinition.edges.splice(index, 1)
  }
}

const handleNodeDelete = (nodeId: string) => {
  // 删除节点
  const nodeIndex = flowDefinition.nodes.findIndex(n => n.id === nodeId)
  if (nodeIndex > -1) {
    flowDefinition.nodes.splice(nodeIndex, 1)
  }
  
  // 删除相关连线
  flowDefinition.edges = flowDefinition.edges.filter(
    e => e.source !== nodeId && e.target !== nodeId
  )
  
  // 清除选择
  if (selectedElement.value?.id === nodeId) {
    selectedElement.value = null
  }
}

const handlePropertyChange = (property: string, value: any) => {
  if (selectedElement.value) {
    selectedElement.value.properties = {
      ...selectedElement.value.properties,
      [property]: value
    }
  }
}

const handleFormFieldAdd = (field: FormField) => {
  addFormField(field)
  flowDefinition.settings.formSettings!.fields = formFields.value
}

const handleFormFieldUpdate = (fieldId: string, field: FormField) => {
  updateFormField(fieldId, field)
  flowDefinition.settings.formSettings!.fields = formFields.value
}

const handleFormFieldDelete = (fieldId: string) => {
  deleteFormField(fieldId)
  flowDefinition.settings.formSettings!.fields = formFields.value
}

const handleSave = () => {
  if (!canSave.value) {
    message.warning('请完善流程信息')
    return
  }
  
  emit('save', processDefinition)
}

const handleDeploy = () => {
  if (!canDeploy.value) {
    message.warning('流程定义不完整，无法发布')
    return
  }
  
  emit('deploy', processDefinition)
}

const handlePreview = () => {
  previewVisible.value = true
}

const handleExport = () => {
  const dataStr = JSON.stringify(flowDefinition, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${processDefinition.processKey || 'workflow'}.json`
  link.click()
  URL.revokeObjectURL(url)
}

const handleImport = () => {
  importVisible.value = true
}

const handleImportSuccess = (definition: FlowDefinition) => {
  Object.assign(flowDefinition, definition)
  updateCanvas()
  message.success('导入成功')
}

const handleClear = () => {
  flowDefinition.nodes = []
  flowDefinition.edges = []
  selectedElement.value = null
  createDefaultStartNode()
  updateCanvas()
}

// 辅助函数
const createDefaultStartNode = () => {
  const startNode: FlowNode = {
    id: generateNodeId(),
    type: 'START',
    name: '开始',
    position: { x: 100, y: 100 },
    size: { width: 80, height: 40 },
    properties: {}
  }
  flowDefinition.nodes.push(startNode)
}

const createNode = (type: NodeType, position: { x: number; y: number }) => {
  const nodeConfig = nodeTypes.value.find(nt => nt.type === type)
  if (!nodeConfig) return
  
  const node: FlowNode = {
    id: generateNodeId(),
    type,
    name: nodeConfig.name,
    position,
    size: { width: 80, height: 40 },
    properties: {}
  }
  
  flowDefinition.nodes.push(node)
  selectedElement.value = node
}
</script>

<style lang="less" scoped>
.simple-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .designer-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .canvas-container {
      flex: 1;
      position: relative;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      border-right: 1px solid #e8e8e8;
    }
  }
}
</style>
