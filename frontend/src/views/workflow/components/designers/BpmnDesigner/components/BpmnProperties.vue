<template>
  <div class="bpmn-properties">
    <!-- 面板头部 -->
    <div class="properties-header">
      <h3>属性面板</h3>
      <a-button
        type="text"
        size="small"
        @click="$emit('close')"
      >
        <template #icon>
          <CloseOutlined />
        </template>
      </a-button>
    </div>

    <!-- 属性内容 -->
    <div class="properties-content">
      <div v-if="!selectedElement" class="no-selection">
        <a-empty
          description="请选择一个元素"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>

      <div v-else class="element-properties">
        <!-- 基本信息 -->
        <a-collapse v-model:activeKey="activeKeys" ghost>
          <!-- 通用属性 -->
          <a-collapse-panel key="general" header="通用属性">
            <a-form layout="vertical" :model="elementProperties">
              <a-form-item label="ID">
                <a-input
                  v-model:value="elementProperties.id"
                  :disabled="true"
                />
              </a-form-item>
              
              <a-form-item label="名称">
                <a-input
                  v-model:value="elementProperties.name"
                  placeholder="请输入名称"
                  @blur="handlePropertyChange('name', elementProperties.name)"
                />
              </a-form-item>
              
              <a-form-item v-if="elementProperties.type" label="类型">
                <a-input
                  v-model:value="elementProperties.type"
                  :disabled="true"
                />
              </a-form-item>
              
              <a-form-item v-if="showDocumentation" label="文档">
                <a-textarea
                  v-model:value="elementProperties.documentation"
                  placeholder="请输入文档说明"
                  :rows="3"
                  @blur="handlePropertyChange('documentation', elementProperties.documentation)"
                />
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <!-- 用户任务属性 -->
          <a-collapse-panel
            v-if="isUserTask"
            key="userTask"
            header="用户任务属性"
          >
            <a-form layout="vertical" :model="userTaskProperties">
              <a-form-item label="处理人">
                <a-input
                  v-model:value="userTaskProperties.assignee"
                  placeholder="请输入处理人"
                  @blur="handlePropertyChange('assignee', userTaskProperties.assignee)"
                />
              </a-form-item>
              
              <a-form-item label="候选用户">
                <a-select
                  v-model:value="userTaskProperties.candidateUsers"
                  mode="tags"
                  placeholder="请选择候选用户"
                  @change="handlePropertyChange('candidateUsers', userTaskProperties.candidateUsers)"
                >
                  <!-- 动态加载用户选项 -->
                </a-select>
              </a-form-item>
              
              <a-form-item label="候选用户组">
                <a-select
                  v-model:value="userTaskProperties.candidateGroups"
                  mode="tags"
                  placeholder="请选择候选用户组"
                  @change="handlePropertyChange('candidateGroups', userTaskProperties.candidateGroups)"
                >
                  <!-- 动态加载用户组选项 -->
                </a-select>
              </a-form-item>
              
              <a-form-item label="到期时间">
                <a-input
                  v-model:value="userTaskProperties.dueDate"
                  placeholder="请输入到期时间表达式"
                  @blur="handlePropertyChange('dueDate', userTaskProperties.dueDate)"
                />
              </a-form-item>
              
              <a-form-item label="优先级">
                <a-input-number
                  v-model:value="userTaskProperties.priority"
                  :min="0"
                  :max="100"
                  @change="handlePropertyChange('priority', userTaskProperties.priority)"
                />
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <!-- 服务任务属性 -->
          <a-collapse-panel
            v-if="isServiceTask"
            key="serviceTask"
            header="服务任务属性"
          >
            <a-form layout="vertical" :model="serviceTaskProperties">
              <a-form-item label="实现类型">
                <a-select
                  v-model:value="serviceTaskProperties.implementation"
                  placeholder="请选择实现类型"
                  @change="handlePropertyChange('implementation', serviceTaskProperties.implementation)"
                >
                  <a-select-option value="class">Java类</a-select-option>
                  <a-select-option value="expression">表达式</a-select-option>
                  <a-select-option value="delegateExpression">委托表达式</a-select-option>
                  <a-select-option value="external">外部任务</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item
                v-if="serviceTaskProperties.implementation === 'class'"
                label="Java类"
              >
                <a-input
                  v-model:value="serviceTaskProperties.class"
                  placeholder="请输入Java类全名"
                  @blur="handlePropertyChange('class', serviceTaskProperties.class)"
                />
              </a-form-item>
              
              <a-form-item
                v-if="serviceTaskProperties.implementation === 'expression'"
                label="表达式"
              >
                <a-textarea
                  v-model:value="serviceTaskProperties.expression"
                  placeholder="请输入表达式"
                  :rows="3"
                  @blur="handlePropertyChange('expression', serviceTaskProperties.expression)"
                />
              </a-form-item>
              
              <a-form-item
                v-if="serviceTaskProperties.implementation === 'external'"
                label="外部任务主题"
              >
                <a-input
                  v-model:value="serviceTaskProperties.topic"
                  placeholder="请输入外部任务主题"
                  @blur="handlePropertyChange('topic', serviceTaskProperties.topic)"
                />
              </a-form-item>
              
              <a-form-item label="异步执行">
                <a-switch
                  v-model:checked="serviceTaskProperties.async"
                  @change="handlePropertyChange('async', serviceTaskProperties.async)"
                />
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <!-- 网关属性 -->
          <a-collapse-panel
            v-if="isGateway"
            key="gateway"
            header="网关属性"
          >
            <a-form layout="vertical" :model="gatewayProperties">
              <a-form-item label="网关类型">
                <a-input
                  v-model:value="gatewayProperties.type"
                  :disabled="true"
                />
              </a-form-item>
              
              <a-form-item
                v-if="gatewayProperties.type === 'exclusiveGateway'"
                label="默认流"
              >
                <a-select
                  v-model:value="gatewayProperties.default"
                  placeholder="请选择默认流"
                  allow-clear
                  @change="handlePropertyChange('default', gatewayProperties.default)"
                >
                  <!-- 动态加载出边选项 -->
                </a-select>
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <!-- 序列流属性 -->
          <a-collapse-panel
            v-if="isSequenceFlow"
            key="sequenceFlow"
            header="序列流属性"
          >
            <a-form layout="vertical" :model="sequenceFlowProperties">
              <a-form-item label="条件表达式">
                <a-textarea
                  v-model:value="sequenceFlowProperties.conditionExpression"
                  placeholder="请输入条件表达式"
                  :rows="3"
                  @blur="handlePropertyChange('conditionExpression', sequenceFlowProperties.conditionExpression)"
                />
              </a-form-item>
              
              <a-form-item label="条件类型">
                <a-select
                  v-model:value="sequenceFlowProperties.conditionType"
                  placeholder="请选择条件类型"
                  @change="handlePropertyChange('conditionType', sequenceFlowProperties.conditionType)"
                >
                  <a-select-option value="expression">表达式</a-select-option>
                  <a-select-option value="script">脚本</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <!-- 扩展属性 -->
          <a-collapse-panel key="extensions" header="扩展属性">
            <div class="extension-properties">
              <div
                v-for="(prop, index) in extensionProperties"
                :key="index"
                class="extension-property"
              >
                <a-input
                  v-model:value="prop.name"
                  placeholder="属性名"
                  style="width: 40%"
                  @blur="handleExtensionPropertyChange"
                />
                <a-input
                  v-model:value="prop.value"
                  placeholder="属性值"
                  style="width: 50%; margin-left: 8px"
                  @blur="handleExtensionPropertyChange"
                />
                <a-button
                  type="text"
                  danger
                  size="small"
                  style="margin-left: 8px"
                  @click="removeExtensionProperty(index)"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
              
              <a-button
                type="dashed"
                block
                @click="addExtensionProperty"
              >
                <PlusOutlined />
                添加属性
              </a-button>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Empty } from 'ant-design-vue'
import {
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  selectedElement?: any
  modeler?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  propertyChange: [property: string, value: any]
  close: []
}>()

// 响应式数据
const activeKeys = ref(['general'])

// 元素属性
const elementProperties = reactive({
  id: '',
  name: '',
  type: '',
  documentation: ''
})

// 用户任务属性
const userTaskProperties = reactive({
  assignee: '',
  candidateUsers: [],
  candidateGroups: [],
  dueDate: '',
  priority: 50
})

// 服务任务属性
const serviceTaskProperties = reactive({
  implementation: '',
  class: '',
  expression: '',
  topic: '',
  async: false
})

// 网关属性
const gatewayProperties = reactive({
  type: '',
  default: ''
})

// 序列流属性
const sequenceFlowProperties = reactive({
  conditionExpression: '',
  conditionType: 'expression'
})

// 扩展属性
const extensionProperties = ref<Array<{ name: string; value: string }>>([])

// 计算属性
const isUserTask = computed(() => {
  return props.selectedElement?.$type === 'bpmn:UserTask'
})

const isServiceTask = computed(() => {
  return props.selectedElement?.$type === 'bpmn:ServiceTask'
})

const isGateway = computed(() => {
  const type = props.selectedElement?.$type
  return type?.includes('Gateway')
})

const isSequenceFlow = computed(() => {
  return props.selectedElement?.$type === 'bpmn:SequenceFlow'
})

const showDocumentation = computed(() => {
  return props.selectedElement && props.selectedElement.$type !== 'bpmn:SequenceFlow'
})

// 监听选中元素变化
watch(
  () => props.selectedElement,
  (element) => {
    if (element) {
      loadElementProperties(element)
    }
  },
  { immediate: true }
)

// 方法
const loadElementProperties = (element: any) => {
  if (!element) return

  // 加载通用属性
  elementProperties.id = element.id || ''
  elementProperties.name = element.businessObject?.name || ''
  elementProperties.type = element.$type || ''
  elementProperties.documentation = getDocumentation(element) || ''

  // 根据元素类型加载特定属性
  if (isUserTask.value) {
    loadUserTaskProperties(element)
  } else if (isServiceTask.value) {
    loadServiceTaskProperties(element)
  } else if (isGateway.value) {
    loadGatewayProperties(element)
  } else if (isSequenceFlow.value) {
    loadSequenceFlowProperties(element)
  }

  // 加载扩展属性
  loadExtensionProperties(element)
}

const loadUserTaskProperties = (element: any) => {
  const bo = element.businessObject
  userTaskProperties.assignee = bo.assignee || ''
  userTaskProperties.candidateUsers = bo.candidateUsers?.split(',') || []
  userTaskProperties.candidateGroups = bo.candidateGroups?.split(',') || []
  userTaskProperties.dueDate = bo.dueDate || ''
  userTaskProperties.priority = bo.priority || 50
}

const loadServiceTaskProperties = (element: any) => {
  const bo = element.businessObject
  serviceTaskProperties.implementation = getImplementationType(bo)
  serviceTaskProperties.class = bo.class || ''
  serviceTaskProperties.expression = bo.expression || ''
  serviceTaskProperties.topic = bo.topic || ''
  serviceTaskProperties.async = bo.async || false
}

const loadGatewayProperties = (element: any) => {
  const bo = element.businessObject
  gatewayProperties.type = element.$type
  gatewayProperties.default = bo.default?.id || ''
}

const loadSequenceFlowProperties = (element: any) => {
  const bo = element.businessObject
  const condition = bo.conditionExpression
  sequenceFlowProperties.conditionExpression = condition?.body || ''
  sequenceFlowProperties.conditionType = condition?.$type?.includes('script') ? 'script' : 'expression'
}

const loadExtensionProperties = (element: any) => {
  // TODO: 实现扩展属性加载
  extensionProperties.value = []
}

const getDocumentation = (element: any) => {
  const docs = element.businessObject?.documentation
  return docs?.[0]?.text || ''
}

const getImplementationType = (businessObject: any) => {
  if (businessObject.class) return 'class'
  if (businessObject.expression) return 'expression'
  if (businessObject.delegateExpression) return 'delegateExpression'
  if (businessObject.topic) return 'external'
  return ''
}

const handlePropertyChange = (property: string, value: any) => {
  if (!props.modeler || !props.selectedElement) return

  const modeling = props.modeler.get('modeling')
  const element = props.selectedElement

  // 根据属性类型进行不同的处理
  switch (property) {
    case 'name':
      modeling.updateProperties(element, { name: value })
      break
    case 'documentation':
      updateDocumentation(element, value)
      break
    case 'assignee':
    case 'candidateUsers':
    case 'candidateGroups':
    case 'dueDate':
    case 'priority':
      modeling.updateProperties(element, { [property]: value })
      break
    case 'conditionExpression':
      updateConditionExpression(element, value)
      break
    default:
      modeling.updateProperties(element, { [property]: value })
  }

  emit('propertyChange', property, value)
}

const updateDocumentation = (element: any, text: string) => {
  const modeling = props.modeler.get('modeling')
  const documentation = text ? [{ text }] : []
  modeling.updateProperties(element, { documentation })
}

const updateConditionExpression = (element: any, expression: string) => {
  const modeling = props.modeler.get('modeling')
  const conditionExpression = expression ? {
    $type: 'bpmn:FormalExpression',
    body: expression
  } : undefined
  modeling.updateProperties(element, { conditionExpression })
}

const handleExtensionPropertyChange = () => {
  // TODO: 实现扩展属性变化处理
}

const addExtensionProperty = () => {
  extensionProperties.value.push({ name: '', value: '' })
}

const removeExtensionProperty = (index: number) => {
  extensionProperties.value.splice(index, 1)
  handleExtensionPropertyChange()
}
</script>

<style lang="less" scoped>
.bpmn-properties {
  width: 320px;
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  height: 100%;

  .properties-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .properties-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .no-selection {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }

    .element-properties {
      .extension-properties {
        .extension-property {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
        }
      }
    }
  }
}

:deep(.ant-collapse) {
  background: transparent;
  border: none;

  .ant-collapse-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #fff;

    .ant-collapse-header {
      padding: 8px 12px;
      font-size: 13px;
      font-weight: 500;
    }

    .ant-collapse-content {
      border-top: 1px solid #e8e8e8;

      .ant-collapse-content-box {
        padding: 12px;
      }
    }
  }
}

:deep(.ant-form-item) {
  margin-bottom: 12px;

  .ant-form-item-label {
    padding-bottom: 4px;

    label {
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
