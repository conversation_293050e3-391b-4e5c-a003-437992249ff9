<template>
  <div class="bpmn-toolbar">
    <!-- 左侧：基本信息 -->
    <div class="toolbar-left">
      <div class="process-info">
        <a-input
          v-model:value="localProcessDefinition.processName"
          placeholder="请输入流程名称"
          class="process-name-input"
          :disabled="readonly"
          @blur="handleProcessNameChange"
        />
        <a-select
          v-model:value="localProcessDefinition.category"
          placeholder="选择分类"
          class="category-select"
          :disabled="readonly"
          allow-clear
          @change="handleCategoryChange"
        >
          <a-select-option value="HR">人力资源</a-select-option>
          <a-select-option value="FINANCE">财务管理</a-select-option>
          <a-select-option value="PROCUREMENT">采购管理</a-select-option>
          <a-select-option value="PROJECT">项目管理</a-select-option>
          <a-select-option value="OTHER">其他</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 中间：操作按钮 -->
    <div class="toolbar-center">
      <a-space>
        <!-- 撤销重做 -->
        <a-button-group>
          <a-button
            :disabled="!canUndo || readonly"
            @click="handleUndo"
          >
            <template #icon>
              <UndoOutlined />
            </template>
            撤销
          </a-button>
          <a-button
            :disabled="!canRedo || readonly"
            @click="handleRedo"
          >
            <template #icon>
              <RedoOutlined />
            </template>
            重做
          </a-button>
        </a-button-group>

        <!-- 缩放控制 -->
        <a-button-group>
          <a-button @click="$emit('zoomOut')">
            <template #icon>
              <ZoomOutOutlined />
            </template>
          </a-button>
          <a-button @click="$emit('zoomReset')">
            {{ Math.round(zoomLevel * 100) }}%
          </a-button>
          <a-button @click="$emit('zoomIn')">
            <template #icon>
              <ZoomInOutlined />
            </template>
          </a-button>
        </a-button-group>

        <!-- 视图控制 -->
        <a-button-group>
          <a-button @click="$emit('fitViewport')">
            <template #icon>
              <FullscreenOutlined />
            </template>
            适应画布
          </a-button>
          <a-button @click="toggleMinimap">
            <template #icon>
              <PictureOutlined />
            </template>
            小地图
          </a-button>
        </a-button-group>

        <!-- BPMN特有功能 -->
        <a-button-group>
          <a-button @click="$emit('validate')">
            <template #icon>
              <CheckCircleOutlined />
            </template>
            验证
          </a-button>
          <a-button @click="toggleProperties">
            <template #icon>
              <SettingOutlined />
            </template>
            属性
          </a-button>
        </a-button-group>
      </a-space>
    </div>

    <!-- 右侧：主要操作 -->
    <div class="toolbar-right">
      <a-space>
        <!-- 导入导出 -->
        <a-dropdown>
          <a-button>
            <template #icon>
              <MoreOutlined />
            </template>
            更多
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="import" @click="$emit('import')">
                <ImportOutlined />
                导入BPMN
              </a-menu-item>
              <a-menu-item key="export" @click="$emit('export')">
                <ExportOutlined />
                导出BPMN
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="exportSvg" @click="handleExportSvg">
                <FileImageOutlined />
                导出图片
              </a-menu-item>
              <a-menu-item key="exportPdf" @click="handleExportPdf">
                <FilePdfOutlined />
                导出PDF
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="clear" @click="handleClear" :disabled="readonly">
                <ClearOutlined />
                清空画布
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <!-- 保存 -->
        <a-button
          type="primary"
          :disabled="!canSave || readonly"
          :loading="saving"
          @click="handleSave"
        >
          <template #icon>
            <SaveOutlined />
          </template>
          保存
        </a-button>

        <!-- 发布 -->
        <a-button
          type="primary"
          :disabled="!canDeploy || readonly"
          :loading="deploying"
          @click="handleDeploy"
        >
          <template #icon>
            <RocketOutlined />
          </template>
          发布
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Modal, message } from 'ant-design-vue'
import {
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  PictureOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  MoreOutlined,
  DownOutlined,
  ImportOutlined,
  ExportOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  ClearOutlined,
  SaveOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'
import type { ProcessDefinition } from '../../../../types/workflow'

// Props
interface Props {
  processDefinition: ProcessDefinition
  canSave?: boolean
  canDeploy?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canSave: false,
  canDeploy: false,
  readonly: false
})

// Emits
const emit = defineEmits<{
  save: []
  deploy: []
  import: []
  export: []
  validate: []
  zoomIn: []
  zoomOut: []
  zoomReset: []
  fitViewport: []
  processChange: [definition: ProcessDefinition]
}>()

// 响应式数据
const saving = ref(false)
const deploying = ref(false)
const canUndo = ref(false)
const canRedo = ref(false)
const zoomLevel = ref(1)
const showMinimap = ref(true)
const showProperties = ref(false)

// 本地流程定义副本
const localProcessDefinition = reactive<ProcessDefinition>({
  ...props.processDefinition
})

// 监听props变化
watch(
  () => props.processDefinition,
  (newDefinition) => {
    Object.assign(localProcessDefinition, newDefinition)
  },
  { deep: true }
)

// 事件处理函数
const handleProcessNameChange = () => {
  emit('processChange', localProcessDefinition)
}

const handleCategoryChange = () => {
  emit('processChange', localProcessDefinition)
}

const handleSave = async () => {
  if (!props.canSave) return
  
  saving.value = true
  try {
    emit('save')
  } finally {
    saving.value = false
  }
}

const handleDeploy = async () => {
  if (!props.canDeploy) return
  
  Modal.confirm({
    title: '确认发布',
    content: '发布后流程将生效，确定要发布吗？',
    onOk: async () => {
      deploying.value = true
      try {
        emit('deploy')
      } finally {
        deploying.value = false
      }
    }
  })
}

const handleUndo = () => {
  // TODO: 实现撤销功能
  message.info('撤销功能开发中...')
}

const handleRedo = () => {
  // TODO: 实现重做功能
  message.info('重做功能开发中...')
}

const toggleMinimap = () => {
  showMinimap.value = !showMinimap.value
  // TODO: 控制小地图显示/隐藏
}

const toggleProperties = () => {
  showProperties.value = !showProperties.value
  // TODO: 控制属性面板显示/隐藏
}

const handleExportSvg = () => {
  // TODO: 实现SVG导出
  message.info('SVG导出功能开发中...')
}

const handleExportPdf = () => {
  // TODO: 实现PDF导出
  message.info('PDF导出功能开发中...')
}

const handleClear = () => {
  Modal.confirm({
    title: '确认清空',
    content: '清空后将删除所有元素，确定要清空吗？',
    onOk: () => {
      // TODO: 实现清空画布
      message.info('清空功能开发中...')
    }
  })
}
</script>

<style lang="less" scoped>
.bpmn-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    flex: 1;
    min-width: 0;

    .process-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .process-name-input {
        width: 200px;
        font-size: 16px;
        font-weight: 500;
      }

      .category-select {
        width: 120px;
      }
    }
  }

  .toolbar-center {
    flex: 0 0 auto;
    margin: 0 24px;
  }

  .toolbar-right {
    flex: 0 0 auto;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .bpmn-toolbar {
    .toolbar-center {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .bpmn-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;

    .toolbar-left,
    .toolbar-right {
      width: 100%;
    }

    .process-info {
      flex-direction: column;
      align-items: stretch;

      .process-name-input,
      .category-select {
        width: 100%;
      }
    }
  }
}
</style>
