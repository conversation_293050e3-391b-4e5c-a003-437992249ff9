<template>
  <div class="bpmn-designer">
    <!-- 工具栏 -->
    <BpmnToolbar
      :process-definition="processDefinition"
      :can-save="canSave"
      :can-deploy="canDeploy"
      :readonly="readonly"
      @save="handleSave"
      @deploy="handleDeploy"
      @import="handleImport"
      @export="handleExport"
      @validate="handleValidate"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @zoom-reset="handleZoomReset"
      @fit-viewport="handleFitViewport"
    />

    <!-- 主要内容区 -->
    <div class="designer-content">
      <!-- BPMN画布容器 -->
      <div class="canvas-container">
        <div
          ref="bpmnContainer"
          class="bpmn-canvas"
        />
        
        <!-- 小地图 -->
        <div
          v-if="showMinimap"
          ref="minimapContainer"
          class="minimap-container"
        />
      </div>

      <!-- 属性面板 -->
      <BpmnProperties
        v-if="showProperties"
        :selected-element="selectedElement"
        :modeler="modeler"
        @property-change="handlePropertyChange"
        @close="showProperties = false"
      />
    </div>

    <!-- 导入弹窗 -->
    <ImportModal
      v-model:visible="importVisible"
      :accept="'.bpmn,.xml'"
      @import-success="handleImportSuccess"
    />

    <!-- 验证结果弹窗 -->
    <ValidationModal
      v-model:visible="validationVisible"
      :validation-result="validationResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import BpmnModeler from 'bpmn-js/lib/Modeler'
import BpmnPropertiesPanel from 'bpmn-js-properties-panel'
import BpmnColorPicker from 'bpmn-js-color-picker'
import minimapModule from 'diagram-js-minimap'

// 导入BPMN样式
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
import 'bpmn-js-properties-panel/dist/assets/properties-panel.css'
import 'diagram-js-minimap/assets/diagram-js-minimap.css'

import type { ProcessDefinition } from '../../../types/workflow'
import { validateBpmnXml } from '../../../utils/bpmn-validator'
import { convertBpmnToFlowDefinition, convertFlowDefinitionToBpmn } from '../../../utils/bpmn-converter'

// 组件引入
import BpmnToolbar from './components/BpmnToolbar.vue'
import BpmnProperties from './components/BpmnProperties.vue'
import ImportModal from '../common/ImportModal.vue'
import ValidationModal from '../common/ValidationModal.vue'

// Props
interface Props {
  processDefinition?: ProcessDefinition
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits
const emit = defineEmits<{
  save: [definition: ProcessDefinition]
  deploy: [definition: ProcessDefinition]
  change: [xml: string]
}>()

// 响应式数据
const bpmnContainer = ref<HTMLElement>()
const minimapContainer = ref<HTMLElement>()
const modeler = ref<BpmnModeler>()
const selectedElement = ref<any>(null)
const showProperties = ref(false)
const showMinimap = ref(true)
const importVisible = ref(false)
const validationVisible = ref(false)
const validationResult = ref<any>(null)

// 流程定义
const processDefinition = reactive<ProcessDefinition>({
  processKey: '',
  processName: '',
  designerType: 'BPMN',
  processDefinitionJson: '',
  bpmnXml: '',
  ...props.processDefinition
})

// 默认BPMN XML模板
const defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`

// 计算属性
const canSave = computed(() => {
  return !props.readonly && 
         processDefinition.processKey && 
         processDefinition.processName &&
         processDefinition.bpmnXml
})

const canDeploy = computed(() => {
  return canSave.value && validateBpmnXml(processDefinition.bpmnXml || '')
})

// 生命周期
onMounted(async () => {
  await nextTick()
  initBpmnModeler()
})

onUnmounted(() => {
  if (modeler.value) {
    modeler.value.destroy()
  }
})

// 监听流程定义变化
watch(
  () => props.processDefinition,
  (newDefinition) => {
    if (newDefinition) {
      Object.assign(processDefinition, newDefinition)
      if (newDefinition.bpmnXml && modeler.value) {
        loadBpmnXml(newDefinition.bpmnXml)
      }
    }
  },
  { deep: true, immediate: true }
)

// 方法
const initBpmnModeler = () => {
  if (!bpmnContainer.value) return

  // 创建BPMN建模器
  modeler.value = new BpmnModeler({
    container: bpmnContainer.value,
    additionalModules: [
      BpmnPropertiesPanel,
      BpmnColorPicker,
      minimapModule
    ],
    propertiesPanel: {
      parent: '#properties-panel'
    },
    minimap: {
      open: showMinimap.value,
      container: minimapContainer.value
    },
    keyboard: {
      bindTo: window
    }
  })

  // 监听选择事件
  modeler.value.on('selection.changed', (event: any) => {
    const { newSelection } = event
    selectedElement.value = newSelection[0] || null
    showProperties.value = !!selectedElement.value
  })

  // 监听元素变化事件
  modeler.value.on('element.changed', () => {
    handleModelChange()
  })

  // 监听命令执行事件
  modeler.value.on('commandStack.changed', () => {
    handleModelChange()
  })

  // 加载初始BPMN
  const initialXml = processDefinition.bpmnXml || defaultBpmnXml
  loadBpmnXml(initialXml)
}

const loadBpmnXml = async (xml: string) => {
  if (!modeler.value) return

  try {
    await modeler.value.importXML(xml)
    
    // 适应画布
    const canvas = modeler.value.get('canvas')
    canvas.zoom('fit-viewport')
    
    message.success('BPMN流程加载成功')
  } catch (error) {
    console.error('加载BPMN失败:', error)
    message.error('加载BPMN失败')
  }
}

const handleModelChange = async () => {
  if (!modeler.value) return

  try {
    const { xml } = await modeler.value.saveXML({ format: true })
    processDefinition.bpmnXml = xml
    
    // 转换为通用流程定义格式
    const flowDefinition = convertBpmnToFlowDefinition(xml)
    processDefinition.processDefinitionJson = JSON.stringify(flowDefinition)
    
    emit('change', xml)
  } catch (error) {
    console.error('保存BPMN失败:', error)
  }
}

const handleSave = () => {
  if (!canSave.value) {
    message.warning('请完善流程信息')
    return
  }
  
  emit('save', processDefinition)
}

const handleDeploy = () => {
  if (!canDeploy.value) {
    message.warning('流程定义不完整，无法发布')
    return
  }
  
  emit('deploy', processDefinition)
}

const handleImport = () => {
  importVisible.value = true
}

const handleExport = async () => {
  if (!modeler.value) return

  try {
    const { xml } = await modeler.value.saveXML({ format: true })
    const dataStr = xml
    const dataBlob = new Blob([dataStr], { type: 'application/xml' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${processDefinition.processKey || 'workflow'}.bpmn`
    link.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('导出BPMN失败:', error)
    message.error('导出失败')
  }
}

const handleValidate = async () => {
  if (!modeler.value) return

  try {
    const { xml } = await modeler.value.saveXML()
    const result = validateBpmnXml(xml)
    validationResult.value = result
    validationVisible.value = true
  } catch (error) {
    console.error('验证BPMN失败:', error)
    message.error('验证失败')
  }
}

const handleZoomIn = () => {
  if (!modeler.value) return
  const canvas = modeler.value.get('canvas')
  canvas.zoom(canvas.zoom() * 1.2)
}

const handleZoomOut = () => {
  if (!modeler.value) return
  const canvas = modeler.value.get('canvas')
  canvas.zoom(canvas.zoom() / 1.2)
}

const handleZoomReset = () => {
  if (!modeler.value) return
  const canvas = modeler.value.get('canvas')
  canvas.zoom(1)
}

const handleFitViewport = () => {
  if (!modeler.value) return
  const canvas = modeler.value.get('canvas')
  canvas.zoom('fit-viewport')
}

const handleImportSuccess = async (content: string) => {
  await loadBpmnXml(content)
  importVisible.value = false
}

const handlePropertyChange = (property: string, value: any) => {
  // 属性变化会自动触发模型变化事件
  console.log('Property changed:', property, value)
}

// 暴露方法给父组件
defineExpose({
  modeler,
  loadBpmnXml,
  exportXml: async () => {
    if (!modeler.value) return ''
    const { xml } = await modeler.value.saveXML({ format: true })
    return xml
  },
  validate: handleValidate
})
</script>

<style lang="less" scoped>
.bpmn-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .designer-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .canvas-container {
      flex: 1;
      position: relative;
      background: #fff;

      .bpmn-canvas {
        width: 100%;
        height: 100%;
      }

      .minimap-container {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 200px;
        height: 150px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.9);
        z-index: 100;
      }
    }
  }
}

// BPMN样式覆盖
:deep(.djs-palette) {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.djs-context-pad) {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.bio-properties-panel) {
  border-left: 1px solid #e8e8e8;
  background: #fafafa;
}
</style>
