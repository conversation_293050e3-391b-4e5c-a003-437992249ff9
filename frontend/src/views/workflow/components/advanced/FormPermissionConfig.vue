<template>
  <div class="form-permission-config">
    <!-- 配置工具栏 -->
    <div class="config-toolbar">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <PlusOutlined />
          添加权限
        </a-button>
        <a-button @click="handleBatchImport">
          <ImportOutlined />
          批量导入
        </a-button>
        <a-button @click="handleBatchExport" :disabled="selectedRowKeys.length === 0">
          <ExportOutlined />
          批量导出
        </a-button>
        <a-button danger @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0">
          <DeleteOutlined />
          批量删除
        </a-button>
      </a-space>
      
      <!-- 搜索过滤 -->
      <div class="search-filters">
        <a-space>
          <a-select
            v-model:value="filters.nodeKey"
            placeholder="选择节点"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="node in nodeOptions"
              :key="node.key"
              :value="node.key"
            >
              {{ node.name }}
            </a-select-option>
          </a-select>
          
          <a-select
            v-model:value="filters.permissionType"
            placeholder="权限类型"
            style="width: 120px"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="READONLY">只读</a-select-option>
            <a-select-option value="EDITABLE">可编辑</a-select-option>
            <a-select-option value="HIDDEN">隐藏</a-select-option>
            <a-select-option value="REQUIRED">必填</a-select-option>
            <a-select-option value="DISABLED">禁用</a-select-option>
          </a-select>
          
          <a-input
            v-model:value="filters.fieldKey"
            placeholder="搜索字段"
            style="width: 150px"
            allow-clear
            @change="handleFilterChange"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-space>
      </div>
    </div>

    <!-- 权限配置表格 -->
    <a-table
      :columns="columns"
      :data-source="permissionList"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 节点信息 -->
        <template v-if="column.key === 'node'">
          <div class="node-info">
            <a-tag color="blue">{{ record.nodeKey || '全局' }}</a-tag>
            <div class="node-name">{{ record.nodeName || '所有节点' }}</div>
          </div>
        </template>

        <!-- 字段信息 -->
        <template v-if="column.key === 'field'">
          <div class="field-info">
            <div class="field-key">{{ record.fieldKey }}</div>
            <div class="field-name">{{ record.fieldName }}</div>
          </div>
        </template>

        <!-- 权限类型 -->
        <template v-if="column.key === 'permissionType'">
          <a-tag :color="getPermissionTypeColor(record.permissionType)">
            {{ getPermissionTypeText(record.permissionType) }}
          </a-tag>
        </template>

        <!-- 适用对象 -->
        <template v-if="column.key === 'target'">
          <div class="target-info">
            <a-tag v-if="record.userId" color="green">
              用户: {{ record.userName }}
            </a-tag>
            <a-tag v-if="record.roleId" color="orange">
              角色: {{ record.roleName }}
            </a-tag>
            <a-tag v-if="!record.userId && !record.roleId" color="default">
              所有用户
            </a-tag>
          </div>
        </template>

        <!-- 必填标识 -->
        <template v-if="column.key === 'required'">
          <a-switch
            :checked="record.required"
            size="small"
            disabled
          />
        </template>

        <!-- 优先级 -->
        <template v-if="column.key === 'priority'">
          <a-tag :color="getPriorityColor(record.priority)">
            {{ record.priority }}
          </a-tag>
        </template>

        <!-- 状态 -->
        <template v-if="column.key === 'enabled'">
          <a-switch
            :checked="record.enabled"
            size="small"
            @change="(checked) => handleToggleEnabled(record, checked)"
          />
        </template>

        <!-- 操作 -->
        <template v-if="column.key === 'actions'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="handleCopy(record)">
              复制
            </a-button>
            <a-popconfirm
              title="确定要删除这个权限配置吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 权限配置弹窗 -->
    <FormPermissionModal
      v-model:visible="modalVisible"
      :permission-data="currentPermission"
      :process-definition-id="processDefinitionId"
      :node-options="nodeOptions"
      :field-options="fieldOptions"
      @success="handleModalSuccess"
    />

    <!-- 批量导入弹窗 -->
    <a-modal
      v-model:visible="importModalVisible"
      title="批量导入权限配置"
      :confirm-loading="importLoading"
      @ok="handleImportConfirm"
    >
      <div class="import-content">
        <a-upload
          v-model:file-list="importFileList"
          :before-upload="beforeUpload"
          accept=".json,.xlsx,.csv"
          :max-count="1"
        >
          <a-button>
            <UploadOutlined />
            选择文件
          </a-button>
        </a-upload>
        
        <div class="import-tips">
          <a-alert
            message="导入说明"
            description="支持JSON、Excel、CSV格式文件。请确保文件格式正确，包含必要的字段信息。"
            type="info"
            show-icon
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  DeleteOutlined,
  SearchOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, UploadFile } from 'ant-design-vue'
import { formPermissionApi } from '../../api/workflow'
import FormPermissionModal from './FormPermissionModal.vue'

// Props
interface Props {
  processDefinitionId: number
  nodeOptions?: any[]
  fieldOptions?: any[]
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const importModalVisible = ref(false)
const importLoading = ref(false)
const permissionList = ref<any[]>([])
const currentPermission = ref<any>(null)
const selectedRowKeys = ref<number[]>([])
const importFileList = ref<UploadFile[]>([])

// 过滤条件
const filters = reactive({
  nodeKey: undefined,
  permissionType: undefined,
  fieldKey: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '节点',
    key: 'node',
    width: 150,
    sorter: true
  },
  {
    title: '字段',
    key: 'field',
    width: 180,
    sorter: true
  },
  {
    title: '权限类型',
    key: 'permissionType',
    width: 120,
    filters: [
      { text: '只读', value: 'READONLY' },
      { text: '可编辑', value: 'EDITABLE' },
      { text: '隐藏', value: 'HIDDEN' },
      { text: '必填', value: 'REQUIRED' },
      { text: '禁用', value: 'DISABLED' }
    ]
  },
  {
    title: '适用对象',
    key: 'target',
    width: 200
  },
  {
    title: '必填',
    key: 'required',
    width: 80,
    align: 'center'
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    sorter: true,
    align: 'center'
  },
  {
    title: '状态',
    key: 'enabled',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}))

// 生命周期
onMounted(() => {
  loadPermissions()
})

// 方法
const loadPermissions = async () => {
  loading.value = true
  try {
    const params = {
      processDefinitionId: props.processDefinitionId,
      page: pagination.current - 1,
      size: pagination.pageSize,
      ...filters
    }
    
    const response = await formPermissionApi.getPermissions(params)
    if (response.code === 200) {
      permissionList.value = response.data.content || []
      pagination.total = response.data.totalElements || 0
    } else {
      message.error('加载权限配置失败')
    }
  } catch (error) {
    console.error('加载权限配置失败:', error)
    message.error('加载权限配置失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  currentPermission.value = {
    processDefinitionId: props.processDefinitionId,
    enabled: true,
    priority: 0,
    required: false
  }
  modalVisible.value = true
}

const handleEdit = (record: any) => {
  currentPermission.value = { ...record }
  modalVisible.value = true
}

const handleCopy = (record: any) => {
  currentPermission.value = {
    ...record,
    id: undefined,
    fieldKey: record.fieldKey + '_copy'
  }
  modalVisible.value = true
}

const handleDelete = async (record: any) => {
  try {
    const response = await formPermissionApi.deletePermission(record.id)
    if (response.code === 200) {
      message.success('删除成功')
      loadPermissions()
    } else {
      message.error('删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录')
    return
  }
  
  try {
    const response = await formPermissionApi.batchDeletePermissions(selectedRowKeys.value)
    if (response.code === 200) {
      message.success('批量删除成功')
      selectedRowKeys.value = []
      loadPermissions()
    } else {
      message.error('批量删除失败')
    }
  } catch (error) {
    console.error('批量删除失败:', error)
    message.error('批量删除失败')
  }
}

const handleToggleEnabled = async (record: any, enabled: boolean) => {
  try {
    const response = await formPermissionApi.updatePermissionEnabled(record.id, enabled)
    if (response.code === 200) {
      record.enabled = enabled
      message.success(enabled ? '启用成功' : '禁用成功')
    } else {
      message.error('操作失败')
    }
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  }
}

const handleBatchImport = () => {
  importFileList.value = []
  importModalVisible.value = true
}

const handleBatchExport = async () => {
  try {
    const response = await formPermissionApi.exportPermissions({
      processDefinitionId: props.processDefinitionId,
      ids: selectedRowKeys.value
    })
    
    // 创建下载链接
    const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `form-permissions-${Date.now()}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

const handleImportConfirm = async () => {
  if (importFileList.value.length === 0) {
    message.warning('请选择要导入的文件')
    return
  }
  
  importLoading.value = true
  try {
    const file = importFileList.value[0]
    const formData = new FormData()
    formData.append('file', file.originFileObj!)
    formData.append('processDefinitionId', String(props.processDefinitionId))
    
    const response = await formPermissionApi.importPermissions(formData)
    if (response.code === 200) {
      message.success('导入成功')
      importModalVisible.value = false
      loadPermissions()
    } else {
      message.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadPermissions()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPermissions()
}

const handleModalSuccess = () => {
  modalVisible.value = false
  loadPermissions()
}

const beforeUpload = () => {
  return false // 阻止自动上传
}

// 辅助方法
const getPermissionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    READONLY: 'blue',
    EDITABLE: 'green',
    HIDDEN: 'red',
    REQUIRED: 'orange',
    DISABLED: 'gray'
  }
  return colorMap[type] || 'default'
}

const getPermissionTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    READONLY: '只读',
    EDITABLE: '可编辑',
    HIDDEN: '隐藏',
    REQUIRED: '必填',
    DISABLED: '禁用'
  }
  return textMap[type] || type
}

const getPriorityColor = (priority: number) => {
  if (priority >= 100) return 'red'
  if (priority >= 50) return 'orange'
  if (priority >= 10) return 'blue'
  return 'default'
}
</script>

<style lang="less" scoped>
.form-permission-config {
  .config-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;

    .search-filters {
      .ant-space {
        flex-wrap: wrap;
      }
    }
  }

  .node-info {
    .node-name {
      font-size: 12px;
      color: #999;
      margin-top: 2px;
    }
  }

  .field-info {
    .field-key {
      font-weight: 500;
      color: #333;
    }

    .field-name {
      font-size: 12px;
      color: #999;
      margin-top: 2px;
    }
  }

  .target-info {
    .ant-tag {
      margin-bottom: 4px;
    }
  }

  .import-content {
    .import-tips {
      margin-top: 16px;
    }
  }
}

:deep(.ant-table-tbody > tr > td) {
  vertical-align: top;
}
</style>
