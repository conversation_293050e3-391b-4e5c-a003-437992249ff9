<template>
  <div class="performance-monitor">
    <!-- 性能概览卡片 -->
    <a-row :gutter="16" class="overview-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="活跃流程数"
            :value="performanceData.activeProcesses"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <PlayCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="活跃任务数"
            :value="performanceData.activeTasks"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="内存使用率"
            :value="performanceData.memoryUsageRatio"
            suffix="%"
            :precision="1"
            :value-style="{ color: getMemoryColor(performanceData.memoryUsageRatio) }"
          >
            <template #prefix>
              <DatabaseOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="系统吞吐量"
            :value="performanceData.throughput"
            suffix="ops/s"
            :precision="2"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <ThunderboltOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 性能图表 -->
    <a-row :gutter="16" class="chart-section">
      <a-col :span="12">
        <a-card title="响应时间趋势" :loading="loading">
          <div ref="responseTimeChart" class="chart-container"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="吞吐量趋势" :loading="loading">
          <div ref="throughputChart" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-section">
      <a-col :span="12">
        <a-card title="内存使用趋势" :loading="loading">
          <div ref="memoryChart" class="chart-container"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="错误率统计" :loading="loading">
          <div ref="errorChart" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 详细性能指标表格 -->
    <a-card title="详细性能指标" class="metrics-table">
      <a-table
        :columns="metricsColumns"
        :data-source="metricsData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'value'">
            <span :style="{ color: getMetricColor(record.status) }">
              {{ formatMetricValue(record.value, record.unit) }}
            </span>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'trend'">
            <span :class="getTrendClass(record.trend)">
              <ArrowUpOutlined v-if="record.trend > 0" />
              <ArrowDownOutlined v-if="record.trend < 0" />
              <MinusOutlined v-if="record.trend === 0" />
              {{ Math.abs(record.trend) }}%
            </span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 实时日志 -->
    <a-card title="实时性能日志" class="log-section">
      <div class="log-controls">
        <a-space>
          <a-switch
            v-model:checked="autoRefresh"
            checked-children="自动刷新"
            un-checked-children="手动刷新"
            @change="handleAutoRefreshChange"
          />
          <a-button @click="refreshData">
            <ReloadOutlined />
            刷新数据
          </a-button>
          <a-button @click="clearLogs">
            <ClearOutlined />
            清空日志
          </a-button>
        </a-space>
      </div>
      
      <div class="log-container">
        <div
          v-for="(log, index) in performanceLogs"
          :key="index"
          class="log-item"
          :class="getLogLevelClass(log.level)"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  DatabaseOutlined,
  ThunderboltOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  ReloadOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import type { TableColumnsType } from 'ant-design-vue'
import { performanceApi } from '../../api/workflow'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const performanceData = reactive({
  activeProcesses: 0,
  activeTasks: 0,
  memoryUsageRatio: 0,
  throughput: 0
})

const performanceLogs = ref<any[]>([])
let refreshTimer: NodeJS.Timeout | null = null

// 图表引用
const responseTimeChart = ref<HTMLElement>()
const throughputChart = ref<HTMLElement>()
const memoryChart = ref<HTMLElement>()
const errorChart = ref<HTMLElement>()

// 图表实例
let responseTimeChartInstance: echarts.ECharts | null = null
let throughputChartInstance: echarts.ECharts | null = null
let memoryChartInstance: echarts.ECharts | null = null
let errorChartInstance: echarts.ECharts | null = null

// 性能指标表格列
const metricsColumns: TableColumnsType = [
  {
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '当前值',
    dataIndex: 'value',
    key: 'value',
    width: 120,
    align: 'right'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '趋势',
    dataIndex: 'trend',
    key: 'trend',
    width: 100,
    align: 'center'
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  }
]

// 性能指标数据
const metricsData = ref([
  {
    name: '平均响应时间',
    value: 0,
    unit: 'ms',
    status: 'normal',
    trend: 0,
    description: '系统平均响应时间'
  },
  {
    name: '流程完成率',
    value: 0,
    unit: '%',
    status: 'normal',
    trend: 0,
    description: '流程成功完成的比例'
  },
  {
    name: '任务处理速度',
    value: 0,
    unit: 'tasks/min',
    status: 'normal',
    trend: 0,
    description: '每分钟处理的任务数量'
  },
  {
    name: 'CPU使用率',
    value: 0,
    unit: '%',
    status: 'normal',
    trend: 0,
    description: '系统CPU使用率'
  },
  {
    name: '数据库连接数',
    value: 0,
    unit: 'connections',
    status: 'normal',
    trend: 0,
    description: '当前数据库连接数'
  },
  {
    name: '缓存命中率',
    value: 0,
    unit: '%',
    status: 'normal',
    trend: 0,
    description: '缓存命中的比例'
  }
])

// 生命周期
onMounted(() => {
  initCharts()
  loadPerformanceData()
  
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
  destroyCharts()
})

// 方法
const initCharts = async () => {
  await nextTick()
  
  // 初始化响应时间图表
  if (responseTimeChart.value) {
    responseTimeChartInstance = echarts.init(responseTimeChart.value)
    responseTimeChartInstance.setOption(getResponseTimeChartOption())
  }
  
  // 初始化吞吐量图表
  if (throughputChart.value) {
    throughputChartInstance = echarts.init(throughputChart.value)
    throughputChartInstance.setOption(getThroughputChartOption())
  }
  
  // 初始化内存图表
  if (memoryChart.value) {
    memoryChartInstance = echarts.init(memoryChart.value)
    memoryChartInstance.setOption(getMemoryChartOption())
  }
  
  // 初始化错误率图表
  if (errorChart.value) {
    errorChartInstance = echarts.init(errorChart.value)
    errorChartInstance.setOption(getErrorChartOption())
  }
}

const destroyCharts = () => {
  responseTimeChartInstance?.dispose()
  throughputChartInstance?.dispose()
  memoryChartInstance?.dispose()
  errorChartInstance?.dispose()
}

const loadPerformanceData = async () => {
  loading.value = true
  try {
    const response = await performanceApi.getPerformanceReport()
    if (response.code === 200) {
      const data = response.data
      
      // 更新概览数据
      performanceData.activeProcesses = data.currentActiveProcesses
      performanceData.activeTasks = data.currentActiveTasks
      performanceData.memoryUsageRatio = data.memoryUsageRatio * 100
      performanceData.throughput = calculateThroughput(data)
      
      // 更新指标数据
      updateMetricsData(data)
      
      // 更新图表数据
      updateCharts(data)
      
      // 添加性能日志
      addPerformanceLog('INFO', `性能数据更新完成 - 活跃流程: ${data.currentActiveProcesses}, 活跃任务: ${data.currentActiveTasks}`)
      
    } else {
      message.error('加载性能数据失败')
    }
  } catch (error) {
    console.error('加载性能数据失败:', error)
    message.error('加载性能数据失败')
    addPerformanceLog('ERROR', '性能数据加载失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const updateMetricsData = (data: any) => {
  metricsData.value[0].value = data.avgProcessExecutionTime
  metricsData.value[1].value = calculateCompletionRate(data)
  metricsData.value[2].value = calculateTaskProcessingSpeed(data)
  metricsData.value[3].value = Math.random() * 100 // 模拟CPU使用率
  metricsData.value[4].value = Math.floor(Math.random() * 50) + 10 // 模拟数据库连接数
  metricsData.value[5].value = Math.random() * 100 // 模拟缓存命中率
  
  // 更新状态和趋势
  metricsData.value.forEach(metric => {
    metric.status = getMetricStatus(metric.value, metric.name)
    metric.trend = Math.floor(Math.random() * 21) - 10 // -10% 到 +10%
  })
}

const updateCharts = (data: any) => {
  // 更新响应时间图表
  if (responseTimeChartInstance) {
    const option = responseTimeChartInstance.getOption()
    const now = new Date()
    option.xAxis[0].data.push(formatTime(now))
    option.series[0].data.push(data.avgProcessExecutionTime)
    option.series[1].data.push(data.avgTaskExecutionTime)
    
    // 保持最近30个数据点
    if (option.xAxis[0].data.length > 30) {
      option.xAxis[0].data.shift()
      option.series[0].data.shift()
      option.series[1].data.shift()
    }
    
    responseTimeChartInstance.setOption(option)
  }
  
  // 更新其他图表...
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    loadPerformanceData()
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const handleAutoRefreshChange = (checked: boolean) => {
  if (checked) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const refreshData = () => {
  loadPerformanceData()
}

const clearLogs = () => {
  performanceLogs.value = []
}

const addPerformanceLog = (level: string, message: string) => {
  performanceLogs.value.unshift({
    timestamp: new Date(),
    level,
    message
  })
  
  // 保持最近100条日志
  if (performanceLogs.value.length > 100) {
    performanceLogs.value = performanceLogs.value.slice(0, 100)
  }
}

// 辅助方法
const getMemoryColor = (ratio: number) => {
  if (ratio > 80) return '#ff4d4f'
  if (ratio > 60) return '#faad14'
  return '#52c41a'
}

const getMetricColor = (status: string) => {
  const colorMap: Record<string, string> = {
    normal: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f'
  }
  return colorMap[status] || '#666'
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    normal: 'green',
    warning: 'orange',
    error: 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    normal: '正常',
    warning: '警告',
    error: '异常'
  }
  return textMap[status] || status
}

const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const getLogLevelClass = (level: string) => {
  return `log-${level.toLowerCase()}`
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}

const formatMetricValue = (value: number, unit: string) => {
  if (unit === '%') {
    return value.toFixed(1) + unit
  } else if (unit === 'ms') {
    return value.toFixed(0) + unit
  } else {
    return value.toFixed(2) + (unit ? ' ' + unit : '')
  }
}

const calculateThroughput = (data: any) => {
  return (data.totalProcessCompletes + data.totalTaskCompletes) / 60 // 简化计算
}

const calculateCompletionRate = (data: any) => {
  const total = data.totalProcessStarts
  const completed = data.totalProcessCompletes
  return total > 0 ? (completed / total * 100) : 0
}

const calculateTaskProcessingSpeed = (data: any) => {
  return data.totalTaskCompletes / 60 // 简化计算
}

const getMetricStatus = (value: number, name: string) => {
  // 根据指标名称和值判断状态
  if (name.includes('响应时间') && value > 1000) return 'warning'
  if (name.includes('CPU') && value > 80) return 'warning'
  if (name.includes('内存') && value > 80) return 'warning'
  return 'normal'
}

// 图表配置
const getResponseTimeChartOption = () => ({
  title: { text: '响应时间趋势', left: 'center' },
  tooltip: { trigger: 'axis' },
  legend: { data: ['流程响应时间', '任务响应时间'], bottom: 0 },
  xAxis: { type: 'category', data: [] },
  yAxis: { type: 'value', name: '时间(ms)' },
  series: [
    { name: '流程响应时间', type: 'line', data: [], smooth: true },
    { name: '任务响应时间', type: 'line', data: [], smooth: true }
  ]
})

const getThroughputChartOption = () => ({
  title: { text: '吞吐量趋势', left: 'center' },
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: [] },
  yAxis: { type: 'value', name: '操作/秒' },
  series: [{ name: '吞吐量', type: 'line', data: [], smooth: true, areaStyle: {} }]
})

const getMemoryChartOption = () => ({
  title: { text: '内存使用趋势', left: 'center' },
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: [] },
  yAxis: { type: 'value', name: '使用率(%)' },
  series: [{ name: '内存使用率', type: 'line', data: [], smooth: true, areaStyle: {} }]
})

const getErrorChartOption = () => ({
  title: { text: '错误率统计', left: 'center' },
  tooltip: { trigger: 'item' },
  series: [{
    name: '错误类型',
    type: 'pie',
    radius: '50%',
    data: [
      { value: 10, name: '业务错误' },
      { value: 5, name: '系统错误' },
      { value: 2, name: '网络错误' }
    ]
  }]
})
</script>

<style lang="less" scoped>
.performance-monitor {
  .overview-cards {
    margin-bottom: 24px;
  }

  .chart-section {
    margin-bottom: 24px;

    .chart-container {
      height: 300px;
    }
  }

  .metrics-table {
    margin-bottom: 24px;

    .trend-up {
      color: #52c41a;
    }

    .trend-down {
      color: #ff4d4f;
    }

    .trend-stable {
      color: #666;
    }
  }

  .log-section {
    .log-controls {
      margin-bottom: 16px;
    }

    .log-container {
      height: 300px;
      overflow-y: auto;
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;

      .log-item {
        display: flex;
        margin-bottom: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;

        .log-time {
          color: #999;
          margin-right: 8px;
          min-width: 80px;
        }

        .log-level {
          margin-right: 8px;
          min-width: 50px;
          font-weight: bold;
        }

        .log-message {
          flex: 1;
        }

        &.log-info .log-level {
          color: #1890ff;
        }

        &.log-warning .log-level {
          color: #faad14;
        }

        &.log-error .log-level {
          color: #ff4d4f;
        }
      }
    }
  }
}
</style>
