<template>
  <div class="department-selector">
    <a-tree-select
      v-model:value="selectedValue"
      :tree-data="deptTreeData"
      :placeholder="placeholder"
      :loading="loading"
      :disabled="disabled"
      :multiple="multiple"
      :allow-clear="true"
      :show-search="true"
      :filter-tree-node="filterTreeNode"
      :field-names="{ label: 'name', value: 'id', children: 'children' }"
      tree-default-expand-all
      @change="handleChange"
      @load-data="loadChildrenData"
    >
      <template #title="{ name, id, userCount }">
        <div class="dept-node">
          <span class="dept-name">{{ name }}</span>
          <span v-if="userCount > 0" class="user-count">({{ userCount }}人)</span>
        </div>
      </template>
    </a-tree-select>

    <!-- 已选部门展示 -->
    <div v-if="selectedDepts.length > 0" class="selected-depts">
      <a-tag
        v-for="dept in selectedDepts"
        :key="dept.id"
        closable
        @close="removeDept(dept.id)"
      >
        <FolderOutlined />
        <span style="margin-left: 4px">{{ dept.name }}</span>
      </a-tag>
    </div>

    <!-- 部门详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="部门详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedDeptDetail" class="dept-detail">
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="部门名称">
            {{ selectedDeptDetail.name }}
          </a-descriptions-item>
          <a-descriptions-item label="部门编码">
            {{ selectedDeptDetail.code }}
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            {{ selectedDeptDetail.leaderName }}
          </a-descriptions-item>
          <a-descriptions-item label="人员数量">
            {{ selectedDeptDetail.userCount }}人
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(selectedDeptDetail.createdTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="selectedDeptDetail.status === 'ACTIVE' ? 'green' : 'red'">
              {{ selectedDeptDetail.status === 'ACTIVE' ? '正常' : '停用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="部门描述" :span="2">
            {{ selectedDeptDetail.description || '无' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 部门人员列表 -->
        <a-divider>部门人员</a-divider>
        <div class="dept-users">
          <a-list
            :data-source="deptUsers"
            :loading="usersLoading"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :src="item.avatar">
                      {{ item.name?.charAt(0) }}
                    </a-avatar>
                  </template>
                  <template #title>
                    {{ item.name }}
                  </template>
                  <template #description>
                    {{ item.positionName }} | {{ item.email }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { FolderOutlined } from '@ant-design/icons-vue'
import { departmentApi, userApi } from '../../api/system'
import { formatDateTime } from '../../utils/date-utils'

// Props
interface Props {
  value?: number | number[]
  multiple?: boolean
  placeholder?: string
  disabled?: boolean
  showDetail?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: '请选择部门',
  disabled: false,
  showDetail: false
})

// Emits
const emit = defineEmits<{
  'update:value': [value: number | number[] | null]
  'change': [value: number | number[] | null, depts: any[]]
}>()

// 响应式数据
const loading = ref(false)
const deptTreeData = ref<any[]>([])
const selectedDepts = ref<any[]>([])
const detailModalVisible = ref(false)
const selectedDeptDetail = ref<any>(null)
const deptUsers = ref<any[]>([])
const usersLoading = ref(false)

// 计算属性
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 监听value变化
watch(
  () => props.value,
  async (newValue) => {
    if (newValue) {
      await loadSelectedDepts(newValue)
    } else {
      selectedDepts.value = []
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  loadDeptTree()
})

// 方法
const loadDeptTree = async () => {
  loading.value = true
  try {
    const response = await departmentApi.getDeptTree()
    if (response.code === 200) {
      deptTreeData.value = processTreeData(response.data || [])
    } else {
      message.error('加载部门树失败')
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
    message.error('加载部门树失败')
  } finally {
    loading.value = false
  }
}

const processTreeData = (data: any[]): any[] => {
  return data.map(item => ({
    ...item,
    isLeaf: !item.children || item.children.length === 0,
    children: item.children ? processTreeData(item.children) : undefined
  }))
}

const loadChildrenData = async (treeNode: any) => {
  if (treeNode.children && treeNode.children.length > 0) {
    return
  }

  try {
    const response = await departmentApi.getChildDepts(treeNode.id)
    if (response.code === 200) {
      const children = processTreeData(response.data || [])
      treeNode.children = children
      treeNode.isLeaf = children.length === 0
    }
  } catch (error) {
    console.error('加载子部门失败:', error)
    message.error('加载子部门失败')
  }
}

const filterTreeNode = (inputValue: string, treeNode: any) => {
  return treeNode.name.toLowerCase().includes(inputValue.toLowerCase()) ||
         (treeNode.code && treeNode.code.toLowerCase().includes(inputValue.toLowerCase()))
}

const handleChange = (value: number | number[]) => {
  emit('update:value', value)
  emit('change', value, selectedDepts.value)
}

const removeDept = (deptId: number) => {
  if (props.multiple && Array.isArray(props.value)) {
    const newValue = props.value.filter(id => id !== deptId)
    emit('update:value', newValue)
  }
}

const loadSelectedDepts = async (deptIds: number | number[]) => {
  const ids = Array.isArray(deptIds) ? deptIds : [deptIds]
  if (ids.length === 0) {
    selectedDepts.value = []
    return
  }

  try {
    const response = await departmentApi.getDeptsByIds(ids)
    if (response.code === 200) {
      selectedDepts.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门信息失败:', error)
  }
}

const showDeptDetail = async (deptId: number) => {
  if (!props.showDetail) return

  try {
    const response = await departmentApi.getDeptDetail(deptId)
    if (response.code === 200) {
      selectedDeptDetail.value = response.data
      detailModalVisible.value = true
      await loadDeptUsers(deptId)
    }
  } catch (error) {
    console.error('加载部门详情失败:', error)
    message.error('加载部门详情失败')
  }
}

const loadDeptUsers = async (deptId: number) => {
  usersLoading.value = true
  try {
    const response = await userApi.getUsersByDept(deptId)
    if (response.code === 200) {
      deptUsers.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门人员失败:', error)
    message.error('加载部门人员失败')
  } finally {
    usersLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.department-selector {
  .dept-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .dept-name {
      flex: 1;
      color: #333;
    }

    .user-count {
      font-size: 12px;
      color: #999;
      margin-left: 8px;
    }
  }

  .selected-depts {
    margin-top: 8px;

    .ant-tag {
      margin-bottom: 4px;
      display: inline-flex;
      align-items: center;
    }
  }
}

.dept-detail {
  .dept-users {
    max-height: 300px;
    overflow-y: auto;
  }
}

:deep(.ant-tree-select-dropdown) {
  .ant-select-tree-node-content-wrapper {
    width: 100%;
  }
}
</style>
