<template>
  <div class="task-action-buttons">
    <a-space :size="8">
      <!-- 签收按钮 -->
      <a-button
        v-if="canClaim"
        type="primary"
        size="small"
        :loading="actionLoading.claim"
        @click="handleClaim"
      >
        <HandOutlined />
        签收
      </a-button>

      <!-- 审批按钮 -->
      <a-button
        v-if="canApprove"
        type="primary"
        size="small"
        :loading="actionLoading.approve"
        @click="handleApprove"
      >
        <CheckCircleOutlined />
        审批
      </a-button>

      <!-- 驳回按钮 -->
      <a-button
        v-if="canReject"
        danger
        size="small"
        :loading="actionLoading.reject"
        @click="handleReject"
      >
        <CloseCircleOutlined />
        驳回
      </a-button>

      <!-- 更多操作下拉菜单 -->
      <a-dropdown v-if="hasMoreActions" :trigger="['click']">
        <a-button size="small">
          更多
          <DownOutlined />
        </a-button>
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <!-- 转办 -->
            <a-menu-item v-if="canTransfer" key="transfer">
              <SwapOutlined />
              转办
            </a-menu-item>
            
            <!-- 委派 -->
            <a-menu-item v-if="canDelegate" key="delegate">
              <UserSwitchOutlined />
              委派
            </a-menu-item>
            
            <!-- 加签 -->
            <a-menu-item v-if="canAddSign" key="addSign">
              <UserAddOutlined />
              加签
            </a-menu-item>
            
            <!-- 减签 -->
            <a-menu-item v-if="canRemoveSign" key="removeSign">
              <UserDeleteOutlined />
              减签
            </a-menu-item>
            
            <a-menu-divider v-if="canTransfer || canDelegate || canAddSign || canRemoveSign" />
            
            <!-- 撤销 -->
            <a-menu-item v-if="canCancel" key="cancel" danger>
              <StopOutlined />
              撤销
            </a-menu-item>
            
            <!-- 终止 -->
            <a-menu-item v-if="canTerminate" key="terminate" danger>
              <ExclamationCircleOutlined />
              终止
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>

      <!-- 查看详情按钮 -->
      <a-button
        v-if="showViewButton"
        size="small"
        @click="handleView"
      >
        <EyeOutlined />
        查看
      </a-button>
    </a-space>

    <!-- 任务审批弹窗 -->
    <TaskApprovalModal
      v-model:visible="approvalModalVisible"
      :task-info="taskInfo"
      :form-fields="formFields"
      :rejectable-nodes="rejectableNodes"
      @success="handleActionSuccess"
    />

    <!-- 快速操作确认弹窗 -->
    <a-modal
      v-model:visible="quickActionModalVisible"
      :title="quickActionTitle"
      :confirm-loading="quickActionLoading"
      @ok="confirmQuickAction"
    >
      <div class="quick-action-content">
        <a-form layout="vertical" :model="quickActionForm">
          <!-- 目标用户选择（转办/委派时） -->
          <a-form-item
            v-if="quickActionType === 'transfer' || quickActionType === 'delegate'"
            :label="quickActionType === 'transfer' ? '转办给' : '委派给'"
            name="targetUserId"
            :rules="[{ required: true, message: '请选择目标用户' }]"
          >
            <UserSelector
              v-model:value="quickActionForm.targetUserId"
              v-model:user-name="quickActionForm.targetUserName"
              placeholder="请选择目标用户"
              :multiple="false"
            />
          </a-form-item>

          <!-- 用户选择（加签/减签时） -->
          <a-form-item
            v-if="quickActionType === 'addSign' || quickActionType === 'removeSign'"
            :label="quickActionType === 'addSign' ? '加签用户' : '减签用户'"
            name="userIds"
            :rules="[{ required: true, message: '请选择用户' }]"
          >
            <UserSelector
              v-model:value="quickActionForm.userIds"
              placeholder="请选择用户"
              :multiple="true"
            />
          </a-form-item>

          <!-- 原因说明 -->
          <a-form-item
            :label="getReasonLabel(quickActionType)"
            name="reason"
            :rules="getReasonRules(quickActionType)"
          >
            <a-textarea
              v-model:value="quickActionForm.reason"
              :placeholder="getReasonPlaceholder(quickActionType)"
              :rows="3"
              show-count
              :maxlength="200"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  HandOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SwapOutlined,
  UserSwitchOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { taskApi } from '../../api/workflow'
import TaskApprovalModal from './TaskApprovalModal.vue'
import UserSelector from './UserSelector.vue'

// Props
interface Props {
  taskInfo: any
  currentUserId?: number
  formFields?: any[]
  rejectableNodes?: any[]
  showViewButton?: boolean
  permissions?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  showViewButton: true,
  permissions: () => []
})

// Emits
const emit = defineEmits<{
  'action-success': [action: string, result: any]
  'view-task': [taskInfo: any]
}>()

// 响应式数据
const actionLoading = reactive({
  claim: false,
  approve: false,
  reject: false
})

const approvalModalVisible = ref(false)
const quickActionModalVisible = ref(false)
const quickActionLoading = ref(false)
const quickActionType = ref('')

const quickActionForm = reactive({
  targetUserId: null,
  targetUserName: '',
  userIds: [],
  reason: ''
})

// 计算属性
const canClaim = computed(() => {
  return props.taskInfo.status === 'CREATED' && 
         !props.taskInfo.assigneeId &&
         props.taskInfo.candidateUsers?.includes(props.currentUserId?.toString())
})

const canApprove = computed(() => {
  return (props.taskInfo.status === 'CREATED' || props.taskInfo.status === 'CLAIMED') &&
         (props.taskInfo.assigneeId === props.currentUserId ||
          props.taskInfo.candidateUsers?.includes(props.currentUserId?.toString()))
})

const canReject = computed(() => {
  return canApprove.value
})

const canTransfer = computed(() => {
  return canApprove.value && props.permissions.includes('TRANSFER')
})

const canDelegate = computed(() => {
  return canApprove.value && props.permissions.includes('DELEGATE')
})

const canAddSign = computed(() => {
  return canApprove.value && 
         (props.taskInfo.approvalType === 'MULTI_AND' || props.taskInfo.approvalType === 'MULTI_OR') &&
         props.permissions.includes('ADD_SIGN')
})

const canRemoveSign = computed(() => {
  return canApprove.value && 
         (props.taskInfo.approvalType === 'MULTI_AND' || props.taskInfo.approvalType === 'MULTI_OR') &&
         props.permissions.includes('REMOVE_SIGN')
})

const canCancel = computed(() => {
  return props.taskInfo.initiatorId === props.currentUserId &&
         !props.taskInfo.isFinished &&
         props.permissions.includes('CANCEL')
})

const canTerminate = computed(() => {
  return props.permissions.includes('TERMINATE')
})

const hasMoreActions = computed(() => {
  return canTransfer.value || canDelegate.value || canAddSign.value || 
         canRemoveSign.value || canCancel.value || canTerminate.value
})

const quickActionTitle = computed(() => {
  const titleMap: Record<string, string> = {
    transfer: '转办任务',
    delegate: '委派任务',
    addSign: '加签',
    removeSign: '减签',
    cancel: '撤销任务',
    terminate: '终止任务'
  }
  return titleMap[quickActionType.value] || ''
})

// 方法
const handleClaim = async () => {
  actionLoading.claim = true
  try {
    const response = await taskApi.claimTask(props.taskInfo.id, {
      userId: props.currentUserId,
      userName: getCurrentUserName()
    })
    
    if (response.code === 200) {
      message.success('签收成功')
      emit('action-success', 'claim', response.data)
    } else {
      message.error(response.message || '签收失败')
    }
  } catch (error) {
    console.error('签收失败:', error)
    message.error('签收失败')
  } finally {
    actionLoading.claim = false
  }
}

const handleApprove = () => {
  approvalModalVisible.value = true
}

const handleReject = () => {
  approvalModalVisible.value = true
}

const handleView = () => {
  emit('view-task', props.taskInfo)
}

const handleMenuClick = ({ key }: { key: string }) => {
  quickActionType.value = key
  
  if (key === 'cancel' || key === 'terminate') {
    // 直接确认操作
    confirmQuickAction()
  } else {
    // 显示表单
    resetQuickActionForm()
    quickActionModalVisible.value = true
  }
}

const resetQuickActionForm = () => {
  Object.assign(quickActionForm, {
    targetUserId: null,
    targetUserName: '',
    userIds: [],
    reason: ''
  })
}

const confirmQuickAction = async () => {
  quickActionLoading.value = true
  
  try {
    let response
    const baseData = {
      userId: props.currentUserId,
      userName: getCurrentUserName(),
      reason: quickActionForm.reason
    }

    switch (quickActionType.value) {
      case 'transfer':
        response = await taskApi.transferTask(props.taskInfo.id, {
          ...baseData,
          targetUserId: quickActionForm.targetUserId
        })
        break
        
      case 'delegate':
        response = await taskApi.delegateTask(props.taskInfo.id, {
          ...baseData,
          targetUserId: quickActionForm.targetUserId
        })
        break
        
      case 'addSign':
        response = await taskApi.addSign(props.taskInfo.id, {
          ...baseData,
          userIds: quickActionForm.userIds
        })
        break
        
      case 'removeSign':
        response = await taskApi.removeSign(props.taskInfo.id, {
          ...baseData,
          userIds: quickActionForm.userIds
        })
        break
        
      case 'cancel':
        response = await taskApi.cancelTask(props.taskInfo.id, baseData)
        break
        
      case 'terminate':
        response = await taskApi.terminateTask(props.taskInfo.id, baseData)
        break
    }

    if (response?.code === 200) {
      message.success(response.message || '操作成功')
      emit('action-success', quickActionType.value, response.data)
      quickActionModalVisible.value = false
    } else {
      message.error(response?.message || '操作失败')
    }
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  } finally {
    quickActionLoading.value = false
  }
}

const handleActionSuccess = (result: any) => {
  emit('action-success', 'approval', result)
}

const getReasonLabel = (actionType: string) => {
  const labelMap: Record<string, string> = {
    transfer: '转办原因',
    delegate: '委派原因',
    addSign: '加签原因',
    removeSign: '减签原因',
    cancel: '撤销原因',
    terminate: '终止原因'
  }
  return labelMap[actionType] || '原因'
}

const getReasonPlaceholder = (actionType: string) => {
  const placeholderMap: Record<string, string> = {
    transfer: '请输入转办原因',
    delegate: '请输入委派原因',
    addSign: '请输入加签原因',
    removeSign: '请输入减签原因',
    cancel: '请输入撤销原因',
    terminate: '请输入终止原因'
  }
  return placeholderMap[actionType] || '请输入原因'
}

const getReasonRules = (actionType: string) => {
  if (actionType === 'cancel' || actionType === 'terminate') {
    return [{ required: true, message: '请输入原因' }]
  }
  return []
}

const getCurrentUserName = () => {
  // TODO: 从用户上下文获取当前用户姓名
  return '当前用户'
}
</script>

<style lang="less" scoped>
.task-action-buttons {
  .quick-action-content {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
