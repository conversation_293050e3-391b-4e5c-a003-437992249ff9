<template>
  <div class="dynamic-form">
    <a-form
      ref="formRef"
      :model="formModel"
      :rules="formRules"
      layout="vertical"
    >
      <template v-for="field in fields" :key="field.key">
        <!-- 文本输入框 -->
        <a-form-item
          v-if="field.type === 'input'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-input
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :maxlength="field.maxLength"
            :show-count="field.showCount"
          />
        </a-form-item>

        <!-- 多行文本 -->
        <a-form-item
          v-else-if="field.type === 'textarea'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-textarea
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :rows="field.rows || 4"
            :maxlength="field.maxLength"
            :show-count="field.showCount"
          />
        </a-form-item>

        <!-- 数字输入框 -->
        <a-form-item
          v-else-if="field.type === 'number'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-input-number
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :min="field.min"
            :max="field.max"
            :precision="field.precision"
            :step="field.step"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 选择框 -->
        <a-form-item
          v-else-if="field.type === 'select'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-select
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :mode="field.multiple ? 'multiple' : undefined"
            :allow-clear="field.allowClear"
          >
            <a-select-option
              v-for="option in field.options"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 单选框组 -->
        <a-form-item
          v-else-if="field.type === 'radio'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-radio-group
            v-model:value="formModel[field.key]"
            :disabled="readonly || field.readonly"
          >
            <a-radio
              v-for="option in field.options"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 复选框组 -->
        <a-form-item
          v-else-if="field.type === 'checkbox'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-checkbox-group
            v-model:value="formModel[field.key]"
            :disabled="readonly || field.readonly"
          >
            <a-checkbox
              v-for="option in field.options"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <!-- 日期选择器 -->
        <a-form-item
          v-else-if="field.type === 'date'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-date-picker
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :format="field.format || 'YYYY-MM-DD'"
            :show-time="field.showTime"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 日期范围选择器 -->
        <a-form-item
          v-else-if="field.type === 'dateRange'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-range-picker
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :disabled="readonly || field.readonly"
            :format="field.format || 'YYYY-MM-DD'"
            :show-time="field.showTime"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 开关 -->
        <a-form-item
          v-else-if="field.type === 'switch'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-switch
            v-model:checked="formModel[field.key]"
            :disabled="readonly || field.readonly"
            :checked-children="field.checkedText"
            :un-checked-children="field.uncheckedText"
          />
        </a-form-item>

        <!-- 文件上传 -->
        <a-form-item
          v-else-if="field.type === 'upload'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <a-upload
            v-model:file-list="formModel[field.key]"
            :disabled="readonly || field.readonly"
            :multiple="field.multiple"
            :accept="field.accept"
            :before-upload="() => false"
            :max-count="field.maxCount"
          >
            <a-button :disabled="readonly || field.readonly">
              <UploadOutlined />
              {{ field.uploadText || '选择文件' }}
            </a-button>
          </a-upload>
        </a-form-item>

        <!-- 用户选择器 -->
        <a-form-item
          v-else-if="field.type === 'user'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <UserSelector
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :multiple="field.multiple"
            :disabled="readonly || field.readonly"
          />
        </a-form-item>

        <!-- 部门选择器 -->
        <a-form-item
          v-else-if="field.type === 'department'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <DepartmentSelector
            v-model:value="formModel[field.key]"
            :placeholder="field.placeholder"
            :multiple="field.multiple"
            :disabled="readonly || field.readonly"
          />
        </a-form-item>

        <!-- 自定义组件 -->
        <a-form-item
          v-else-if="field.type === 'custom'"
          :label="field.label"
          :name="field.key"
          :rules="getFieldRules(field)"
        >
          <component
            :is="field.component"
            v-model:value="formModel[field.key]"
            v-bind="field.props"
            :disabled="readonly || field.readonly"
          />
        </a-form-item>

        <!-- 分组标题 -->
        <a-divider
          v-else-if="field.type === 'divider'"
          :orientation="field.orientation || 'left'"
        >
          {{ field.label }}
        </a-divider>

        <!-- 静态文本 -->
        <div
          v-else-if="field.type === 'text'"
          class="form-text"
        >
          <label class="form-text-label">{{ field.label }}：</label>
          <span class="form-text-value">{{ field.value || formModel[field.key] }}</span>
        </div>
      </template>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import UserSelector from './UserSelector.vue'
import DepartmentSelector from './DepartmentSelector.vue'

// Props
interface Props {
  fields: any[]
  model?: Record<string, any>
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  model: () => ({}),
  readonly: false
})

// Emits
const emit = defineEmits<{
  'update:model': [model: Record<string, any>]
  'change': [key: string, value: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const formModel = reactive<Record<string, any>>({})
const formRules = reactive<Record<string, any>>({})

// 监听model变化
watch(
  () => props.model,
  (newModel) => {
    Object.assign(formModel, newModel)
  },
  { immediate: true, deep: true }
)

// 监听formModel变化
watch(
  formModel,
  (newModel) => {
    emit('update:model', { ...newModel })
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  initializeForm()
})

// 方法
const initializeForm = () => {
  // 初始化表单字段默认值
  props.fields.forEach(field => {
    if (field.key && formModel[field.key] === undefined) {
      formModel[field.key] = getDefaultValue(field)
    }
  })
}

const getDefaultValue = (field: any) => {
  switch (field.type) {
    case 'checkbox':
      return []
    case 'switch':
      return false
    case 'upload':
      return []
    case 'number':
      return field.defaultValue || 0
    default:
      return field.defaultValue || ''
  }
}

const getFieldRules = (field: any) => {
  const rules: any[] = []
  
  // 必填验证
  if (field.required) {
    rules.push({
      required: true,
      message: field.requiredMessage || `请输入${field.label}`
    })
  }
  
  // 长度验证
  if (field.minLength || field.maxLength) {
    rules.push({
      min: field.minLength,
      max: field.maxLength,
      message: `${field.label}长度应在${field.minLength || 0}-${field.maxLength || '∞'}之间`
    })
  }
  
  // 数值范围验证
  if (field.type === 'number' && (field.min !== undefined || field.max !== undefined)) {
    rules.push({
      type: 'number',
      min: field.min,
      max: field.max,
      message: `${field.label}应在${field.min || '-∞'}-${field.max || '∞'}之间`
    })
  }
  
  // 正则验证
  if (field.pattern) {
    rules.push({
      pattern: new RegExp(field.pattern),
      message: field.patternMessage || `${field.label}格式不正确`
    })
  }
  
  // 自定义验证
  if (field.validator) {
    rules.push({
      validator: field.validator
    })
  }
  
  return rules
}

const validate = async () => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

const clearValidate = () => {
  formRef.value?.clearValidate()
}

const resetFields = () => {
  formRef.value?.resetFields()
}

// 暴露方法
defineExpose({
  validate,
  clearValidate,
  resetFields,
  formRef
})
</script>

<style lang="less" scoped>
.dynamic-form {
  .form-text {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;

    .form-text-label {
      font-weight: 500;
      color: #666;
      margin-right: 8px;
      min-width: 80px;
    }

    .form-text-value {
      color: #333;
      flex: 1;
    }
  }
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;

  label {
    font-weight: 500;
    color: #333;
  }
}
</style>
