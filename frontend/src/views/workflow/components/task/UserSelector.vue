<template>
  <div class="user-selector">
    <a-select
      v-model:value="selectedValue"
      :mode="multiple ? 'multiple' : undefined"
      :placeholder="placeholder"
      :loading="loading"
      :filter-option="false"
      show-search
      allow-clear
      @search="handleSearch"
      @change="handleChange"
      @clear="handleClear"
    >
      <a-select-option
        v-for="user in userOptions"
        :key="user.id"
        :value="user.id"
        :label="user.name"
      >
        <div class="user-option">
          <a-avatar :size="24" :src="user.avatar">
            {{ user.name?.charAt(0) }}
          </a-avatar>
          <div class="user-info">
            <div class="user-name">{{ user.name }}</div>
            <div class="user-dept">{{ user.departmentName }}</div>
          </div>
        </div>
      </a-select-option>
    </a-select>

    <!-- 已选用户展示 -->
    <div v-if="selectedUsers.length > 0" class="selected-users">
      <a-tag
        v-for="user in selectedUsers"
        :key="user.id"
        closable
        @close="removeUser(user.id)"
      >
        <a-avatar :size="16" :src="user.avatar">
          {{ user.name?.charAt(0) }}
        </a-avatar>
        <span style="margin-left: 4px">{{ user.name }}</span>
      </a-tag>
    </div>

    <!-- 快速选择 -->
    <div v-if="showQuickSelect" class="quick-select">
      <a-divider orientation="left" plain>快速选择</a-divider>
      <a-space wrap>
        <a-button
          v-for="group in quickSelectGroups"
          :key="group.key"
          size="small"
          @click="selectGroup(group)"
        >
          {{ group.name }}
        </a-button>
      </a-space>
    </div>

    <!-- 组织架构树选择 -->
    <a-modal
      v-model:visible="treeModalVisible"
      title="选择用户"
      :width="800"
      @ok="confirmTreeSelection"
    >
      <div class="tree-selector">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="tree-panel">
              <h4>组织架构</h4>
              <a-tree
                v-model:selectedKeys="selectedDeptKeys"
                :tree-data="deptTreeData"
                :field-names="{ title: 'name', key: 'id', children: 'children' }"
                @select="handleDeptSelect"
              />
            </div>
          </a-col>
          <a-col :span="12">
            <div class="user-panel">
              <h4>部门用户</h4>
              <a-checkbox-group v-model:value="tempSelectedUserIds" style="width: 100%">
                <div
                  v-for="user in deptUsers"
                  :key="user.id"
                  class="user-item"
                >
                  <a-checkbox :value="user.id">
                    <div class="user-option">
                      <a-avatar :size="24" :src="user.avatar">
                        {{ user.name?.charAt(0) }}
                      </a-avatar>
                      <div class="user-info">
                        <div class="user-name">{{ user.name }}</div>
                        <div class="user-position">{{ user.positionName }}</div>
                      </div>
                    </div>
                  </a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 高级选择按钮 -->
    <div v-if="showAdvanced" class="advanced-actions">
      <a-button size="small" @click="openTreeSelector">
        <TeamOutlined />
        组织架构选择
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { TeamOutlined } from '@ant-design/icons-vue'
import { userApi, departmentApi } from '../../api/system'
import { debounce } from 'lodash-es'

// Props
interface Props {
  value?: number | number[]
  userName?: string
  multiple?: boolean
  placeholder?: string
  showQuickSelect?: boolean
  showAdvanced?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: '请选择用户',
  showQuickSelect: false,
  showAdvanced: true
})

// Emits
const emit = defineEmits<{
  'update:value': [value: number | number[] | null]
  'update:userName': [userName: string]
  'change': [value: number | number[] | null, users: any[]]
}>()

// 响应式数据
const loading = ref(false)
const userOptions = ref<any[]>([])
const selectedUsers = ref<any[]>([])
const treeModalVisible = ref(false)
const selectedDeptKeys = ref<number[]>([])
const deptTreeData = ref<any[]>([])
const deptUsers = ref<any[]>([])
const tempSelectedUserIds = ref<number[]>([])

// 计算属性
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 快速选择组
const quickSelectGroups = ref([
  { key: 'dept_leader', name: '部门负责人' },
  { key: 'hr', name: 'HR' },
  { key: 'finance', name: '财务' },
  { key: 'admin', name: '行政' }
])

// 监听value变化，更新选中用户信息
watch(
  () => props.value,
  async (newValue) => {
    if (newValue) {
      await loadSelectedUsers(newValue)
    } else {
      selectedUsers.value = []
    }
  },
  { immediate: true }
)

// 方法
const handleSearch = debounce(async (searchText: string) => {
  if (!searchText || searchText.length < 2) {
    userOptions.value = []
    return
  }

  loading.value = true
  try {
    const response = await userApi.searchUsers({
      keyword: searchText,
      page: 0,
      size: 20
    })
    
    if (response.code === 200) {
      userOptions.value = response.data.content || []
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    message.error('搜索用户失败')
  } finally {
    loading.value = false
  }
}, 300)

const handleChange = (value: number | number[]) => {
  emit('update:value', value)
  
  // 更新用户名
  if (value && !props.multiple) {
    const user = userOptions.value.find(u => u.id === value)
    if (user) {
      emit('update:userName', user.name)
    }
  }
  
  emit('change', value, selectedUsers.value)
}

const handleClear = () => {
  emit('update:value', null)
  emit('update:userName', '')
  selectedUsers.value = []
  emit('change', null, [])
}

const removeUser = (userId: number) => {
  if (props.multiple && Array.isArray(props.value)) {
    const newValue = props.value.filter(id => id !== userId)
    emit('update:value', newValue)
  }
}

const loadSelectedUsers = async (userIds: number | number[]) => {
  const ids = Array.isArray(userIds) ? userIds : [userIds]
  if (ids.length === 0) {
    selectedUsers.value = []
    return
  }

  try {
    const response = await userApi.getUsersByIds(ids)
    if (response.code === 200) {
      selectedUsers.value = response.data || []
      
      // 更新用户名（单选模式）
      if (!props.multiple && selectedUsers.value.length > 0) {
        emit('update:userName', selectedUsers.value[0].name)
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

const selectGroup = async (group: any) => {
  loading.value = true
  try {
    const response = await userApi.getUsersByGroup(group.key)
    if (response.code === 200) {
      const users = response.data || []
      if (users.length === 0) {
        message.warning(`${group.name}组没有用户`)
        return
      }

      if (props.multiple) {
        const currentIds = Array.isArray(props.value) ? props.value : []
        const newIds = [...new Set([...currentIds, ...users.map(u => u.id)])]
        emit('update:value', newIds)
      } else {
        if (users.length === 1) {
          emit('update:value', users[0].id)
          emit('update:userName', users[0].name)
        } else {
          // 多个用户时显示选择框
          userOptions.value = users
        }
      }
    }
  } catch (error) {
    console.error('加载组用户失败:', error)
    message.error('加载组用户失败')
  } finally {
    loading.value = false
  }
}

const openTreeSelector = async () => {
  treeModalVisible.value = true
  await loadDeptTree()
  
  // 初始化已选用户
  if (props.value) {
    tempSelectedUserIds.value = Array.isArray(props.value) ? [...props.value] : [props.value]
  } else {
    tempSelectedUserIds.value = []
  }
}

const loadDeptTree = async () => {
  try {
    const response = await departmentApi.getDeptTree()
    if (response.code === 200) {
      deptTreeData.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
    message.error('加载部门树失败')
  }
}

const handleDeptSelect = async (selectedKeys: number[]) => {
  if (selectedKeys.length === 0) {
    deptUsers.value = []
    return
  }

  const deptId = selectedKeys[0]
  try {
    const response = await userApi.getUsersByDept(deptId)
    if (response.code === 200) {
      deptUsers.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门用户失败:', error)
    message.error('加载部门用户失败')
  }
}

const confirmTreeSelection = async () => {
  if (tempSelectedUserIds.value.length === 0) {
    message.warning('请选择用户')
    return
  }

  emit('update:value', props.multiple ? tempSelectedUserIds.value : tempSelectedUserIds.value[0])
  await loadSelectedUsers(tempSelectedUserIds.value)
  treeModalVisible.value = false
}
</script>

<style lang="less" scoped>
.user-selector {
  .user-option {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-info {
      flex: 1;
      min-width: 0;

      .user-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .user-dept,
      .user-position {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
      }
    }
  }

  .selected-users {
    margin-top: 8px;

    .ant-tag {
      margin-bottom: 4px;
      display: inline-flex;
      align-items: center;
    }
  }

  .quick-select {
    margin-top: 12px;
  }

  .advanced-actions {
    margin-top: 8px;
  }
}

.tree-selector {
  .tree-panel,
  .user-panel {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .user-item {
    width: 100%;
    margin-bottom: 8px;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background: #f5f5f5;
    }

    .ant-checkbox {
      width: 100%;
    }
  }
}

:deep(.ant-select-dropdown) {
  .ant-select-item-option-content {
    padding: 0;
  }
}
</style>
