<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    :width="800"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="task-approval-modal">
      <!-- 任务信息 -->
      <div class="task-info">
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="任务名称">
            {{ taskInfo.taskName }}
          </a-descriptions-item>
          <a-descriptions-item label="流程名称">
            {{ taskInfo.processName }}
          </a-descriptions-item>
          <a-descriptions-item label="发起人">
            {{ taskInfo.initiatorName }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(taskInfo.createdTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="任务描述" :span="2">
            {{ taskInfo.description || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 操作表单 -->
      <div class="action-form">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
        >
          <!-- 操作类型选择 -->
          <a-form-item label="操作类型" name="action">
            <a-radio-group v-model:value="formData.action" @change="handleActionChange">
              <a-radio value="approve">
                <CheckCircleOutlined style="color: #52c41a" />
                同意
              </a-radio>
              <a-radio value="reject">
                <CloseCircleOutlined style="color: #ff4d4f" />
                驳回
              </a-radio>
              <a-radio value="transfer">
                <SwapOutlined style="color: #1890ff" />
                转办
              </a-radio>
              <a-radio value="delegate">
                <UserSwitchOutlined style="color: #722ed1" />
                委派
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 审批意见 -->
          <a-form-item 
            :label="commentLabel" 
            name="comment"
            :rules="commentRules"
          >
            <a-textarea
              v-model:value="formData.comment"
              :placeholder="commentPlaceholder"
              :rows="4"
              show-count
              :maxlength="500"
            />
          </a-form-item>

          <!-- 驳回目标节点选择 -->
          <a-form-item 
            v-if="formData.action === 'reject'"
            label="驳回到"
            name="targetNodeKey"
          >
            <a-select
              v-model:value="formData.targetNodeKey"
              placeholder="请选择驳回目标节点"
              allow-clear
            >
              <a-select-option value="">发起人</a-select-option>
              <a-select-option value="previous">上一节点</a-select-option>
              <a-select-option 
                v-for="node in rejectableNodes"
                :key="node.key"
                :value="node.key"
              >
                {{ node.name }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- 转办/委派目标用户选择 -->
          <a-form-item 
            v-if="formData.action === 'transfer' || formData.action === 'delegate'"
            :label="formData.action === 'transfer' ? '转办给' : '委派给'"
            name="targetUserId"
          >
            <UserSelector
              v-model:value="formData.targetUserId"
              v-model:user-name="formData.targetUserName"
              placeholder="请选择目标用户"
              :multiple="false"
            />
          </a-form-item>

          <!-- 表单字段 -->
          <div v-if="formFields && formFields.length > 0" class="form-fields">
            <a-divider>表单信息</a-divider>
            <DynamicForm
              ref="dynamicFormRef"
              :fields="formFields"
              :model="formData.formData"
              :readonly="formData.action === 'reject'"
            />
          </div>

          <!-- 附件上传 -->
          <a-form-item label="附件">
            <a-upload
              v-model:file-list="fileList"
              :before-upload="beforeUpload"
              :remove="handleRemoveFile"
              multiple
            >
              <a-button>
                <UploadOutlined />
                上传附件
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form>
      </div>

      <!-- 任务历史 -->
      <div class="task-history">
        <a-divider>审批历史</a-divider>
        <TaskHistoryTimeline :task-id="taskInfo.id" />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SwapOutlined,
  UserSwitchOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import type { FormInstance, UploadFile } from 'ant-design-vue'
import { taskApi } from '../../api/workflow'
import { formatDateTime } from '../../utils/date-utils'
import UserSelector from './UserSelector.vue'
import DynamicForm from './DynamicForm.vue'
import TaskHistoryTimeline from './TaskHistoryTimeline.vue'

// Props
interface Props {
  visible: boolean
  taskInfo: any
  formFields?: any[]
  rejectableNodes?: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'success': [result: any]
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const dynamicFormRef = ref()
const fileList = ref<UploadFile[]>([])

// 表单数据
const formData = reactive({
  action: 'approve',
  comment: '',
  targetNodeKey: '',
  targetUserId: null,
  targetUserName: '',
  formData: {}
})

// 计算属性
const modalTitle = computed(() => {
  const actionMap = {
    approve: '审批任务',
    reject: '驳回任务',
    transfer: '转办任务',
    delegate: '委派任务'
  }
  return actionMap[formData.action] || '处理任务'
})

const commentLabel = computed(() => {
  const labelMap = {
    approve: '审批意见',
    reject: '驳回原因',
    transfer: '转办原因',
    delegate: '委派原因'
  }
  return labelMap[formData.action] || '意见'
})

const commentPlaceholder = computed(() => {
  const placeholderMap = {
    approve: '请输入审批意见（可选）',
    reject: '请输入驳回原因',
    transfer: '请输入转办原因',
    delegate: '请输入委派原因'
  }
  return placeholderMap[formData.action] || '请输入意见'
})

const commentRules = computed(() => {
  if (formData.action === 'approve') {
    return [] // 同意时意见可选
  }
  return [{ required: true, message: `请输入${commentLabel.value}` }]
})

// 表单验证规则
const formRules = {
  action: [{ required: true, message: '请选择操作类型' }],
  targetUserId: [
    {
      validator: (rule: any, value: any) => {
        if ((formData.action === 'transfer' || formData.action === 'delegate') && !value) {
          return Promise.reject('请选择目标用户')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      resetForm()
    }
  }
)

// 方法
const resetForm = () => {
  Object.assign(formData, {
    action: 'approve',
    comment: '',
    targetNodeKey: '',
    targetUserId: null,
    targetUserName: '',
    formData: {}
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

const handleActionChange = () => {
  // 清除相关字段
  formData.comment = ''
  formData.targetNodeKey = ''
  formData.targetUserId = null
  formData.targetUserName = ''
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    // 验证动态表单
    if (dynamicFormRef.value && formData.action === 'approve') {
      const dynamicFormValid = await dynamicFormRef.value.validate()
      if (!dynamicFormValid) {
        return
      }
    }

    loading.value = true

    // 构建请求数据
    const requestData = {
      userId: getCurrentUserId(), // 需要从用户上下文获取
      userName: getCurrentUserName(),
      comment: formData.comment,
      formData: formData.formData,
      variables: {},
      targetUserId: formData.targetUserId,
      targetNodeKey: formData.targetNodeKey,
      reason: formData.comment
    }

    let result
    switch (formData.action) {
      case 'approve':
        result = await taskApi.approveTask(props.taskInfo.id, requestData)
        break
      case 'reject':
        result = await taskApi.rejectTask(props.taskInfo.id, requestData)
        break
      case 'transfer':
        result = await taskApi.transferTask(props.taskInfo.id, requestData)
        break
      case 'delegate':
        result = await taskApi.delegateTask(props.taskInfo.id, requestData)
        break
    }

    if (result.code === 200) {
      message.success(result.message || '操作成功')
      emit('success', result.data)
      emit('update:visible', false)
    } else {
      message.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('任务处理失败:', error)
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

const beforeUpload = (file: UploadFile) => {
  // 文件上传前的处理
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword'].includes(file.type!)
  if (!isValidType) {
    message.error('只能上传图片、PDF或Word文档')
    return false
  }
  
  const isLt10M = file.size! / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  return false // 阻止自动上传
}

const handleRemoveFile = (file: UploadFile) => {
  const index = fileList.value.indexOf(file)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 获取当前用户信息（需要根据实际情况实现）
const getCurrentUserId = () => {
  // TODO: 从用户上下文或store中获取当前用户ID
  return 1
}

const getCurrentUserName = () => {
  // TODO: 从用户上下文或store中获取当前用户姓名
  return '当前用户'
}
</script>

<style lang="less" scoped>
.task-approval-modal {
  .task-info {
    margin-bottom: 24px;
  }

  .action-form {
    margin-bottom: 24px;

    .form-fields {
      margin-top: 16px;
    }
  }

  .task-history {
    max-height: 300px;
    overflow-y: auto;
  }
}

:deep(.ant-radio-group) {
  .ant-radio-wrapper {
    margin-right: 24px;
    margin-bottom: 8px;
  }
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}
</style>
