<template>
  <div class="task-history-timeline">
    <a-spin :spinning="loading">
      <a-timeline v-if="historyList.length > 0">
        <a-timeline-item
          v-for="(item, index) in historyList"
          :key="item.id"
          :color="getTimelineColor(item.action, item.result)"
        >
          <template #dot>
            <component :is="getActionIcon(item.action, item.result)" />
          </template>
          
          <div class="timeline-content">
            <!-- 操作信息 -->
            <div class="action-header">
              <span class="action-title">{{ getActionTitle(item.action, item.result) }}</span>
              <span class="action-time">{{ formatDateTime(item.actionTime) }}</span>
            </div>
            
            <!-- 操作人信息 -->
            <div class="operator-info">
              <a-avatar :size="24" :src="item.operatorAvatar">
                {{ item.operatorName?.charAt(0) }}
              </a-avatar>
              <span class="operator-name">{{ item.operatorName }}</span>
              <span v-if="item.duration" class="duration">
                耗时: {{ formatDuration(item.duration) }}
              </span>
            </div>
            
            <!-- 操作内容 -->
            <div v-if="item.comment" class="action-content">
              <div class="comment-label">{{ getCommentLabel(item.action) }}：</div>
              <div class="comment-text">{{ item.comment }}</div>
            </div>
            
            <!-- 目标信息 -->
            <div v-if="item.targetUserName || item.targetNodeKey" class="target-info">
              <a-tag v-if="item.targetUserName" size="small">
                {{ getTargetLabel(item.action) }}: {{ item.targetUserName }}
              </a-tag>
              <a-tag v-if="item.targetNodeKey" size="small" color="blue">
                目标节点: {{ getNodeName(item.targetNodeKey) }}
              </a-tag>
            </div>
            
            <!-- 表单数据 -->
            <div v-if="item.formData && showFormData" class="form-data">
              <a-collapse size="small">
                <a-collapse-panel key="formData" header="表单数据">
                  <pre class="form-data-content">{{ formatFormData(item.formData) }}</pre>
                </a-collapse-panel>
              </a-collapse>
            </div>
            
            <!-- 附件信息 -->
            <div v-if="item.attachments && item.attachments.length > 0" class="attachments">
              <div class="attachment-label">附件：</div>
              <div class="attachment-list">
                <a-tag
                  v-for="file in item.attachments"
                  :key="file.id"
                  color="cyan"
                  style="cursor: pointer"
                  @click="downloadAttachment(file)"
                >
                  <PaperClipOutlined />
                  {{ file.name }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      
      <a-empty v-else description="暂无历史记录" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SwapOutlined,
  UserSwitchOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  HandOutlined,
  StopOutlined,
  PaperClipOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { taskApi } from '../../api/workflow'
import { formatDateTime } from '../../utils/date-utils'

// Props
interface Props {
  taskId: number
  showFormData?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  showFormData: false,
  autoRefresh: false,
  refreshInterval: 30000
})

// 响应式数据
const loading = ref(false)
const historyList = ref<any[]>([])
let refreshTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  loadHistory()
  
  if (props.autoRefresh) {
    startAutoRefresh()
  }
})

// 方法
const loadHistory = async () => {
  if (!props.taskId) return
  
  loading.value = true
  try {
    const response = await taskApi.getTaskHistory(props.taskId)
    if (response.code === 200) {
      historyList.value = response.data || []
    } else {
      message.error('加载历史记录失败')
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
    message.error('加载历史记录失败')
  } finally {
    loading.value = false
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    loadHistory()
  }, props.refreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const getTimelineColor = (action: string, result: string) => {
  const colorMap: Record<string, string> = {
    'APPROVE': '#52c41a',
    'REJECT': '#ff4d4f',
    'TRANSFER': '#1890ff',
    'DELEGATE': '#722ed1',
    'ADD_SIGN': '#13c2c2',
    'REMOVE_SIGN': '#fa8c16',
    'CLAIM': '#2f54eb',
    'CANCEL': '#8c8c8c',
    'TERMINATE': '#f5222d'
  }
  
  return colorMap[action] || '#d9d9d9'
}

const getActionIcon = (action: string, result: string) => {
  const iconMap: Record<string, any> = {
    'APPROVE': CheckCircleOutlined,
    'REJECT': CloseCircleOutlined,
    'TRANSFER': SwapOutlined,
    'DELEGATE': UserSwitchOutlined,
    'ADD_SIGN': UserAddOutlined,
    'REMOVE_SIGN': UserDeleteOutlined,
    'CLAIM': HandOutlined,
    'CANCEL': StopOutlined,
    'TERMINATE': StopOutlined
  }
  
  return iconMap[action] || ClockCircleOutlined
}

const getActionTitle = (action: string, result: string) => {
  const titleMap: Record<string, string> = {
    'APPROVE': '审批通过',
    'REJECT': '驳回',
    'TRANSFER': '转办',
    'DELEGATE': '委派',
    'ADD_SIGN': '加签',
    'REMOVE_SIGN': '减签',
    'CLAIM': '签收',
    'UNCLAIM': '取消签收',
    'CANCEL': '撤销',
    'TERMINATE': '终止'
  }
  
  return titleMap[action] || action
}

const getCommentLabel = (action: string) => {
  const labelMap: Record<string, string> = {
    'APPROVE': '审批意见',
    'REJECT': '驳回原因',
    'TRANSFER': '转办原因',
    'DELEGATE': '委派原因',
    'ADD_SIGN': '加签原因',
    'REMOVE_SIGN': '减签原因'
  }
  
  return labelMap[action] || '备注'
}

const getTargetLabel = (action: string) => {
  const labelMap: Record<string, string> = {
    'TRANSFER': '转办给',
    'DELEGATE': '委派给',
    'ADD_SIGN': '加签用户',
    'REMOVE_SIGN': '减签用户'
  }
  
  return labelMap[action] || '目标用户'
}

const getNodeName = (nodeKey: string) => {
  // TODO: 根据nodeKey获取节点名称
  const nodeMap: Record<string, string> = {
    'start': '开始',
    'end': '结束',
    'previous': '上一节点'
  }
  
  return nodeMap[nodeKey] || nodeKey
}

const formatDuration = (duration: number) => {
  if (!duration) return ''
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${seconds}秒`
  }
}

const formatFormData = (formData: string) => {
  try {
    const data = JSON.parse(formData)
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return formData
  }
}

const downloadAttachment = (file: any) => {
  // TODO: 实现附件下载
  message.info('下载功能待实现')
}

// 暴露方法供父组件调用
defineExpose({
  refresh: loadHistory,
  startAutoRefresh,
  stopAutoRefresh
})
</script>

<style lang="less" scoped>
.task-history-timeline {
  .timeline-content {
    .action-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .action-title {
        font-weight: 500;
        color: #333;
        font-size: 14px;
      }

      .action-time {
        color: #999;
        font-size: 12px;
      }
    }

    .operator-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .operator-name {
        font-size: 13px;
        color: #666;
      }

      .duration {
        font-size: 12px;
        color: #999;
        margin-left: auto;
      }
    }

    .action-content {
      margin-bottom: 8px;
      padding: 8px 12px;
      background: #fafafa;
      border-radius: 4px;

      .comment-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .comment-text {
        font-size: 13px;
        color: #333;
        line-height: 1.5;
        white-space: pre-wrap;
      }
    }

    .target-info {
      margin-bottom: 8px;

      .ant-tag {
        margin-right: 8px;
      }
    }

    .form-data {
      margin-bottom: 8px;

      .form-data-content {
        font-size: 12px;
        color: #666;
        background: #f8f8f8;
        border: none;
        margin: 0;
        max-height: 200px;
        overflow-y: auto;
      }
    }

    .attachments {
      .attachment-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .attachment-list {
        .ant-tag {
          margin-bottom: 4px;
          margin-right: 8px;
        }
      }
    }
  }
}

:deep(.ant-timeline-item-content) {
  margin-left: 8px;
}

:deep(.ant-timeline-item-tail) {
  border-left-width: 2px;
}
</style>
