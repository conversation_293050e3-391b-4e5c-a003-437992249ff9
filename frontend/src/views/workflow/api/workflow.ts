/**
 * 工作流API服务
 * <AUTHOR>
 * @since 1.0.0
 */

import { request } from '@/utils/request'
import type {
  ProcessDefinition,
  ProcessInstance,
  TaskInstance,
  ProcessStartData,
  TaskAction,
  QueryParams,
  PageResult,
  ApiResponse,
  ProcessStatistics,
  TaskStatistics
} from '../types/workflow'

// API基础路径
const API_BASE = '/api/v1/workflow'

/**
 * 流程定义API
 */
export const processDefinitionApi = {
  /**
   * 创建流程定义
   */
  create(data: ProcessDefinition): Promise<ApiResponse<ProcessDefinition>> {
    return request.post(`${API_BASE}/process-definitions`, data)
  },

  /**
   * 更新流程定义
   */
  update(id: number, data: ProcessDefinition): Promise<ApiResponse<ProcessDefinition>> {
    return request.put(`${API_BASE}/process-definitions/${id}`, data)
  },

  /**
   * 发布流程定义
   */
  deploy(id: number): Promise<ApiResponse<ProcessDefinition>> {
    return request.post(`${API_BASE}/process-definitions/${id}/deploy`)
  },

  /**
   * 挂起流程定义
   */
  suspend(id: number): Promise<ApiResponse<ProcessDefinition>> {
    return request.post(`${API_BASE}/process-definitions/${id}/suspend`)
  },

  /**
   * 激活流程定义
   */
  activate(id: number): Promise<ApiResponse<ProcessDefinition>> {
    return request.post(`${API_BASE}/process-definitions/${id}/activate`)
  },

  /**
   * 删除流程定义
   */
  delete(id: number): Promise<ApiResponse<boolean>> {
    return request.delete(`${API_BASE}/process-definitions/${id}`)
  },

  /**
   * 根据ID查询流程定义
   */
  getById(id: number): Promise<ApiResponse<ProcessDefinition>> {
    return request.get(`${API_BASE}/process-definitions/${id}`)
  },

  /**
   * 根据流程标识查询最新版本
   */
  getLatestByKey(processKey: string): Promise<ApiResponse<ProcessDefinition>> {
    return request.get(`${API_BASE}/process-definitions/key/${processKey}`)
  },

  /**
   * 查询激活状态的流程定义
   */
  getActiveList(): Promise<ApiResponse<ProcessDefinition[]>> {
    return request.get(`${API_BASE}/process-definitions/active`)
  },

  /**
   * 分页查询流程定义
   */
  getList(params: QueryParams): Promise<ApiResponse<PageResult<ProcessDefinition>>> {
    return request.get(`${API_BASE}/process-definitions`, { params })
  },

  /**
   * 获取流程定义统计信息
   */
  getStatistics(): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/process-definitions/statistics`)
  }
}

/**
 * 流程实例API
 */
export const processInstanceApi = {
  /**
   * 启动流程实例
   */
  start(data: ProcessStartData): Promise<ApiResponse<ProcessInstance>> {
    return request.post(`${API_BASE}/process-instances`, data)
  },

  /**
   * 终止流程实例
   */
  terminate(id: number, reason?: string): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/process-instances/${id}/terminate`, { reason })
  },

  /**
   * 撤销流程实例
   */
  cancel(id: number, reason?: string): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/process-instances/${id}/cancel`, { reason })
  },

  /**
   * 挂起流程实例
   */
  suspend(id: number): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/process-instances/${id}/suspend`)
  },

  /**
   * 激活流程实例
   */
  activate(id: number): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/process-instances/${id}/activate`)
  },

  /**
   * 根据ID查询流程实例
   */
  getById(id: number): Promise<ApiResponse<ProcessInstance>> {
    return request.get(`${API_BASE}/process-instances/${id}`)
  },

  /**
   * 分页查询流程实例
   */
  getList(params: QueryParams): Promise<ApiResponse<PageResult<ProcessInstance>>> {
    return request.get(`${API_BASE}/process-instances`, { params })
  },

  /**
   * 获取我发起的流程实例
   */
  getMyInitiated(params: QueryParams): Promise<ApiResponse<PageResult<ProcessInstance>>> {
    return request.get(`${API_BASE}/process-instances/my-initiated`, { params })
  },

  /**
   * 获取我参与的流程实例
   */
  getMyParticipated(params: QueryParams): Promise<ApiResponse<PageResult<ProcessInstance>>> {
    return request.get(`${API_BASE}/process-instances/my-participated`, { params })
  },

  /**
   * 获取流程实例历史
   */
  getHistory(id: number): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/process-instances/${id}/history`)
  },

  /**
   * 获取流程实例变量
   */
  getVariables(id: number): Promise<ApiResponse<Record<string, any>>> {
    return request.get(`${API_BASE}/process-instances/${id}/variables`)
  },

  /**
   * 设置流程实例变量
   */
  setVariables(id: number, variables: Record<string, any>): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/process-instances/${id}/variables`, variables)
  }
}

/**
 * 任务API
 */
export const taskApi = {
  /**
   * 获取待办任务列表
   */
  getTodoList(params: QueryParams): Promise<ApiResponse<PageResult<TaskInstance>>> {
    return request.get(`${API_BASE}/tasks/todo`, { params })
  },

  /**
   * 获取已办任务列表
   */
  getDoneList(params: QueryParams): Promise<ApiResponse<PageResult<TaskInstance>>> {
    return request.get(`${API_BASE}/tasks/done`, { params })
  },

  /**
   * 获取抄送任务列表
   */
  getCopyList(params: QueryParams): Promise<ApiResponse<PageResult<any>>> {
    return request.get(`${API_BASE}/tasks/copy`, { params })
  },

  /**
   * 根据ID查询任务
   */
  getById(id: number): Promise<ApiResponse<TaskInstance>> {
    return request.get(`${API_BASE}/tasks/${id}`)
  },

  /**
   * 签收任务
   */
  claim(id: number): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/claim`)
  },

  /**
   * 完成任务
   */
  complete(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/complete`, data)
  },

  /**
   * 驳回任务
   */
  reject(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/reject`, data)
  },

  /**
   * 转办任务
   */
  transfer(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/transfer`, data)
  },

  /**
   * 委派任务
   */
  delegate(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/delegate`, data)
  },

  /**
   * 加签
   */
  addSign(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/add-sign`, data)
  },

  /**
   * 减签
   */
  removeSign(id: number, data: TaskAction): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/tasks/${id}/remove-sign`, data)
  },

  /**
   * 获取任务表单
   */
  getForm(id: number): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/tasks/${id}/form`)
  },

  /**
   * 获取任务历史
   */
  getHistory(id: number): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/tasks/${id}/history`)
  }
}

/**
 * 流程模板API
 */
export const templateApi = {
  /**
   * 获取流程模板列表
   */
  getList(params: QueryParams): Promise<ApiResponse<PageResult<any>>> {
    return request.get(`${API_BASE}/templates`, { params })
  },

  /**
   * 根据ID查询流程模板
   */
  getById(id: number): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/templates/${id}`)
  },

  /**
   * 创建流程模板
   */
  create(data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/templates`, data)
  },

  /**
   * 更新流程模板
   */
  update(id: number, data: any): Promise<ApiResponse<any>> {
    return request.put(`${API_BASE}/templates/${id}`, data)
  },

  /**
   * 删除流程模板
   */
  delete(id: number): Promise<ApiResponse<boolean>> {
    return request.delete(`${API_BASE}/templates/${id}`)
  },

  /**
   * 基于模板创建流程
   */
  createProcess(id: number, data: any): Promise<ApiResponse<ProcessDefinition>> {
    return request.post(`${API_BASE}/templates/${id}/create-process`, data)
  }
}

/**
 * 统计API
 */
export const statisticsApi = {
  /**
   * 获取流程统计概览
   */
  getOverview(): Promise<ApiResponse<ProcessStatistics>> {
    return request.get(`${API_BASE}/statistics/overview`)
  },

  /**
   * 获取任务统计
   */
  getTaskStatistics(): Promise<ApiResponse<TaskStatistics>> {
    return request.get(`${API_BASE}/statistics/tasks`)
  },

  /**
   * 获取流程性能分析
   */
  getPerformance(params: {
    processKey?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/statistics/performance`, { params })
  },

  /**
   * 获取用户工作量统计
   */
  getUserWorkload(params: {
    userId?: number
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/statistics/user-workload`, { params })
  }
}

/**
 * 通知API
 */
export const notificationApi = {
  /**
   * 获取抄送消息列表
   */
  getCopyList(params: QueryParams): Promise<ApiResponse<PageResult<any>>> {
    return request.get(`${API_BASE}/notifications/copies`, { params })
  },

  /**
   * 标记抄送消息已读
   */
  markCopyRead(id: number): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/notifications/copies/${id}/read`)
  },

  /**
   * 批量标记抄送消息已读
   */
  batchMarkCopyRead(ids: number[]): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/notifications/copies/batch-read`, { ids })
  },

  /**
   * 获取流程提醒设置
   */
  getReminderConfig(processKey: string): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/notifications/reminders/${processKey}`)
  },

  /**
   * 更新流程提醒设置
   */
  updateReminderConfig(processKey: string, config: any): Promise<ApiResponse<boolean>> {
    return request.put(`${API_BASE}/notifications/reminders/${processKey}`, config)
  }
}

/**
 * 搜索API
 */
export const searchApi = {
  /**
   * 全文搜索流程
   */
  search(params: {
    q: string
    type?: 'PROCESS' | 'INSTANCE' | 'TASK'
    page?: number
    size?: number
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.get(`${API_BASE}/search`, { params })
  },

  /**
   * 高级查询
   */
  advancedQuery(data: {
    conditions: Array<{
      field: string
      operator: string
      value: any
    }>
    orderBy?: Array<{
      field: string
      direction: 'ASC' | 'DESC'
    }>
    page?: number
    size?: number
  }): Promise<ApiResponse<PageResult<any>>> {
    return request.post(`${API_BASE}/query/advanced`, data)
  }
}

/**
 * 表单权限API
 */
export const formPermissionApi = {
  /**
   * 获取表单权限配置
   */
  getConfig(processKey: string, nodeKey: string): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/form-permissions`, {
      params: { processKey, nodeKey }
    })
  },

  /**
   * 更新表单权限配置
   */
  updateConfig(data: {
    processKey: string
    nodeKey: string
    permissions: Array<{
      fieldName: string
      permission: 'HIDDEN' | 'READ_ONLY' | 'EDITABLE'
    }>
  }): Promise<ApiResponse<boolean>> {
    return request.post(`${API_BASE}/form-permissions`, data)
  }
}

/**
 * 导出导入API
 */
export const importExportApi = {
  /**
   * 导出流程定义
   */
  exportProcess(id: number, format: 'JSON' | 'BPMN' = 'JSON'): Promise<Blob> {
    return request.get(`${API_BASE}/process-definitions/${id}/export`, {
      params: { format },
      responseType: 'blob'
    })
  },

  /**
   * 导入流程定义
   */
  importProcess(file: File): Promise<ApiResponse<ProcessDefinition>> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post(`${API_BASE}/process-definitions/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 导入BPMN XML文件
   */
  importBpmn(file: File, processName?: string, category?: string): Promise<ApiResponse<ProcessDefinition>> {
    const formData = new FormData()
    formData.append('file', file)
    if (processName) formData.append('processName', processName)
    if (category) formData.append('category', category)
    return request.post(`${API_BASE}/process-definitions/import/bpmn`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 导出BPMN XML
   */
  exportBpmn(id: number): Promise<Blob> {
    return request.get(`${API_BASE}/process-definitions/${id}/export/bpmn`, {
      responseType: 'blob'
    })
  },

  /**
   * 导出流程定义JSON
   */
  exportJson(id: number): Promise<Blob> {
    return request.get(`${API_BASE}/process-definitions/${id}/export/json`, {
      responseType: 'blob'
    })
  },

  /**
   * 获取流程定义统计信息
   */
  getStatistics(): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/process-definitions/statistics`)
  }
}

/**
 * 任务管理API
 */
export const taskApi = {
  /**
   * 创建任务
   */
  create(data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks`, data)
  },

  /**
   * 根据ID获取任务
   */
  getById(id: number): Promise<ApiResponse<any>> {
    return request.get(`${API_BASE}/tasks/${id}`)
  },

  /**
   * 更新任务
   */
  update(id: number, data: any): Promise<ApiResponse<any>> {
    return request.put(`${API_BASE}/tasks/${id}`, data)
  },

  /**
   * 删除任务
   */
  delete(id: number): Promise<ApiResponse<boolean>> {
    return request.delete(`${API_BASE}/tasks/${id}`)
  },

  /**
   * 分页查询任务
   */
  getTasks(params: any): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/tasks`, { params })
  },

  /**
   * 获取待办任务
   */
  getTodoTasks(userId: number, tenantId?: string): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/tasks/todo`, {
      params: { userId, tenantId }
    })
  },

  /**
   * 获取已办任务
   */
  getDoneTasks(userId: number, tenantId?: string): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/tasks/done`, {
      params: { userId, tenantId }
    })
  },

  /**
   * 签收任务
   */
  claimTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/claim`, null, {
      params: data
    })
  },

  /**
   * 审批任务
   */
  approveTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/approve`, data)
  },

  /**
   * 驳回任务
   */
  rejectTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/reject`, data)
  },

  /**
   * 转办任务
   */
  transferTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/transfer`, data)
  },

  /**
   * 委派任务
   */
  delegateTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/delegate`, data)
  },

  /**
   * 加签
   */
  addSign(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/add-sign`, data)
  },

  /**
   * 减签
   */
  removeSign(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/remove-sign`, data)
  },

  /**
   * 撤销任务
   */
  cancelTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/cancel`, data)
  },

  /**
   * 终止任务
   */
  terminateTask(taskId: number, data: any): Promise<ApiResponse<any>> {
    return request.post(`${API_BASE}/tasks/${taskId}/terminate`, data)
  },

  /**
   * 获取任务历史
   */
  getTaskHistory(taskId: number): Promise<ApiResponse<any[]>> {
    return request.get(`${API_BASE}/tasks/${taskId}/history`)
  },

  /**
   * 批量导出流程实例
   */
  exportInstances(params: {
    processKey?: string
    status?: string
    startDate?: string
    endDate?: string
    format?: 'EXCEL' | 'CSV'
  }): Promise<Blob> {
    return request.get(`${API_BASE}/process-instances/export`, {
      params,
      responseType: 'blob'
    })
  }
}
