# 工作流引擎前端组件架构设计

## 📋 组件结构概览

```
src/views/workflow/
├── components/                    # 工作流组件库
│   ├── designers/                # 设计器组件
│   │   ├── SimpleDesigner/       # SIMPLE设计器
│   │   │   ├── index.vue         # 主组件
│   │   │   ├── components/       # 子组件
│   │   │   │   ├── Canvas.vue    # 画布组件
│   │   │   │   ├── Toolbar.vue   # 工具栏
│   │   │   │   ├── NodePanel.vue # 节点面板
│   │   │   │   ├── PropertyPanel.vue # 属性面板
│   │   │   │   └── nodes/        # 节点组件
│   │   │   │       ├── StartNode.vue
│   │   │   │       ├── UserTaskNode.vue
│   │   │   │       ├── ServiceTaskNode.vue
│   │   │   │       ├── GatewayNode.vue
│   │   │   │       └── EndNode.vue
│   │   │   ├── hooks/            # 组合式函数
│   │   │   │   ├── useCanvas.ts  # 画布逻辑
│   │   │   │   ├── useDragDrop.ts # 拖拽逻辑
│   │   │   │   └── useNodeEditor.ts # 节点编辑
│   │   │   └── types/            # 类型定义
│   │   │       └── designer.ts
│   │   └── BpmnDesigner/         # BPMN设计器
│   │       ├── index.vue
│   │       ├── components/
│   │       │   ├── BpmnCanvas.vue
│   │       │   ├── BpmnToolbar.vue
│   │       │   └── BpmnProperties.vue
│   │       └── hooks/
│   │           └── useBpmnEditor.ts
│   ├── forms/                    # 表单组件
│   │   ├── FormDesigner/         # 表单设计器
│   │   ├── FormRenderer/         # 表单渲染器
│   │   └── FormValidator/        # 表单验证器
│   ├── tasks/                    # 任务组件
│   │   ├── TaskList.vue          # 任务列表
│   │   ├── TaskDetail.vue        # 任务详情
│   │   ├── TaskApproval.vue      # 任务审批
│   │   └── TaskHistory.vue       # 任务历史
│   ├── process/                  # 流程组件
│   │   ├── ProcessList.vue       # 流程列表
│   │   ├── ProcessDetail.vue     # 流程详情
│   │   ├── ProcessMonitor.vue    # 流程监控
│   │   └── ProcessStatistics.vue # 流程统计
│   └── common/                   # 通用组件
│       ├── WorkflowModal.vue     # 工作流弹窗
│       ├── UserSelector.vue      # 用户选择器
│       ├── RoleSelector.vue      # 角色选择器
│       └── TimelineView.vue      # 时间线视图
├── pages/                        # 页面组件
│   ├── designer/                 # 设计器页面
│   │   ├── SimpleDesignerPage.vue
│   │   └── BpmnDesignerPage.vue
│   ├── process/                  # 流程管理页面
│   │   ├── ProcessDefinitionPage.vue
│   │   ├── ProcessInstancePage.vue
│   │   └── ProcessTemplatePage.vue
│   ├── task/                     # 任务管理页面
│   │   ├── TodoTaskPage.vue
│   │   ├── DoneTaskPage.vue
│   │   └── TaskStatisticsPage.vue
│   └── monitor/                  # 监控页面
│       ├── ProcessMonitorPage.vue
│       └── SystemStatisticsPage.vue
├── stores/                       # 状态管理
│   ├── workflow.ts               # 工作流状态
│   ├── designer.ts               # 设计器状态
│   ├── task.ts                   # 任务状态
│   └── process.ts                # 流程状态
├── api/                          # API接口
│   ├── workflow.ts               # 工作流API
│   ├── process.ts                # 流程API
│   ├── task.ts                   # 任务API
│   └── template.ts               # 模板API
├── types/                        # 类型定义
│   ├── workflow.ts               # 工作流类型
│   ├── process.ts                # 流程类型
│   ├── task.ts                   # 任务类型
│   └── form.ts                   # 表单类型
├── utils/                        # 工具函数
│   ├── workflow-engine.ts        # 工作流引擎工具
│   ├── form-builder.ts           # 表单构建工具
│   ├── node-validator.ts         # 节点验证工具
│   └── export-import.ts          # 导入导出工具
└── styles/                       # 样式文件
    ├── workflow.less             # 工作流样式
    ├── designer.less             # 设计器样式
    └── components.less           # 组件样式
```

## 🎨 SIMPLE设计器组件设计

### 核心特性
- **拖拽式操作**: 支持从工具栏拖拽节点到画布
- **可视化连线**: 支持节点间的可视化连接
- **属性配置**: 支持节点属性的实时配置
- **表单集成**: 支持表单字段的拖拽设计
- **实时预览**: 支持流程的实时预览和验证

### 节点类型定义
```typescript
// types/designer.ts
export interface NodeType {
  id: string
  type: 'START' | 'USER_TASK' | 'SERVICE_TASK' | 'GATEWAY' | 'END'
  name: string
  icon: string
  description: string
  properties: NodeProperty[]
}

export interface NodeProperty {
  key: string
  label: string
  type: 'input' | 'select' | 'textarea' | 'user-selector' | 'role-selector'
  required: boolean
  options?: Array<{ label: string; value: any }>
  defaultValue?: any
}

export interface FlowNode {
  id: string
  type: string
  name: string
  position: { x: number; y: number }
  size: { width: number; height: number }
  properties: Record<string, any>
  style?: Record<string, any>
}

export interface FlowEdge {
  id: string
  source: string
  target: string
  label?: string
  condition?: string
  style?: Record<string, any>
}

export interface FlowDefinition {
  nodes: FlowNode[]
  edges: FlowEdge[]
  variables: Record<string, any>
  settings: FlowSettings
}
```

### 设计器主组件结构
```vue
<!-- components/designers/SimpleDesigner/index.vue -->
<template>
  <div class="simple-designer">
    <!-- 工具栏 -->
    <Toolbar 
      :node-types="nodeTypes"
      @add-node="handleAddNode"
      @save="handleSave"
      @preview="handlePreview"
    />
    
    <!-- 主要内容区 -->
    <div class="designer-content">
      <!-- 节点面板 -->
      <NodePanel 
        :node-types="nodeTypes"
        @drag-start="handleDragStart"
      />
      
      <!-- 画布区域 -->
      <Canvas 
        ref="canvasRef"
        :nodes="flowDefinition.nodes"
        :edges="flowDefinition.edges"
        @node-select="handleNodeSelect"
        @edge-select="handleEdgeSelect"
        @canvas-drop="handleCanvasDrop"
        @node-move="handleNodeMove"
        @edge-create="handleEdgeCreate"
      />
      
      <!-- 属性面板 -->
      <PropertyPanel 
        :selected-element="selectedElement"
        @property-change="handlePropertyChange"
      />
    </div>
  </div>
</template>
```

## 🔧 BPMN设计器组件设计

### 技术选型
- **核心库**: bpmn-js (官方BPMN 2.0实现)
- **扩展插件**: 
  - bpmn-js-properties-panel (属性面板)
  - bpmn-js-color-picker (颜色选择)
  - diagram-js-minimap (小地图)

### 组件结构
```vue
<!-- components/designers/BpmnDesigner/index.vue -->
<template>
  <div class="bpmn-designer">
    <BpmnToolbar 
      @save="handleSave"
      @export="handleExport"
      @import="handleImport"
      @validate="handleValidate"
    />
    
    <div class="bpmn-content">
      <BpmnCanvas 
        ref="bpmnCanvasRef"
        :xml="bpmnXml"
        @element-select="handleElementSelect"
        @element-change="handleElementChange"
      />
      
      <BpmnProperties 
        :selected-element="selectedElement"
        @property-update="handlePropertyUpdate"
      />
    </div>
  </div>
</template>
```

## 📝 表单设计器组件

### 表单字段类型
```typescript
export interface FormField {
  id: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'upload'
  name: string
  label: string
  placeholder?: string
  required: boolean
  validation?: ValidationRule[]
  options?: Array<{ label: string; value: any }>
  defaultValue?: any
  permissions?: FieldPermission[]
}

export interface FieldPermission {
  nodeKey: string
  permission: 'HIDDEN' | 'READ_ONLY' | 'EDITABLE'
}
```

## 🎯 任务处理组件

### 任务审批组件
```vue
<!-- components/tasks/TaskApproval.vue -->
<template>
  <div class="task-approval">
    <!-- 任务信息 -->
    <TaskInfo :task="currentTask" />
    
    <!-- 表单渲染 -->
    <FormRenderer 
      :form-definition="formDefinition"
      :form-data="formData"
      :permissions="fieldPermissions"
      @form-change="handleFormChange"
    />
    
    <!-- 审批操作 -->
    <ApprovalActions 
      :task="currentTask"
      :available-actions="availableActions"
      @approve="handleApprove"
      @reject="handleReject"
      @transfer="handleTransfer"
      @delegate="handleDelegate"
    />
    
    <!-- 审批历史 -->
    <TaskHistory :process-instance-id="currentTask.processInstanceId" />
  </div>
</template>
```

## 📊 流程监控组件

### 流程实例监控
```vue
<!-- components/process/ProcessMonitor.vue -->
<template>
  <div class="process-monitor">
    <!-- 流程图显示 -->
    <ProcessDiagram 
      :process-definition="processDefinition"
      :current-tasks="currentTasks"
      :completed-tasks="completedTasks"
    />
    
    <!-- 实例信息 -->
    <ProcessInstanceInfo :instance="processInstance" />
    
    <!-- 任务列表 -->
    <TaskTimeline :tasks="allTasks" />
    
    <!-- 变量信息 -->
    <VariableViewer :variables="processVariables" />
  </div>
</template>
```

## 🔄 状态管理设计

### 工作流状态管理
```typescript
// stores/workflow.ts
export const useWorkflowStore = defineStore('workflow', () => {
  // 状态
  const processDefinitions = ref<ProcessDefinition[]>([])
  const processInstances = ref<ProcessInstance[]>([])
  const currentProcess = ref<ProcessInstance | null>(null)
  
  // 操作
  const fetchProcessDefinitions = async (params: QueryParams) => {
    // 获取流程定义列表
  }
  
  const startProcess = async (startData: ProcessStartData) => {
    // 启动流程实例
  }
  
  const completeTask = async (taskId: string, data: TaskCompleteData) => {
    // 完成任务
  }
  
  return {
    processDefinitions,
    processInstances,
    currentProcess,
    fetchProcessDefinitions,
    startProcess,
    completeTask
  }
})
```

## 🎨 样式设计规范

### 设计器样式
```less
// styles/designer.less
.simple-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .designer-content {
    flex: 1;
    display: flex;
    
    .node-panel {
      width: 240px;
      border-right: 1px solid #e8e8e8;
      background: #fafafa;
    }
    
    .canvas-container {
      flex: 1;
      position: relative;
      background: #fff;
      
      .flow-canvas {
        width: 100%;
        height: 100%;
      }
    }
    
    .property-panel {
      width: 320px;
      border-left: 1px solid #e8e8e8;
      background: #fafafa;
    }
  }
}
```

## 🔌 插件扩展机制

### 节点插件接口
```typescript
export interface NodePlugin {
  type: string
  name: string
  icon: string
  component: Component
  properties: NodeProperty[]
  validator?: (node: FlowNode) => ValidationResult
  executor?: (context: ExecutionContext) => Promise<ExecutionResult>
}

// 注册节点插件
export const registerNodePlugin = (plugin: NodePlugin) => {
  nodePluginRegistry.set(plugin.type, plugin)
}
```

这个技术架构设计为您的工作流引擎提供了：

1. **完整的双设计器架构** - SIMPLE和BPMN两种设计器
2. **模块化的组件设计** - 高度可复用和可扩展
3. **完善的数据库设计** - 支持复杂的工作流场景
4. **标准化的API接口** - RESTful风格，易于集成
5. **现代化的前端架构** - 基于Vue 3和TypeScript
6. **灵活的扩展机制** - 支持自定义节点和插件

接下来我将开始实现具体的功能模块。您希望我先从哪个模块开始开发？

<function_calls>
<invoke name="update_tasks">
<parameter name="tasks">[{"task_id": "2cf384c1-568f-401b-8523-fe2fab8211e0", "state": "COMPLETE"}, {"task_id": "8b956915-e13d-49d7-adec-a07b544ed044", "state": "IN_PROGRESS"}]
