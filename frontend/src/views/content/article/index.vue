<!--
  文章管理页面
  @description 文章发布、分类管理、SEO设置等功能
-->
<template>
  <div class="article-management">
    <a-card :bordered="false">
      <!-- 文章统计卡片 -->
      <div class="article-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="总文章数"
                :value="articleStats.totalCount"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <FileTextOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="已发布"
                :value="articleStats.publishedCount"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="草稿箱"
                :value="articleStats.draftCount"
                :value-style="{ color: '#fa8c16' }"
              >
                <template #prefix>
                  <EditOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="stat-card">
              <a-statistic
                title="总浏览量"
                :value="articleStats.totalViews"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <EyeOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="文章标题" name="title">
            <a-input
              v-model:value="searchForm.title"
              placeholder="请输入文章标题"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="文章分类" name="categoryId">
            <a-tree-select
              v-model:value="searchForm.categoryId"
              :tree-data="categoryTree"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              placeholder="请选择文章分类"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="作者" name="author">
            <a-input
              v-model:value="searchForm.author"
              placeholder="请输入作者"
              allow-clear
              style="width: 150px"
            />
          </a-form-item>
          
          <a-form-item label="状态" name="status">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="0">草稿</a-select-option>
              <a-select-option :value="1">已发布</a-select-option>
              <a-select-option :value="2">已下线</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="发布时间" name="publishTime">
            <a-range-picker
              v-model:value="searchForm.publishTime"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <PlusOutlined />
            写文章
          </a-button>
          <a-button @click="handleManageCategory">
            <FolderOutlined />
            分类管理
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchPublish"
          >
            <SendOutlined />
            批量发布
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchOffline"
          >
            <StopOutlined />
            批量下线
          </a-button>
          <a-button @click="handleExport">
            <ExportOutlined />
            导出
          </a-button>
        </a-space>
      </div>

      <!-- 文章表格 -->
      <a-table
        ref="tableRef"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1500 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 文章信息 -->
        <template #articleInfo="{ record }">
          <div class="article-info">
            <div class="article-cover">
              <a-image
                :width="60"
                :height="40"
                :src="record.coverImage"
                :preview="false"
                fallback="/images/no-image.png"
              />
            </div>
            <div class="article-details">
              <div class="article-title">{{ record.title }}</div>
              <div class="article-summary">{{ record.summary || '暂无摘要' }}</div>
              <div class="article-tags">
                <a-tag
                  v-for="tag in (record.tags || '').split(',')"
                  :key="tag"
                  size="small"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </div>
          </div>
        </template>

        <!-- 分类 -->
        <template #category="{ record }">
          <a-tag v-if="record.categoryName" color="blue">
            {{ record.categoryName }}
          </a-tag>
          <span v-else>-</span>
        </template>

        <!-- 统计信息 -->
        <template #statistics="{ record }">
          <div class="article-statistics">
            <div class="stat-item">
              <EyeOutlined />
              <span>{{ record.viewCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <LikeOutlined />
              <span>{{ record.likeCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <MessageOutlined />
              <span>{{ record.commentCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <ShareAltOutlined />
              <span>{{ record.shareCount || 0 }}</span>
            </div>
          </div>
        </template>

        <!-- 特殊标记 -->
        <template #marks="{ record }">
          <div class="article-marks">
            <a-tag v-if="record.isTop" color="red" size="small">置顶</a-tag>
            <a-tag v-if="record.isHot" color="orange" size="small">热门</a-tag>
            <a-tag v-if="record.isRecommend" color="green" size="small">推荐</a-tag>
          </div>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-if="record.status === 0"
                    @click="handlePublish(record)"
                  >
                    <SendOutlined />
                    发布
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status === 1"
                    @click="handleOffline(record)"
                  >
                    <StopOutlined />
                    下线
                  </a-menu-item>
                  <a-menu-item @click="handleCopy(record)">
                    <CopyOutlined />
                    复制
                  </a-menu-item>
                  <a-menu-item @click="handleSeoSetting(record)">
                    <SettingOutlined />
                    SEO设置
                  </a-menu-item>
                  <a-menu-item @click="handleToggleTop(record)">
                    <PushpinOutlined />
                    {{ record.isTop ? '取消置顶' : '设为置顶' }}
                  </a-menu-item>
                  <a-menu-item @click="handleToggleRecommend(record)">
                    <StarOutlined />
                    {{ record.isRecommend ? '取消推荐' : '设为推荐' }}
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item danger @click="handleDelete(record)">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 文章详情抽屉 -->
    <ArticleDetailDrawer
      v-model:open="detailDrawerVisible"
      :article-id="currentArticleId"
    />

    <!-- 分类管理抽屉 -->
    <CategoryManageDrawer
      v-model:open="categoryDrawerVisible"
      @success="handleCategorySuccess"
    />

    <!-- SEO设置弹窗 -->
    <ArticleSeoModal
      v-model:open="seoModalVisible"
      :article-id="currentArticleId"
      @success="handleSeoSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  FolderOutlined,
  SendOutlined,
  StopOutlined,
  ExportOutlined,
  DownOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  EditOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined,
  ShareAltOutlined,
  CopyOutlined,
  SettingOutlined,
  PushpinOutlined,
  StarOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'

// 模拟组件导入
const ArticleDetailDrawer = defineAsyncComponent(() => import('./components/ArticleDetailDrawer.vue'))
const CategoryManageDrawer = defineAsyncComponent(() => import('./components/CategoryManageDrawer.vue'))
const ArticleSeoModal = defineAsyncComponent(() => import('./components/ArticleSeoModal.vue'))

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const dataSource = ref<any[]>([])
const selectedRowKeys = ref<string[]>([])
const categoryTree = ref<any[]>([])
const articleStats = ref<any>({})
const detailDrawerVisible = ref(false)
const categoryDrawerVisible = ref(false)
const seoModalVisible = ref(false)
const currentArticleId = ref<number | null>(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  title: '',
  categoryId: undefined,
  author: '',
  status: undefined,
  publishTime: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '文章信息',
    key: 'articleInfo',
    width: 350,
    fixed: 'left',
    slots: { customRender: 'articleInfo' }
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
    slots: { customRender: 'category' }
  },
  {
    title: '作者',
    dataIndex: 'author',
    width: 100
  },
  {
    title: '统计',
    key: 'statistics',
    width: 150,
    slots: { customRender: 'statistics' }
  },
  {
    title: '标记',
    key: 'marks',
    width: 120,
    slots: { customRender: 'marks' }
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    width: 180
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

/**
 * 获取状态颜色
 */
const getStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'orange',   // 草稿
    1: 'green',    // 已发布
    2: 'red'       // 已下线
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    0: '草稿',
    1: '已发布',
    2: '已下线'
  }
  return textMap[status] || '未知'
}

// 模拟数据加载函数
const loadData = async () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    dataSource.value = [
      {
        id: 1,
        title: '电商系统开发最佳实践',
        summary: '本文介绍了电商系统开发的最佳实践和注意事项...',
        coverImage: '',
        author: '技术团队',
        categoryId: 4,
        categoryName: '技术分享',
        tags: 'Vue3,TypeScript,电商',
        viewCount: 1250,
        likeCount: 89,
        commentCount: 23,
        shareCount: 45,
        isTop: true,
        isHot: false,
        isRecommend: true,
        status: 1,
        publishTime: '2024-01-01 10:00:00'
      }
    ]
    pagination.total = 1
    loading.value = false
  }, 1000)
}

const loadCategoryTree = async () => {
  categoryTree.value = [
    {
      id: 1,
      name: '公司新闻',
      children: []
    },
    {
      id: 2,
      name: '行业资讯',
      children: []
    },
    {
      id: 3,
      name: '产品介绍',
      children: []
    },
    {
      id: 4,
      name: '技术分享',
      children: []
    },
    {
      id: 5,
      name: '帮助中心',
      children: []
    }
  ]
}

const loadArticleStats = async () => {
  articleStats.value = {
    totalCount: 156,
    publishedCount: 120,
    draftCount: 36,
    totalViews: 125680
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  loadData()
}

const handleAdd = () => {
  router.push('/content/article/edit')
}

const handleView = (record: any) => {
  currentArticleId.value = record.id
  detailDrawerVisible.value = true
}

const handleEdit = (record: any) => {
  router.push(`/content/article/edit/${record.id}`)
}

const handlePublish = (record: any) => {
  Modal.confirm({
    title: '确认发布',
    content: `确定要发布文章"${record.title}"吗？`,
    onOk: () => {
      message.success('发布成功')
      loadData()
    }
  })
}

const handleOffline = (record: any) => {
  Modal.confirm({
    title: '确认下线',
    content: `确定要下线文章"${record.title}"吗？`,
    onOk: () => {
      message.success('下线成功')
      loadData()
    }
  })
}

const handleCopy = (record: any) => {
  message.success('复制成功')
}

const handleSeoSetting = (record: any) => {
  currentArticleId.value = record.id
  seoModalVisible.value = true
}

const handleSeoSuccess = () => {
  loadData()
}

const handleToggleTop = (record: any) => {
  const action = record.isTop ? '取消置顶' : '设为置顶'
  message.success(`${action}成功`)
  loadData()
}

const handleToggleRecommend = (record: any) => {
  const action = record.isRecommend ? '取消推荐' : '设为推荐'
  message.success(`${action}成功`)
  loadData()
}

const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除文章"${record.title}"吗？`,
    onOk: () => {
      message.success('删除成功')
      loadData()
    }
  })
}

const handleManageCategory = () => {
  categoryDrawerVisible.value = true
}

const handleCategorySuccess = () => {
  loadCategoryTree()
}

const handleBatchPublish = () => {
  message.info('批量发布功能开发中...')
}

const handleBatchOffline = () => {
  message.info('批量下线功能开发中...')
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

// 页面加载时初始化数据
onMounted(() => {
  loadData()
  loadCategoryTree()
  loadArticleStats()
})
</script>

<style scoped lang="less">
.article-management {
  .article-stats {
    margin-bottom: 24px;
    
    .stat-card {
      text-align: center;
    }
  }
  
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .action-buttons {
    margin-bottom: 16px;
  }
  
  .article-info {
    display: flex;
    align-items: flex-start;
    
    .article-cover {
      margin-right: 12px;
      flex-shrink: 0;
      
      .ant-image {
        border-radius: 4px;
        overflow: hidden;
      }
    }
    
    .article-details {
      flex: 1;
      min-width: 0;
      
      .article-title {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .article-summary {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .article-tags {
        .ant-tag {
          margin-right: 4px;
          margin-bottom: 2px;
        }
      }
    }
  }
  
  .article-statistics {
    .stat-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 12px;
      color: #8c8c8c;
      
      .anticon {
        margin-right: 4px;
      }
    }
  }
  
  .article-marks {
    .ant-tag {
      margin-bottom: 2px;
    }
  }
}
</style>
