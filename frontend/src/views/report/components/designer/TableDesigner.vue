<!--
  表格设计器
  @description 可视化表格报表设计器，支持列配置、样式设置、条件格式等
-->
<template>
  <div class="table-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <a-space>
        <a-button size="small" @click="addColumn">
          <PlusOutlined />
          添加列
        </a-button>
        <a-button size="small" @click="addRow">
          <TableOutlined />
          添加行
        </a-button>
        <a-divider type="vertical" />
        <a-button size="small" @click="toggleBorders">
          <BorderOutlined />
          边框
        </a-button>
        <a-button size="small" @click="toggleStripes">
          <UnorderedListOutlined />
          斑马纹
        </a-button>
        <a-divider type="vertical" />
        <a-button size="small" @click="autoFitColumns">
          <ColumnWidthOutlined />
          自适应列宽
        </a-button>
        <a-button size="small" @click="resetTable">
          <ReloadOutlined />
          重置
        </a-button>
      </a-space>
    </div>

    <!-- 表格设计区域 -->
    <div class="table-design-area">
      <div class="table-container" :style="tableContainerStyle">
        <!-- 表格头部 -->
        <div v-if="config.showHeader" class="table-header">
          <div
            v-for="(column, index) in columns"
            :key="column.id"
            class="table-header-cell"
            :class="{ 'selected': selectedColumn === index }"
            :style="getColumnStyle(column, index)"
            @click="selectColumn(index)"
            @contextmenu="showColumnMenu($event, index)"
          >
            <div class="cell-content">
              <span class="column-title">{{ column.title }}</span>
              <div class="column-actions">
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    <MoreOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleColumnAction($event, index)">
                      <a-menu-item key="edit">
                        <EditOutlined />
                        编辑列
                      </a-menu-item>
                      <a-menu-item key="sort">
                        <SortAscendingOutlined />
                        排序设置
                      </a-menu-item>
                      <a-menu-item key="filter">
                        <FilterOutlined />
                        筛选设置
                      </a-menu-item>
                      <a-menu-item key="format">
                        <BgColorsOutlined />
                        条件格式
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="insert-left">
                        <PlusOutlined />
                        左侧插入列
                      </a-menu-item>
                      <a-menu-item key="insert-right">
                        <PlusOutlined />
                        右侧插入列
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" class="danger-item">
                        <DeleteOutlined />
                        删除列
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <!-- 列宽调整手柄 -->
            <div
              class="resize-handle"
              @mousedown="startResize($event, index)"
            ></div>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="table-body">
          <div
            v-for="(row, rowIndex) in previewData"
            :key="rowIndex"
            class="table-row"
            :class="{ 
              'striped': config.striped && rowIndex % 2 === 1,
              'selected': selectedRow === rowIndex 
            }"
            @click="selectRow(rowIndex)"
          >
            <div
              v-for="(column, colIndex) in columns"
              :key="column.id"
              class="table-cell"
              :class="{ 'selected': selectedCell?.row === rowIndex && selectedCell?.col === colIndex }"
              :style="getCellStyle(column, row, rowIndex, colIndex)"
              @click="selectCell(rowIndex, colIndex)"
              @dblclick="editCell(rowIndex, colIndex)"
            >
              <div class="cell-content">
                {{ formatCellValue(row[column.dataIndex], column) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!previewData || previewData.length === 0" class="table-empty">
          <a-empty description="暂无数据" />
        </div>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="table-properties">
      <a-tabs v-model:activeKey="activePropertyTab" size="small">
        <!-- 表格属性 -->
        <a-tab-pane key="table" tab="表格">
          <div class="property-section">
            <h4>基本设置</h4>
            <a-form layout="vertical" size="small">
              <a-form-item label="表格标题">
                <a-input v-model:value="config.title" placeholder="请输入表格标题" />
              </a-form-item>
              <a-form-item label="显示设置">
                <a-checkbox v-model:checked="config.showHeader">显示表头</a-checkbox>
                <a-checkbox v-model:checked="config.bordered">显示边框</a-checkbox>
                <a-checkbox v-model:checked="config.striped">斑马纹</a-checkbox>
                <a-checkbox v-model:checked="config.hover">悬停效果</a-checkbox>
              </a-form-item>
              <a-form-item label="表格大小">
                <a-select v-model:value="config.size">
                  <a-select-option value="small">紧凑</a-select-option>
                  <a-select-option value="middle">中等</a-select-option>
                  <a-select-option value="large">宽松</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>

        <!-- 列属性 -->
        <a-tab-pane key="column" tab="列设置">
          <div v-if="selectedColumn !== null" class="property-section">
            <h4>列属性</h4>
            <a-form layout="vertical" size="small">
              <a-form-item label="列标题">
                <a-input v-model:value="currentColumn.title" />
              </a-form-item>
              <a-form-item label="数据字段">
                <a-input v-model:value="currentColumn.dataIndex" />
              </a-form-item>
              <a-form-item label="列宽">
                <a-input-number
                  v-model:value="currentColumn.width"
                  :min="50"
                  :max="500"
                  addon-after="px"
                />
              </a-form-item>
              <a-form-item label="对齐方式">
                <a-select v-model:value="currentColumn.align">
                  <a-select-option value="left">左对齐</a-select-option>
                  <a-select-option value="center">居中</a-select-option>
                  <a-select-option value="right">右对齐</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="数据类型">
                <a-select v-model:value="currentColumn.dataType">
                  <a-select-option value="text">文本</a-select-option>
                  <a-select-option value="number">数字</a-select-option>
                  <a-select-option value="date">日期</a-select-option>
                  <a-select-option value="currency">货币</a-select-option>
                  <a-select-option value="percent">百分比</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="功能设置">
                <a-checkbox v-model:checked="currentColumn.sortable">可排序</a-checkbox>
                <a-checkbox v-model:checked="currentColumn.filterable">可筛选</a-checkbox>
                <a-checkbox v-model:checked="currentColumn.resizable">可调整大小</a-checkbox>
                <a-checkbox v-model:checked="currentColumn.fixed">固定列</a-checkbox>
              </a-form-item>
            </a-form>
          </div>
          <div v-else class="property-placeholder">
            <a-empty description="请选择一列进行配置" />
          </div>
        </a-tab-pane>

        <!-- 样式设置 -->
        <a-tab-pane key="style" tab="样式">
          <div class="property-section">
            <h4>表格样式</h4>
            <a-form layout="vertical" size="small">
              <a-form-item label="表头背景色">
                <a-input v-model:value="config.headerStyle.backgroundColor" type="color" />
              </a-form-item>
              <a-form-item label="表头文字颜色">
                <a-input v-model:value="config.headerStyle.color" type="color" />
              </a-form-item>
              <a-form-item label="行背景色">
                <a-input v-model:value="config.bodyStyle.backgroundColor" type="color" />
              </a-form-item>
              <a-form-item label="边框颜色">
                <a-input v-model:value="config.borderColor" type="color" />
              </a-form-item>
              <a-form-item label="字体大小">
                <a-input-number
                  v-model:value="config.fontSize"
                  :min="10"
                  :max="24"
                  addon-after="px"
                />
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 列编辑弹窗 -->
    <ColumnEditModal
      v-model:visible="columnEditVisible"
      :column="editingColumn"
      @save="handleColumnSave"
    />

    <!-- 条件格式弹窗 -->
    <ConditionalFormatModal
      v-model:visible="formatModalVisible"
      :column="editingColumn"
      @save="handleFormatSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  TableOutlined,
  BorderOutlined,
  UnorderedListOutlined,
  ColumnWidthOutlined,
  ReloadOutlined,
  MoreOutlined,
  EditOutlined,
  SortAscendingOutlined,
  FilterOutlined,
  BgColorsOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import ColumnEditModal from './ColumnEditModal.vue'
import ConditionalFormatModal from './ConditionalFormatModal.vue'
import type { ReportConfig } from '../../types/report'
import { generateId } from '../../utils/report-utils'

// 组件属性
interface Props {
  config: ReportConfig
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// 组件事件
const emit = defineEmits<{
  update: [config: ReportConfig]
}>()

// 响应式数据
const selectedColumn = ref<number | null>(null)
const selectedRow = ref<number | null>(null)
const selectedCell = ref<{ row: number; col: number } | null>(null)
const activePropertyTab = ref('table')
const columnEditVisible = ref(false)
const formatModalVisible = ref(false)
const editingColumn = ref<any>(null)

// 表格配置
const config = reactive({
  title: props.config.name || '数据表格',
  showHeader: true,
  bordered: true,
  striped: false,
  hover: true,
  size: 'middle',
  headerStyle: {
    backgroundColor: '#fafafa',
    color: '#262626'
  },
  bodyStyle: {
    backgroundColor: '#ffffff'
  },
  borderColor: '#f0f0f0',
  fontSize: 14
})

// 列配置
const columns = ref([
  {
    id: generateId('col_'),
    title: '列1',
    dataIndex: 'col1',
    width: 120,
    align: 'left',
    dataType: 'text',
    sortable: false,
    filterable: false,
    resizable: true,
    fixed: false
  }
])

// 计算属性
const currentColumn = computed(() => {
  if (selectedColumn.value !== null) {
    return columns.value[selectedColumn.value]
  }
  return null
})

const previewData = computed(() => {
  if (props.data && props.data.length > 0) {
    return props.data.slice(0, 10) // 只显示前10行用于预览
  }
  
  // 生成示例数据
  return Array.from({ length: 5 }, (_, index) => {
    const row: any = {}
    columns.value.forEach((col, colIndex) => {
      row[col.dataIndex] = `示例数据 ${index + 1}-${colIndex + 1}`
    })
    return row
  })
})

const tableContainerStyle = computed(() => ({
  fontSize: `${config.fontSize}px`,
  borderColor: config.borderColor
}))

// 工具方法
const getColumnStyle = (column: any, index: number) => ({
  width: `${column.width}px`,
  textAlign: column.align,
  backgroundColor: config.headerStyle.backgroundColor,
  color: config.headerStyle.color,
  borderColor: config.borderColor
})

const getCellStyle = (column: any, row: any, rowIndex: number, colIndex: number) => {
  const style: any = {
    width: `${column.width}px`,
    textAlign: column.align,
    borderColor: config.borderColor
  }
  
  // 应用条件格式
  if (column.conditionalFormat) {
    const value = row[column.dataIndex]
    // 这里可以添加条件格式逻辑
  }
  
  return style
}

const formatCellValue = (value: any, column: any) => {
  if (value == null) return ''
  
  switch (column.dataType) {
    case 'number':
      return typeof value === 'number' ? value.toLocaleString() : value
    case 'currency':
      return typeof value === 'number' ? `¥${value.toLocaleString()}` : value
    case 'percent':
      return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : value
    case 'date':
      return value instanceof Date ? value.toLocaleDateString() : value
    default:
      return String(value)
  }
}

// 事件处理
const addColumn = () => {
  const newColumn = {
    id: generateId('col_'),
    title: `列${columns.value.length + 1}`,
    dataIndex: `col${columns.value.length + 1}`,
    width: 120,
    align: 'left',
    dataType: 'text',
    sortable: false,
    filterable: false,
    resizable: true,
    fixed: false
  }
  columns.value.push(newColumn)
  message.success('添加列成功')
}

const addRow = () => {
  // 这里可以添加行的逻辑
  message.info('添加行功能开发中')
}

const toggleBorders = () => {
  config.bordered = !config.bordered
}

const toggleStripes = () => {
  config.striped = !config.striped
}

const autoFitColumns = () => {
  // 自动调整列宽逻辑
  message.success('列宽已自动调整')
}

const resetTable = () => {
  // 重置表格配置
  Object.assign(config, {
    showHeader: true,
    bordered: true,
    striped: false,
    hover: true,
    size: 'middle'
  })
  message.success('表格已重置')
}

const selectColumn = (index: number) => {
  selectedColumn.value = index
  selectedRow.value = null
  selectedCell.value = null
  activePropertyTab.value = 'column'
}

const selectRow = (index: number) => {
  selectedRow.value = index
  selectedColumn.value = null
  selectedCell.value = null
}

const selectCell = (row: number, col: number) => {
  selectedCell.value = { row, col }
  selectedRow.value = null
  selectedColumn.value = null
}

const editCell = (row: number, col: number) => {
  // 编辑单元格逻辑
  message.info('单元格编辑功能开发中')
}

const showColumnMenu = (event: MouseEvent, index: number) => {
  event.preventDefault()
  selectColumn(index)
}

const handleColumnAction = ({ key }: { key: string }, index: number) => {
  switch (key) {
    case 'edit':
      editingColumn.value = columns.value[index]
      columnEditVisible.value = true
      break
    case 'sort':
      columns.value[index].sortable = !columns.value[index].sortable
      message.success(`排序${columns.value[index].sortable ? '已启用' : '已禁用'}`)
      break
    case 'filter':
      columns.value[index].filterable = !columns.value[index].filterable
      message.success(`筛选${columns.value[index].filterable ? '已启用' : '已禁用'}`)
      break
    case 'format':
      editingColumn.value = columns.value[index]
      formatModalVisible.value = true
      break
    case 'insert-left':
      insertColumn(index)
      break
    case 'insert-right':
      insertColumn(index + 1)
      break
    case 'delete':
      deleteColumn(index)
      break
  }
}

const insertColumn = (index: number) => {
  const newColumn = {
    id: generateId('col_'),
    title: `新列${columns.value.length + 1}`,
    dataIndex: `newCol${columns.value.length + 1}`,
    width: 120,
    align: 'left',
    dataType: 'text',
    sortable: false,
    filterable: false,
    resizable: true,
    fixed: false
  }
  columns.value.splice(index, 0, newColumn)
  message.success('插入列成功')
}

const deleteColumn = (index: number) => {
  if (columns.value.length <= 1) {
    message.warning('至少需要保留一列')
    return
  }
  
  columns.value.splice(index, 1)
  selectedColumn.value = null
  message.success('删除列成功')
}

const startResize = (event: MouseEvent, index: number) => {
  // 列宽调整逻辑
  const startX = event.clientX
  const startWidth = columns.value[index].width
  
  const handleMouseMove = (e: MouseEvent) => {
    const diff = e.clientX - startX
    const newWidth = Math.max(50, startWidth + diff)
    columns.value[index].width = newWidth
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleColumnSave = (columnData: any) => {
  if (editingColumn.value) {
    Object.assign(editingColumn.value, columnData)
    message.success('列配置已保存')
  }
}

const handleFormatSave = (formatData: any) => {
  if (editingColumn.value) {
    editingColumn.value.conditionalFormat = formatData
    message.success('条件格式已保存')
  }
}

// 监听配置变化
watch(() => [config, columns.value], () => {
  const updatedConfig = {
    ...props.config,
    tableConfig: {
      ...config,
      columns: columns.value
    }
  }
  emit('update', updatedConfig)
}, { deep: true })
</script>

<style scoped lang="less">
.table-designer {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .designer-toolbar {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }
  
  .table-design-area {
    flex: 1;
    padding: 16px;
    overflow: auto;
    
    .table-container {
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      overflow: hidden;
      
      .table-header {
        display: flex;
        background: #fafafa;
        
        .table-header-cell {
          position: relative;
          border-right: 1px solid #f0f0f0;
          padding: 8px 12px;
          font-weight: 500;
          cursor: pointer;
          
          &:last-child {
            border-right: none;
          }
          
          &.selected {
            background: #e6f7ff;
            border-color: #1890ff;
          }
          
          .cell-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            
            .column-title {
              flex: 1;
            }
            
            .column-actions {
              opacity: 0;
              transition: opacity 0.2s;
            }
          }
          
          &:hover .column-actions {
            opacity: 1;
          }
          
          .resize-handle {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            cursor: col-resize;
            background: transparent;
            
            &:hover {
              background: #1890ff;
            }
          }
        }
      }
      
      .table-body {
        .table-row {
          display: flex;
          
          &.striped {
            background: #fafafa;
          }
          
          &.selected {
            background: #e6f7ff;
          }
          
          &:hover {
            background: #f5f5f5;
          }
          
          .table-cell {
            border-right: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
            padding: 8px 12px;
            
            &:last-child {
              border-right: none;
            }
            
            &.selected {
              background: #e6f7ff;
              border-color: #1890ff;
            }
            
            .cell-content {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
      
      .table-empty {
        padding: 40px;
        text-align: center;
      }
    }
  }
  
  .table-properties {
    width: 300px;
    border-left: 1px solid #f0f0f0;
    background: #fafafa;
    
    .property-section {
      padding: 16px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }
    }
    
    .property-placeholder {
      padding: 40px 16px;
      text-align: center;
    }
  }
}

.danger-item {
  color: #ff4d4f !important;
}
</style>
