<!--
  字段面板
  @description 已选择字段的管理面板，支持字段配置、排序、分组等操作
-->
<template>
  <div class="field-panel">
    <!-- 字段操作栏 -->
    <div class="field-actions">
      <a-space>
        <a-button size="small" @click="addCalculatedField">
          <PlusOutlined />
          计算字段
        </a-button>
        <a-button size="small" @click="clearAllFields" :disabled="fields.length === 0">
          <ClearOutlined />
          清空
        </a-button>
      </a-space>
    </div>

    <!-- 字段分组 -->
    <div class="field-groups">
      <!-- 维度字段 -->
      <div class="field-group">
        <div class="group-header">
          <span class="group-title">
            <AppstoreOutlined />
            维度字段
          </span>
          <a-badge :count="dimensionFields.length" :number-style="{ backgroundColor: '#52c41a' }" />
        </div>
        <div
          class="field-drop-zone"
          :class="{ 'drag-over': dragOverZone === 'dimension' }"
          @drop="handleFieldDrop('dimension', $event)"
          @dragover="handleDragOver('dimension', $event)"
          @dragleave="handleDragLeave"
        >
          <draggable
            v-model="dimensionFields"
            group="fields"
            item-key="id"
            :animation="200"
            @change="handleFieldOrderChange"
          >
            <template #item="{ element: field, index }">
              <FieldItem
                :field="field"
                :index="index"
                @configure="handleFieldConfigure"
                @remove="handleFieldRemove"
                @duplicate="handleFieldDuplicate"
              />
            </template>
          </draggable>
          
          <div v-if="dimensionFields.length === 0" class="empty-zone">
            <FileTextOutlined />
            <span>拖拽字段到此处作为维度</span>
          </div>
        </div>
      </div>

      <!-- 度量字段 -->
      <div class="field-group">
        <div class="group-header">
          <span class="group-title">
            <BarChartOutlined />
            度量字段
          </span>
          <a-badge :count="measureFields.length" :number-style="{ backgroundColor: '#1890ff' }" />
        </div>
        <div
          class="field-drop-zone"
          :class="{ 'drag-over': dragOverZone === 'measure' }"
          @drop="handleFieldDrop('measure', $event)"
          @dragover="handleDragOver('measure', $event)"
          @dragleave="handleDragLeave"
        >
          <draggable
            v-model="measureFields"
            group="fields"
            item-key="id"
            :animation="200"
            @change="handleFieldOrderChange"
          >
            <template #item="{ element: field, index }">
              <FieldItem
                :field="field"
                :index="index"
                @configure="handleFieldConfigure"
                @remove="handleFieldRemove"
                @duplicate="handleFieldDuplicate"
              />
            </template>
          </draggable>
          
          <div v-if="measureFields.length === 0" class="empty-zone">
            <LineChartOutlined />
            <span>拖拽字段到此处作为度量</span>
          </div>
        </div>
      </div>

      <!-- 筛选字段 -->
      <div class="field-group">
        <div class="group-header">
          <span class="group-title">
            <FilterOutlined />
            筛选字段
          </span>
          <a-badge :count="filterFields.length" :number-style="{ backgroundColor: '#fa8c16' }" />
        </div>
        <div
          class="field-drop-zone"
          :class="{ 'drag-over': dragOverZone === 'filter' }"
          @drop="handleFieldDrop('filter', $event)"
          @dragover="handleDragOver('filter', $event)"
          @dragleave="handleDragLeave"
        >
          <draggable
            v-model="filterFields"
            group="fields"
            item-key="id"
            :animation="200"
            @change="handleFieldOrderChange"
          >
            <template #item="{ element: field, index }">
              <FieldItem
                :field="field"
                :index="index"
                @configure="handleFieldConfigure"
                @remove="handleFieldRemove"
                @duplicate="handleFieldDuplicate"
              />
            </template>
          </draggable>
          
          <div v-if="filterFields.length === 0" class="empty-zone">
            <FilterOutlined />
            <span>拖拽字段到此处作为筛选</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 字段统计 -->
    <div class="field-summary">
      <a-descriptions size="small" :column="3">
        <a-descriptions-item label="总字段">{{ totalFieldCount }}</a-descriptions-item>
        <a-descriptions-item label="维度">{{ dimensionFields.length }}</a-descriptions-item>
        <a-descriptions-item label="度量">{{ measureFields.length }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 计算字段弹窗 -->
    <CalculatedFieldModal
      v-model:visible="calculatedFieldVisible"
      :available-fields="allFields"
      @save="handleCalculatedFieldSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import draggable from 'vuedraggable'
import {
  PlusOutlined,
  ClearOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  LineChartOutlined,
  FilterOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import FieldItem from './FieldItem.vue'
import CalculatedFieldModal from './CalculatedFieldModal.vue'
import type { ReportField } from '../../types/report'
import { generateId } from '../../utils/report-utils'

// 组件属性
interface Props {
  fields: ReportField[]
  availableFields?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  availableFields: () => []
})

// 组件事件
const emit = defineEmits<{
  'update:fields': [fields: ReportField[]]
  fieldSelect: [field: ReportField]
  fieldRemove: [fieldId: string]
  fieldConfigure: [field: ReportField]
}>()

// 响应式数据
const dragOverZone = ref<string | null>(null)
const calculatedFieldVisible = ref(false)

// 计算属性
const allFields = computed(() => props.fields)

const dimensionFields = computed({
  get: () => props.fields.filter(field => field.fieldType === 'dimension'),
  set: (value) => updateFieldsByType('dimension', value)
})

const measureFields = computed({
  get: () => props.fields.filter(field => field.fieldType === 'measure'),
  set: (value) => updateFieldsByType('measure', value)
})

const filterFields = computed({
  get: () => props.fields.filter(field => field.fieldType === 'filter'),
  set: (value) => updateFieldsByType('filter', value)
})

const totalFieldCount = computed(() => props.fields.length)

// 工具方法
const updateFieldsByType = (type: string, newFields: ReportField[]) => {
  const otherFields = props.fields.filter(field => field.fieldType !== type)
  const updatedFields = [...otherFields, ...newFields.map(field => ({ ...field, fieldType: type }))]
  emit('update:fields', updatedFields)
}

const createFieldFromData = (data: any, fieldType: string): ReportField => {
  return {
    id: generateId('field_'),
    name: data.name || data.title,
    label: data.title || data.name,
    dataType: mapDataType(data.type || data.dataType),
    fieldType,
    required: false,
    visible: true,
    aggregation: fieldType === 'measure' ? 'SUM' : undefined,
    sort: { enabled: false },
    filter: { enabled: fieldType === 'filter' },
    format: getDefaultFormat(data.type || data.dataType)
  }
}

const mapDataType = (dbType: string): string => {
  const type = dbType?.toLowerCase() || ''
  if (type.includes('int') || type.includes('decimal') || type.includes('float')) {
    return 'INTEGER'
  }
  if (type.includes('date') || type.includes('time')) {
    return 'DATE'
  }
  return 'STRING'
}

const getDefaultFormat = (dataType: string) => {
  const type = dataType?.toLowerCase() || ''
  if (type.includes('int') || type.includes('decimal') || type.includes('float')) {
    return { type: 'number', decimals: 2, thousandSeparator: true }
  }
  if (type.includes('date')) {
    return { type: 'date', dateFormat: 'YYYY-MM-DD' }
  }
  return { type: 'custom' }
}

// 事件处理
const handleDragOver = (zone: string, event: DragEvent) => {
  event.preventDefault()
  dragOverZone.value = zone
}

const handleDragLeave = () => {
  dragOverZone.value = null
}

const handleFieldDrop = (fieldType: string, event: DragEvent) => {
  event.preventDefault()
  dragOverZone.value = null
  
  try {
    const data = JSON.parse(event.dataTransfer?.getData('application/json') || '{}')
    
    if (data.type === 'field') {
      // 从数据源面板拖拽的字段
      const newField = createFieldFromData(data.data, fieldType)
      
      // 检查是否已存在
      const existingField = props.fields.find(f => f.name === newField.name)
      if (existingField) {
        message.warning('字段已存在')
        return
      }
      
      const updatedFields = [...props.fields, newField]
      emit('update:fields', updatedFields)
      message.success('字段添加成功')
    }
  } catch (error) {
    console.error('处理字段拖放失败:', error)
    message.error('添加字段失败')
  }
}

const handleFieldOrderChange = () => {
  // 字段顺序变化时触发
  const allUpdatedFields = [
    ...dimensionFields.value,
    ...measureFields.value,
    ...filterFields.value
  ]
  emit('update:fields', allUpdatedFields)
}

const handleFieldConfigure = (field: ReportField) => {
  emit('fieldConfigure', field)
}

const handleFieldRemove = (fieldId: string) => {
  const updatedFields = props.fields.filter(f => f.id !== fieldId)
  emit('update:fields', updatedFields)
  emit('fieldRemove', fieldId)
  message.success('字段已移除')
}

const handleFieldDuplicate = (field: ReportField) => {
  const duplicatedField: ReportField = {
    ...field,
    id: generateId('field_'),
    name: `${field.name}_copy`,
    label: `${field.label}_副本`
  }
  
  const updatedFields = [...props.fields, duplicatedField]
  emit('update:fields', updatedFields)
  message.success('字段已复制')
}

const addCalculatedField = () => {
  calculatedFieldVisible.value = true
}

const handleCalculatedFieldSave = (calculatedField: ReportField) => {
  const updatedFields = [...props.fields, calculatedField]
  emit('update:fields', updatedFields)
  calculatedFieldVisible.value = false
  message.success('计算字段添加成功')
}

const clearAllFields = () => {
  emit('update:fields', [])
  message.success('已清空所有字段')
}
</script>

<style scoped lang="less">
.field-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .field-actions {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .field-groups {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    
    .field-group {
      margin-bottom: 16px;
      
      .group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: #fafafa;
        border-radius: 6px 6px 0 0;
        border: 1px solid #f0f0f0;
        border-bottom: none;
        
        .group-title {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          font-weight: 500;
          color: #262626;
        }
      }
      
      .field-drop-zone {
        min-height: 60px;
        border: 1px solid #f0f0f0;
        border-radius: 0 0 6px 6px;
        background: #fff;
        transition: all 0.2s;
        
        &.drag-over {
          border-color: #1890ff;
          background: #f6ffed;
        }
        
        .empty-zone {
          height: 60px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #bfbfbf;
          font-size: 12px;
          gap: 4px;
          
          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .field-summary {
    padding: 12px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }
}

// 拖拽组件样式
:deep(.sortable-ghost) {
  opacity: 0.5;
}

:deep(.sortable-chosen) {
  transform: rotate(5deg);
}
</style>
