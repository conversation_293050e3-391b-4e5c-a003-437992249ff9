<!--
  组件库面板
  @description 提供可拖拽的报表组件库，支持分类展示和搜索功能
-->
<template>
  <div class="component-panel">
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索组件..."
        allow-clear
        @input="handleSearch"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
    </div>

    <!-- 组件分类 -->
    <div class="category-section">
      <a-collapse v-model:activeKey="activeCategories" ghost>
        <a-collapse-panel
          v-for="category in filteredCategories"
          :key="category.key"
          :header="category.name"
        >
          <template #extra>
            <a-badge :count="category.components.length" :number-style="{ backgroundColor: '#52c41a' }" />
          </template>
          
          <!-- 组件列表 -->
          <div class="component-list">
            <div
              v-for="component in category.components"
              :key="component.key"
              class="component-item"
              :draggable="true"
              @dragstart="handleDragStart(component, $event)"
              @dragend="handleDragEnd"
              @click="handleComponentClick(component)"
            >
              <div class="component-icon">
                <component :is="component.icon" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.name }}</div>
                <div class="component-desc">{{ component.description }}</div>
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 组件预览弹窗 -->
    <a-modal
      v-model:visible="previewVisible"
      :title="currentComponent?.name"
      width="800px"
      :footer="null"
    >
      <div class="component-preview">
        <div class="preview-content">
          <component
            :is="currentComponent?.previewComponent"
            v-if="currentComponent?.previewComponent"
            :config="currentComponent?.defaultConfig"
          />
          <div v-else class="preview-placeholder">
            <component :is="currentComponent?.icon" style="font-size: 48px; color: #d9d9d9;" />
            <p>暂无预览</p>
          </div>
        </div>
        <div class="preview-info">
          <h4>组件说明</h4>
          <p>{{ currentComponent?.description }}</p>
          <h4>使用场景</h4>
          <p>{{ currentComponent?.usage }}</p>
          <h4>配置项</h4>
          <a-table
            :columns="configColumns"
            :data-source="currentComponent?.configOptions || []"
            :pagination="false"
            size="small"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { componentLibrary } from '../../config/component-library'
import type { ComponentTemplate, ComponentCategory } from '../../types/component'

// 组件事件
const emit = defineEmits<{
  componentDragStart: [component: ComponentTemplate, event: DragEvent]
  componentDragEnd: []
  componentSelect: [component: ComponentTemplate]
}>()

// 响应式数据
const searchKeyword = ref('')
const activeCategories = ref<string[]>([])
const previewVisible = ref(false)
const currentComponent = ref<ComponentTemplate | null>(null)

// 配置表格列
const configColumns = [
  { title: '配置项', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '默认值', dataIndex: 'default', key: 'default' },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

// 计算属性
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return componentLibrary.categories
  }
  
  return componentLibrary.categories
    .map(category => ({
      ...category,
      components: category.components.filter(component =>
        component.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        component.description.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        component.tags?.some(tag => tag.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    }))
    .filter(category => category.components.length > 0)
})

// 事件处理
const handleSearch = () => {
  // 搜索时自动展开所有分类
  if (searchKeyword.value) {
    activeCategories.value = filteredCategories.value.map(cat => cat.key)
  }
}

const handleDragStart = (component: ComponentTemplate, event: DragEvent) => {
  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(component))
    event.dataTransfer.effectAllowed = 'copy'
  }
  
  // 添加拖拽样式
  const target = event.target as HTMLElement
  target.classList.add('dragging')
  
  emit('componentDragStart', component, event)
}

const handleDragEnd = () => {
  // 移除拖拽样式
  document.querySelectorAll('.component-item.dragging').forEach(el => {
    el.classList.remove('dragging')
  })
  
  emit('componentDragEnd')
}

const handleComponentClick = (component: ComponentTemplate) => {
  currentComponent.value = component
  previewVisible.value = true
  emit('componentSelect', component)
}

// 生命周期
onMounted(() => {
  // 默认展开第一个分类
  if (componentLibrary.categories.length > 0) {
    activeCategories.value = [componentLibrary.categories[0].key]
  }
})
</script>

<style scoped lang="less">
.component-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-section {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .category-section {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    
    .component-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .component-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        cursor: grab;
        transition: all 0.2s;
        background: #fff;
        
        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
          transform: translateY(-1px);
        }
        
        &:active {
          cursor: grabbing;
        }
        
        &.dragging {
          opacity: 0.5;
          transform: rotate(5deg);
        }
        
        .component-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
          border-radius: 4px;
          margin-right: 8px;
          font-size: 16px;
          color: #1890ff;
        }
        
        .component-info {
          flex: 1;
          min-width: 0;
          
          .component-name {
            font-size: 12px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .component-desc {
            font-size: 11px;
            color: #8c8c8c;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
  }
  
  .component-preview {
    .preview-content {
      height: 300px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      background: #fafafa;
      
      .preview-placeholder {
        text-align: center;
        color: #8c8c8c;
        
        p {
          margin-top: 8px;
          margin-bottom: 0;
        }
      }
    }
    
    .preview-info {
      h4 {
        margin-top: 16px;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        
        &:first-child {
          margin-top: 0;
        }
      }
      
      p {
        margin-bottom: 0;
        color: #595959;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

// 折叠面板样式覆盖
:deep(.ant-collapse) {
  .ant-collapse-item {
    border: none;
    
    .ant-collapse-header {
      padding: 8px 12px;
      font-size: 12px;
      font-weight: 500;
      color: #262626;
      background: #fafafa;
      border-radius: 4px;
      margin-bottom: 4px;
      
      .ant-collapse-arrow {
        font-size: 10px;
      }
    }
    
    .ant-collapse-content {
      border: none;
      background: transparent;
      
      .ant-collapse-content-box {
        padding: 8px 4px;
      }
    }
  }
}
</style>
