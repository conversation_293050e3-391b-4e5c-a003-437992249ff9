<!--
  数据源管理组件
  @description 支持多种数据源配置管理，包括MySQL、PostgreSQL、MongoDB、API接口等
-->
<template>
  <div class="datasource-manager">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <DatabaseOutlined />
          数据源管理
        </h2>
        <p class="page-description">管理报表系统的数据源连接，支持多种数据库和API接口</p>
      </div>
      <div class="header-actions">
        <a-button @click="refreshDataSources" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          新建数据源
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总数据源"
              :value="statistics.totalCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃连接"
              :value="statistics.activeCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <LinkOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="连接成功率"
              :value="statistics.connectionSuccessRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均响应时间"
              :value="statistics.averageResponseTime"
              suffix="ms"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="数据源名称">
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入数据源名称"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="数据源类型">
            <a-select
              v-model:value="searchForm.type"
              placeholder="请选择类型"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="MYSQL">MySQL</a-select-option>
              <a-select-option value="POSTGRESQL">PostgreSQL</a-select-option>
              <a-select-option value="ORACLE">Oracle</a-select-option>
              <a-select-option value="SQLSERVER">SQL Server</a-select-option>
              <a-select-option value="MONGODB">MongoDB</a-select-option>
              <a-select-option value="REDIS">Redis</a-select-option>
              <a-select-option value="ELASTICSEARCH">Elasticsearch</a-select-option>
              <a-select-option value="REST_API">REST API</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="连接状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="ACTIVE">正常</a-select-option>
              <a-select-option value="DISABLED">禁用</a-select-option>
              <a-select-option value="CONNECTION_FAILED">连接失败</a-select-option>
              <a-select-option value="MAINTENANCE">维护中</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 数据源列表 -->
    <div class="datasource-list-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="dataSources"
          :loading="loading"
          :pagination="paginationConfig"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 数据源信息 -->
          <template #datasourceInfo="{ record }">
            <div class="datasource-info">
              <div class="datasource-name">
                <a-avatar :size="32" style="margin-right: 8px">
                  <template #icon>
                    <component :is="getDataSourceIcon(record.type)" />
                  </template>
                </a-avatar>
                <div>
                  <div class="name">{{ record.name }}</div>
                  <div class="description">{{ record.description }}</div>
                </div>
              </div>
            </div>
          </template>

          <!-- 数据源类型 -->
          <template #type="{ record }">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <!-- 连接信息 -->
          <template #connection="{ record }">
            <div class="connection-info">
              <div class="host">{{ getConnectionDisplay(record) }}</div>
              <div class="database">{{ record.connection.database || '-' }}</div>
            </div>
          </template>

          <!-- 连接状态 -->
          <template #status="{ record }">
            <div class="status-info">
              <a-badge
                :status="getStatusBadge(record.status)"
                :text="getStatusText(record.status)"
              />
              <div class="last-connected" v-if="record.lastConnectedAt">
                最后连接: {{ formatTime(record.lastConnectedAt) }}
              </div>
            </div>
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="testConnection(record)">
                <ApiOutlined />
                测试
              </a-button>
              <a-button type="link" size="small" @click="viewMetadata(record)">
                <TableOutlined />
                元数据
              </a-button>
              <a-button type="link" size="small" @click="editDataSource(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="toggleDataSourceStatus(record)">
                      {{ record.status === 'ACTIVE' ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item @click="copyDataSource(record)">
                      复制配置
                    </a-menu-item>
                    <a-menu-item @click="viewPerformance(record)">
                      性能监控
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteDataSource(record)" class="danger-item">
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedRowKeys.length > 0" class="batch-actions">
      <a-card>
        <div class="batch-info">
          已选择 {{ selectedRowKeys.length }} 个数据源
        </div>
        <div class="batch-buttons">
          <a-space>
            <a-button @click="batchTestConnection" :loading="batchTesting">
              <ApiOutlined />
              批量测试
            </a-button>
            <a-button @click="batchUpdateStatus('DISABLED')">
              <StopOutlined />
              批量禁用
            </a-button>
            <a-button @click="batchUpdateStatus('ACTIVE')">
              <PlayCircleOutlined />
              批量启用
            </a-button>
            <a-button @click="exportDataSources">
              <DownloadOutlined />
              导出配置
            </a-button>
            <a-button danger @click="batchDeleteDataSources">
              <DeleteOutlined />
              批量删除
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 创建/编辑数据源弹窗 -->
    <DataSourceModal
      v-model:visible="modalVisible"
      :datasource-data="currentDataSourceData"
      :mode="modalMode"
      @success="handleModalSuccess"
    />

    <!-- 元数据查看弹窗 -->
    <MetadataModal
      v-model:visible="metadataVisible"
      :datasource-data="currentDataSourceData"
    />

    <!-- 性能监控弹窗 -->
    <PerformanceModal
      v-model:visible="performanceVisible"
      :datasource-data="currentDataSourceData"
    />

    <!-- 连接测试结果弹窗 -->
    <ConnectionTestModal
      v-model:visible="testResultVisible"
      :test-result="testResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  DatabaseOutlined,
  ReloadOutlined,
  PlusOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  ApiOutlined,
  TableOutlined,
  EditOutlined,
  DownOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { useDataSourceStore } from '../../stores/datasource'
import DataSourceModal from './DataSourceModal.vue'
import MetadataModal from './MetadataModal.vue'
import PerformanceModal from './PerformanceModal.vue'
import ConnectionTestModal from './ConnectionTestModal.vue'
import type { DataSourceConfig, DataSourceQueryParams, ConnectionTestResult } from '../../types/datasource'
import { formatTime } from '../../utils/report-utils'

// 状态管理
const dataSourceStore = useDataSourceStore()
const { dataSources, loading, pagination, statistics } = storeToRefs(dataSourceStore)

// 响应式数据
const searchForm = reactive<DataSourceQueryParams>({})
const selectedRowKeys = ref<string[]>([])
const modalVisible = ref(false)
const metadataVisible = ref(false)
const performanceVisible = ref(false)
const testResultVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentDataSourceData = ref<DataSourceConfig | null>(null)
const testResult = ref<ConnectionTestResult | null>(null)
const batchTesting = ref(false)

// 表格列配置
const columns = [
  {
    title: '数据源信息',
    key: 'datasourceInfo',
    slots: { customRender: 'datasourceInfo' },
    width: 280
  },
  {
    title: '类型',
    key: 'type',
    slots: { customRender: 'type' },
    width: 120
  },
  {
    title: '连接信息',
    key: 'connection',
    slots: { customRender: 'connection' },
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    customRender: ({ text }) => formatTime(text)
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}))

// 工具方法
const getDataSourceIcon = (type: string) => {
  const icons = {
    MYSQL: 'DatabaseOutlined',
    POSTGRESQL: 'DatabaseOutlined',
    ORACLE: 'DatabaseOutlined',
    SQLSERVER: 'DatabaseOutlined',
    MONGODB: 'DatabaseOutlined',
    REDIS: 'DatabaseOutlined',
    ELASTICSEARCH: 'SearchOutlined',
    REST_API: 'ApiOutlined',
    GRAPHQL: 'ApiOutlined'
  }
  return icons[type] || 'DatabaseOutlined'
}

const getTypeColor = (type: string) => {
  const colors = {
    MYSQL: 'blue',
    POSTGRESQL: 'cyan',
    ORACLE: 'red',
    SQLSERVER: 'orange',
    MONGODB: 'green',
    REDIS: 'volcano',
    ELASTICSEARCH: 'purple',
    REST_API: 'geekblue',
    GRAPHQL: 'magenta'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts = {
    MYSQL: 'MySQL',
    POSTGRESQL: 'PostgreSQL',
    ORACLE: 'Oracle',
    SQLSERVER: 'SQL Server',
    MONGODB: 'MongoDB',
    REDIS: 'Redis',
    ELASTICSEARCH: 'Elasticsearch',
    REST_API: 'REST API',
    GRAPHQL: 'GraphQL'
  }
  return texts[type] || type
}

const getConnectionDisplay = (record: DataSourceConfig) => {
  const { connection } = record
  if (connection.host && connection.port) {
    return `${connection.host}:${connection.port}`
  }
  if (connection.url) {
    return connection.url
  }
  return '-'
}

const getStatusBadge = (status: string) => {
  const badges = {
    ACTIVE: 'success',
    DISABLED: 'default',
    CONNECTION_FAILED: 'error',
    MAINTENANCE: 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    ACTIVE: '正常',
    DISABLED: '禁用',
    CONNECTION_FAILED: '连接失败',
    MAINTENANCE: '维护中'
  }
  return texts[status] || status
}

// 事件处理
const handleSearch = () => {
  dataSourceStore.setPagination(1, pagination.value.pageSize)
  fetchDataSources()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    delete searchForm[key]
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  dataSourceStore.setPagination(pag.current, pag.pageSize)
  fetchDataSources()
}

const refreshDataSources = () => {
  fetchDataSources()
}

const showCreateModal = () => {
  modalMode.value = 'create'
  currentDataSourceData.value = null
  modalVisible.value = true
}

const editDataSource = (dataSource: DataSourceConfig) => {
  modalMode.value = 'edit'
  currentDataSourceData.value = dataSource
  modalVisible.value = true
}

const testConnection = async (dataSource: DataSourceConfig) => {
  try {
    const result = await dataSourceStore.testConnection(dataSource.id)
    testResult.value = result
    testResultVisible.value = true
  } catch (error) {
    message.error('连接测试失败')
  }
}

const viewMetadata = (dataSource: DataSourceConfig) => {
  currentDataSourceData.value = dataSource
  metadataVisible.value = true
}

const viewPerformance = (dataSource: DataSourceConfig) => {
  currentDataSourceData.value = dataSource
  performanceVisible.value = true
}

const toggleDataSourceStatus = async (dataSource: DataSourceConfig) => {
  const newStatus = dataSource.status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE'
  await dataSourceStore.updateDataSourceStatus(dataSource.id, newStatus)
}

const copyDataSource = (dataSource: DataSourceConfig) => {
  modalMode.value = 'create'
  currentDataSourceData.value = { 
    ...dataSource, 
    name: `${dataSource.name}_副本`,
    id: ''
  }
  modalVisible.value = true
}

const deleteDataSource = (dataSource: DataSourceConfig) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除数据源"${dataSource.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await dataSourceStore.deleteDataSource(dataSource.id)
    }
  })
}

const batchTestConnection = async () => {
  try {
    batchTesting.value = true
    await dataSourceStore.batchTestConnection(selectedRowKeys.value)
    message.success('批量测试完成')
  } catch (error) {
    message.error('批量测试失败')
  } finally {
    batchTesting.value = false
  }
}

const batchUpdateStatus = async (status: string) => {
  try {
    await dataSourceStore.batchUpdateStatus(selectedRowKeys.value, status)
    selectedRowKeys.value = []
  } catch (error) {
    message.error('批量更新状态失败')
  }
}

const exportDataSources = async () => {
  try {
    await dataSourceStore.exportDataSources(selectedRowKeys.value)
    message.success('导出配置成功')
  } catch (error) {
    message.error('导出配置失败')
  }
}

const batchDeleteDataSources = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个数据源吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await dataSourceStore.batchDeleteDataSources(selectedRowKeys.value)
        selectedRowKeys.value = []
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

const handleModalSuccess = () => {
  modalVisible.value = false
  fetchDataSources()
}

const fetchDataSources = () => {
  dataSourceStore.fetchDataSources(searchForm)
}

// 生命周期
onMounted(() => {
  fetchDataSources()
  dataSourceStore.fetchStatistics()
})
</script>

<style scoped lang="less">
.datasource-manager {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 16px;
  }
  
  .search-section {
    margin-bottom: 16px;
  }
  
  .datasource-info {
    .datasource-name {
      display: flex;
      align-items: center;
      
      .name {
        font-weight: 500;
        color: #262626;
      }
      
      .description {
        color: #8c8c8c;
        font-size: 12px;
        margin-top: 2px;
      }
    }
  }
  
  .connection-info {
    .host {
      font-weight: 500;
      color: #262626;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
    }
    
    .database {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .status-info {
    .last-connected {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 4px;
    }
  }
  
  .batch-actions {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    
    .ant-card {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      
      .batch-info {
        margin-right: 16px;
        font-weight: 500;
      }
    }
  }
  
  .danger-item {
    color: #ff4d4f !important;
  }
}
</style>
