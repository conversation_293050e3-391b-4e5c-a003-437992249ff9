/**
 * 支付模块工具函数
 * @description 支付相关的通用工具函数
 */

import dayjs from 'dayjs'
import { message } from 'ant-design-vue'

/**
 * 格式化时间
 * @param time 时间字符串或时间戳
 * @param format 格式化模板
 * @returns 格式化后的时间字符串
 */
export const formatTime = (time: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!time) return '-'
  return dayjs(time).format(format)
}

/**
 * 格式化金额
 * @param amount 金额(分)
 * @param currency 货币类型
 * @param showSymbol 是否显示货币符号
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (amount: number, currency = 'CNY', showSymbol = true): string => {
  if (typeof amount !== 'number') return '0.00'
  
  const value = (amount / 100).toFixed(2)
  const symbols = {
    CNY: '¥',
    USD: '$',
    EUR: '€',
    JPY: '¥',
    GBP: '£',
    HKD: 'HK$'
  }
  
  const symbol = showSymbol ? (symbols[currency] || '') : ''
  return `${symbol}${value}`
}

/**
 * 格式化订单号
 * @param orderNo 订单号
 * @param showPrefix 是否显示前缀
 * @returns 格式化后的订单号
 */
export const formatOrderNo = (orderNo: string, showPrefix = false): string => {
  if (!orderNo) return '-'
  
  if (showPrefix) {
    return `订单号: ${orderNo}`
  }
  
  // 如果订单号很长，中间用省略号显示
  if (orderNo.length > 20) {
    return `${orderNo.substring(0, 8)}...${orderNo.substring(orderNo.length - 8)}`
  }
  
  return orderNo
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successMessage 成功提示消息
 */
export const copyToClipboard = async (text: string, successMessage = '已复制到剪贴板'): Promise<void> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    message.success(successMessage)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

/**
 * 生成随机订单号
 * @param prefix 前缀
 * @returns 订单号
 */
export const generateOrderNo = (prefix = 'PAY'): string => {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `${prefix}${timestamp}${random}`
}

/**
 * 验证金额格式
 * @param amount 金额字符串
 * @returns 是否有效
 */
export const validateAmount = (amount: string): boolean => {
  const regex = /^\d+(\.\d{1,2})?$/
  return regex.test(amount) && parseFloat(amount) > 0
}

/**
 * 转换金额单位(元转分)
 * @param yuan 元
 * @returns 分
 */
export const yuanToFen = (yuan: number | string): number => {
  const value = typeof yuan === 'string' ? parseFloat(yuan) : yuan
  return Math.round(value * 100)
}

/**
 * 转换金额单位(分转元)
 * @param fen 分
 * @returns 元
 */
export const fenToYuan = (fen: number): number => {
  return fen / 100
}

/**
 * 获取支付渠道图标
 * @param channelType 渠道类型
 * @returns 图标URL
 */
export const getChannelIcon = (channelType: string): string => {
  const icons = {
    ALIPAY: '/icons/payment/alipay.png',
    WECHAT: '/icons/payment/wechat.png',
    UNIONPAY: '/icons/payment/unionpay.png',
    PAYPAL: '/icons/payment/paypal.png',
    STRIPE: '/icons/payment/stripe.png',
    SQUARE: '/icons/payment/square.png'
  }
  return icons[channelType] || '/icons/payment/default.png'
}

/**
 * 获取支付渠道名称
 * @param channelType 渠道类型
 * @returns 渠道名称
 */
export const getChannelName = (channelType: string): string => {
  const names = {
    ALIPAY: '支付宝',
    WECHAT: '微信支付',
    UNIONPAY: '银联支付',
    PAYPAL: 'PayPal',
    STRIPE: 'Stripe',
    SQUARE: 'Square'
  }
  return names[channelType] || channelType
}

/**
 * 获取支付方式名称
 * @param paymentMethod 支付方式
 * @returns 支付方式名称
 */
export const getPaymentMethodName = (paymentMethod: string): string => {
  const names = {
    QR_CODE: '扫码支付',
    WEB: '网页支付',
    MOBILE: '手机支付',
    APP: 'APP支付',
    MINI_PROGRAM: '小程序支付',
    JSAPI: '公众号支付',
    MICROPAY: '刷卡支付',
    QUICK_PAY: '快捷支付'
  }
  return names[paymentMethod] || paymentMethod
}

/**
 * 获取订单状态颜色
 * @param status 订单状态
 * @returns 颜色值
 */
export const getOrderStatusColor = (status: string): string => {
  const colors = {
    PENDING: '#1890ff',
    PROCESSING: '#722ed1',
    SUCCESS: '#52c41a',
    FAILED: '#ff4d4f',
    CANCELLED: '#d9d9d9',
    TIMEOUT: '#faad14',
    PARTIAL_REFUNDED: '#fa8c16',
    FULL_REFUNDED: '#8c8c8c'
  }
  return colors[status] || '#d9d9d9'
}

/**
 * 获取订单状态文本
 * @param status 订单状态
 * @returns 状态文本
 */
export const getOrderStatusText = (status: string): string => {
  const texts = {
    PENDING: '待支付',
    PROCESSING: '处理中',
    SUCCESS: '支付成功',
    FAILED: '支付失败',
    CANCELLED: '已取消',
    TIMEOUT: '支付超时',
    PARTIAL_REFUNDED: '部分退款',
    FULL_REFUNDED: '全额退款'
  }
  return texts[status] || status
}

/**
 * 计算订单超时时间
 * @param createdAt 创建时间
 * @param expireMinutes 过期分钟数
 * @returns 是否超时
 */
export const isOrderExpired = (createdAt: string, expireMinutes = 30): boolean => {
  const createTime = dayjs(createdAt)
  const expireTime = createTime.add(expireMinutes, 'minute')
  return dayjs().isAfter(expireTime)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 生成二维码数据URL
 * @param text 二维码内容
 * @param size 二维码大小
 * @returns 二维码数据URL
 */
export const generateQRCode = async (text: string, size = 200): Promise<string> => {
  try {
    // 这里可以使用 qrcode 库生成二维码
    // 为了简化，这里返回一个占位符
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="12">
          QR Code: ${text.substring(0, 20)}...
        </text>
      </svg>
    `)}`
  } catch (error) {
    console.error('生成二维码失败:', error)
    throw error
  }
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  let previous = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func(...args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func(...args)
      }, remaining)
    }
  }
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filename 文件名
 */
export const downloadFile = (url: string, filename?: string): void => {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
