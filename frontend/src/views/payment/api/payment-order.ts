/**
 * 支付订单API接口
 * @description 支付订单管理相关的API接口定义
 */

import axios from 'axios'
import type { AxiosResponse } from 'axios'
import type {
  PaymentOrder,
  PaymentOrderQueryParams,
  CreatePaymentOrderParams,
  UpdatePaymentOrderParams,
  OrderActionParams,
  CancelOrderParams,
  OrderStatistics,
  OrderTrendData,
  OrderExportParams,
  OrderDetailInfo,
  OrderStatusHistory,
  ApiResponse,
  PageResponse
} from '../types/payment-order'

/**
 * 支付订单API类
 */
export class PaymentOrderApi {
  private baseUrl = '/api/payment/orders'

  /**
   * 获取支付订单列表
   * @param params 查询参数
   * @returns 支付订单分页列表
   */
  async getPaymentOrders(params?: PaymentOrderQueryParams): Promise<ApiResponse<PageResponse<PaymentOrder>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<PaymentOrder>>> = await axios.get(this.baseUrl, {
      params
    })
    return response.data
  }

  /**
   * 获取支付订单详情
   * @param id 订单ID
   * @returns 支付订单详情
   */
  async getPaymentOrder(id: string): Promise<ApiResponse<OrderDetailInfo>> {
    const response: AxiosResponse<ApiResponse<OrderDetailInfo>> = await axios.get(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 创建支付订单
   * @param data 订单数据
   * @returns 创建的支付订单
   */
  async createPaymentOrder(data: CreatePaymentOrderParams): Promise<ApiResponse<PaymentOrder>> {
    const response: AxiosResponse<ApiResponse<PaymentOrder>> = await axios.post(this.baseUrl, data)
    return response.data
  }

  /**
   * 更新支付订单
   * @param id 订单ID
   * @param data 订单数据
   * @returns 更新的支付订单
   */
  async updatePaymentOrder(id: string, data: UpdatePaymentOrderParams): Promise<ApiResponse<PaymentOrder>> {
    const response: AxiosResponse<ApiResponse<PaymentOrder>> = await axios.put(`${this.baseUrl}/${id}`, data)
    return response.data
  }

  /**
   * 取消支付订单
   * @param params 取消参数
   * @returns 操作结果
   */
  async cancelPaymentOrder(params: CancelOrderParams): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(
      `${this.baseUrl}/${params.orderId}/cancel`,
      params
    )
    return response.data
  }

  /**
   * 关闭支付订单
   * @param params 关闭参数
   * @returns 操作结果
   */
  async closePaymentOrder(params: OrderActionParams): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(
      `${this.baseUrl}/${params.orderId}/close`,
      params
    )
    return response.data
  }

  /**
   * 查询订单支付状态
   * @param id 订单ID
   * @returns 支付状态
   */
  async queryPaymentStatus(id: string): Promise<ApiResponse<{ status: string; paidAt?: string }>> {
    const response: AxiosResponse<ApiResponse<{ status: string; paidAt?: string }>> = await axios.get(
      `${this.baseUrl}/${id}/status`
    )
    return response.data
  }

  /**
   * 同步订单状态
   * @param id 订单ID
   * @returns 同步结果
   */
  async syncOrderStatus(id: string): Promise<ApiResponse<PaymentOrder>> {
    const response: AxiosResponse<ApiResponse<PaymentOrder>> = await axios.post(`${this.baseUrl}/${id}/sync`)
    return response.data
  }

  /**
   * 批量同步订单状态
   * @param orderIds 订单ID列表
   * @returns 同步结果
   */
  async batchSyncOrderStatus(orderIds: string[]): Promise<ApiResponse<PaymentOrder[]>> {
    const response: AxiosResponse<ApiResponse<PaymentOrder[]>> = await axios.post(`${this.baseUrl}/batch-sync`, {
      orderIds
    })
    return response.data
  }

  /**
   * 获取订单状态变更历史
   * @param id 订单ID
   * @returns 状态变更历史
   */
  async getOrderStatusHistory(id: string): Promise<ApiResponse<OrderStatusHistory[]>> {
    const response: AxiosResponse<ApiResponse<OrderStatusHistory[]>> = await axios.get(
      `${this.baseUrl}/${id}/status-history`
    )
    return response.data
  }

  /**
   * 导出支付订单
   * @param params 导出参数
   * @returns 导出文件URL
   */
  async exportPaymentOrders(params: OrderExportParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    const response: AxiosResponse<ApiResponse<{ downloadUrl: string }>> = await axios.post(
      `${this.baseUrl}/export`,
      params
    )
    return response.data
  }

  /**
   * 获取订单统计数据
   * @param dateRange 时间范围
   * @param filters 过滤条件
   * @returns 统计数据
   */
  async getOrderStatistics(
    dateRange: { start: string; end: string },
    filters?: Partial<PaymentOrderQueryParams>
  ): Promise<ApiResponse<OrderStatistics>> {
    const response: AxiosResponse<ApiResponse<OrderStatistics>> = await axios.get(`${this.baseUrl}/statistics`, {
      params: { ...dateRange, ...filters }
    })
    return response.data
  }

  /**
   * 获取订单趋势数据
   * @param dateRange 时间范围
   * @param granularity 粒度 (day/hour/month)
   * @param filters 过滤条件
   * @returns 趋势数据
   */
  async getOrderTrend(
    dateRange: { start: string; end: string },
    granularity: 'day' | 'hour' | 'month' = 'day',
    filters?: Partial<PaymentOrderQueryParams>
  ): Promise<ApiResponse<OrderTrendData[]>> {
    const response: AxiosResponse<ApiResponse<OrderTrendData[]>> = await axios.get(`${this.baseUrl}/trend`, {
      params: { ...dateRange, granularity, ...filters }
    })
    return response.data
  }

  /**
   * 获取热门支付方式统计
   * @param dateRange 时间范围
   * @returns 支付方式统计
   */
  async getPaymentMethodStats(
    dateRange: { start: string; end: string }
  ): Promise<ApiResponse<Array<{ method: string; count: number; amount: number }>>> {
    const response: AxiosResponse<ApiResponse<Array<{ method: string; count: number; amount: number }>>> =
      await axios.get(`${this.baseUrl}/payment-method-stats`, {
        params: dateRange
      })
    return response.data
  }

  /**
   * 获取支付渠道统计
   * @param dateRange 时间范围
   * @returns 支付渠道统计
   */
  async getChannelStats(
    dateRange: { start: string; end: string }
  ): Promise<ApiResponse<Array<{ channel: string; count: number; amount: number; successRate: number }>>> {
    const response: AxiosResponse<
      ApiResponse<Array<{ channel: string; count: number; amount: number; successRate: number }>>
    > = await axios.get(`${this.baseUrl}/channel-stats`, {
      params: dateRange
    })
    return response.data
  }

  /**
   * 获取订单金额分布
   * @param dateRange 时间范围
   * @returns 金额分布数据
   */
  async getAmountDistribution(
    dateRange: { start: string; end: string }
  ): Promise<ApiResponse<Array<{ range: string; count: number; percentage: number }>>> {
    const response: AxiosResponse<ApiResponse<Array<{ range: string; count: number; percentage: number }>>> =
      await axios.get(`${this.baseUrl}/amount-distribution`, {
        params: dateRange
      })
    return response.data
  }

  /**
   * 重新发起支付
   * @param id 订单ID
   * @returns 支付信息
   */
  async retryPayment(id: string): Promise<ApiResponse<{ paymentUrl?: string; qrCode?: string }>> {
    const response: AxiosResponse<ApiResponse<{ paymentUrl?: string; qrCode?: string }>> = await axios.post(
      `${this.baseUrl}/${id}/retry`
    )
    return response.data
  }

  /**
   * 获取支付二维码
   * @param id 订单ID
   * @returns 二维码信息
   */
  async getPaymentQrCode(id: string): Promise<ApiResponse<{ qrCode: string; expireAt: string }>> {
    const response: AxiosResponse<ApiResponse<{ qrCode: string; expireAt: string }>> = await axios.get(
      `${this.baseUrl}/${id}/qrcode`
    )
    return response.data
  }

  /**
   * 获取支付链接
   * @param id 订单ID
   * @returns 支付链接
   */
  async getPaymentUrl(id: string): Promise<ApiResponse<{ paymentUrl: string; expireAt: string }>> {
    const response: AxiosResponse<ApiResponse<{ paymentUrl: string; expireAt: string }>> = await axios.get(
      `${this.baseUrl}/${id}/payment-url`
    )
    return response.data
  }

  /**
   * 验证订单签名
   * @param id 订单ID
   * @param signature 签名
   * @returns 验证结果
   */
  async verifyOrderSignature(id: string, signature: string): Promise<ApiResponse<{ valid: boolean }>> {
    const response: AxiosResponse<ApiResponse<{ valid: boolean }>> = await axios.post(
      `${this.baseUrl}/${id}/verify-signature`,
      { signature }
    )
    return response.data
  }
}

// 导出API实例
export const paymentOrderApi = new PaymentOrderApi()
