<!--
  回调日志查看组件
  @description 展示回调日志和处理结果，支持实时监控和重试机制
-->
<template>
  <div class="callback-log-viewer">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <ApiOutlined />
          回调日志监控
        </h2>
        <p class="page-description">监控支付回调通知的处理状态和结果</p>
      </div>
      <div class="header-actions">
        <a-button @click="refreshLogs" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button @click="showRetryModal" :disabled="selectedRowKeys.length === 0">
          <RedoOutlined />
          批量重试
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总回调数"
              :value="statistics.totalCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <ApiOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功回调"
              :value="statistics.successCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="失败回调"
              :value="statistics.failedCount"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="回调类型">
            <a-select
              v-model:value="searchForm.type"
              placeholder="请选择类型"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="PAYMENT_NOTIFY">支付通知</a-select-option>
              <a-select-option value="REFUND_NOTIFY">退款通知</a-select-option>
              <a-select-option value="TRANSFER_NOTIFY">转账通知</a-select-option>
              <a-select-option value="BILL_NOTIFY">账单通知</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="支付渠道">
            <a-select
              v-model:value="searchForm.channelType"
              placeholder="请选择渠道"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="ALIPAY">支付宝</a-select-option>
              <a-select-option value="WECHAT">微信支付</a-select-option>
              <a-select-option value="UNIONPAY">银联支付</a-select-option>
              <a-select-option value="PAYPAL">PayPal</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="处理状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="PENDING">待处理</a-select-option>
              <a-select-option value="PROCESSING">处理中</a-select-option>
              <a-select-option value="SUCCESS">处理成功</a-select-option>
              <a-select-option value="FAILED">处理失败</a-select-option>
              <a-select-option value="IGNORED">已忽略</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="订单号">
            <a-input
              v-model:value="searchForm.orderNo"
              placeholder="请输入订单号"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 回调日志列表 -->
    <div class="callback-list-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="callbacks"
          :loading="loading"
          :pagination="paginationConfig"
          :row-selection="rowSelection"
          row-key="id"
          :scroll="{ x: 1800 }"
          @change="handleTableChange"
        >
          <!-- 回调信息 -->
          <template #callbackInfo="{ record }">
            <div class="callback-info">
              <div class="callback-type">
                <a-tag :color="getCallbackTypeColor(record.type)">
                  {{ getCallbackTypeText(record.type) }}
                </a-tag>
              </div>
              <div class="callback-url">{{ record.callbackUrl }}</div>
              <div class="callback-time">{{ formatTime(record.createdAt) }}</div>
            </div>
          </template>

          <!-- 关联订单 -->
          <template #orderInfo="{ record }">
            <div class="order-info">
              <div class="order-no">{{ record.orderNo }}</div>
              <div class="third-party-no" v-if="record.thirdPartyOrderNo">
                第三方: {{ record.thirdPartyOrderNo }}
              </div>
            </div>
          </template>

          <!-- 支付渠道 -->
          <template #channel="{ record }">
            <div class="channel-info">
              <a-avatar :size="24" :src="getChannelIcon(record.channelType)" style="margin-right: 8px">
                {{ getChannelText(record.channelType).charAt(0) }}
              </a-avatar>
              <span>{{ getChannelText(record.channelType) }}</span>
            </div>
          </template>

          <!-- 处理状态 -->
          <template #status="{ record }">
            <div class="status-info">
              <a-badge
                :status="getStatusBadge(record.status)"
                :text="getStatusText(record.status)"
              />
              <div class="retry-info" v-if="record.retryCount > 0">
                <a-tag color="orange" size="small">
                  重试 {{ record.retryCount }}/{{ record.maxRetryCount }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 响应信息 -->
          <template #response="{ record }">
            <div class="response-info">
              <div class="response-status">
                <a-tag
                  :color="getResponseStatusColor(record.responseStatus)"
                  v-if="record.responseStatus"
                >
                  {{ record.responseStatus }}
                </a-tag>
              </div>
              <div class="processing-time" v-if="record.processingTime">
                {{ record.processingTime }}ms
              </div>
              <div class="signature-status" v-if="record.signatureVerified !== undefined">
                <a-tag :color="record.signatureVerified ? 'green' : 'red'" size="small">
                  {{ record.signatureVerified ? '签名验证通过' : '签名验证失败' }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewCallback(record)">
                详情
              </a-button>
              <a-button
                v-if="canRetry(record)"
                type="link"
                size="small"
                @click="retryCallback(record)"
              >
                重试
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-if="canIgnore(record)"
                      @click="ignoreCallback(record)"
                    >
                      忽略
                    </a-menu-item>
                    <a-menu-item @click="copyCallbackUrl(record)">
                      复制URL
                    </a-menu-item>
                    <a-menu-item @click="viewRequestDetails(record)">
                      查看请求详情
                    </a-menu-item>
                    <a-menu-item @click="viewResponseDetails(record)">
                      查看响应详情
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 回调详情弹窗 -->
    <CallbackDetailModal
      v-model:visible="detailModalVisible"
      :callback-data="currentCallbackData"
    />

    <!-- 批量重试弹窗 -->
    <BatchRetryModal
      v-model:visible="retryModalVisible"
      :callback-ids="selectedRowKeys"
      @success="handleRetrySuccess"
    />

    <!-- 请求详情弹窗 -->
    <RequestDetailModal
      v-model:visible="requestModalVisible"
      :callback-data="currentCallbackData"
    />

    <!-- 响应详情弹窗 -->
    <ResponseDetailModal
      v-model:visible="responseModalVisible"
      :callback-data="currentCallbackData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ApiOutlined,
  ReloadOutlined,
  RedoOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { useCallbackStore } from '../../stores/callback'
import CallbackDetailModal from './CallbackDetailModal.vue'
import BatchRetryModal from './BatchRetryModal.vue'
import RequestDetailModal from './RequestDetailModal.vue'
import ResponseDetailModal from './ResponseDetailModal.vue'
import type { CallbackRecord, CallbackQueryParams } from '../../types/callback'
import { formatTime, copyToClipboard, getChannelIcon, getChannelText } from '../../utils/payment-utils'

// 状态管理
const callbackStore = useCallbackStore()
const { callbacks, loading, pagination, statistics } = storeToRefs(callbackStore)

// 响应式数据
const searchForm = reactive<CallbackQueryParams>({})
const selectedRowKeys = ref<string[]>([])
const detailModalVisible = ref(false)
const retryModalVisible = ref(false)
const requestModalVisible = ref(false)
const responseModalVisible = ref(false)
const currentCallbackData = ref<CallbackRecord | null>(null)

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 表格列配置
const columns = [
  {
    title: '回调信息',
    key: 'callbackInfo',
    slots: { customRender: 'callbackInfo' },
    width: 250,
    fixed: 'left'
  },
  {
    title: '关联订单',
    key: 'orderInfo',
    slots: { customRender: 'orderInfo' },
    width: 180
  },
  {
    title: '支付渠道',
    key: 'channel',
    slots: { customRender: 'channel' },
    width: 120
  },
  {
    title: '处理状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 150
  },
  {
    title: '响应信息',
    key: 'response',
    slots: { customRender: 'response' },
    width: 180
  },
  {
    title: '完成时间',
    dataIndex: 'completedAt',
    key: 'completedAt',
    width: 180,
    customRender: ({ text }) => text ? formatTime(text) : '-'
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: CallbackRecord) => ({
    disabled: !canRetry(record)
  })
}

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}))

// 工具方法
const getCallbackTypeColor = (type: string) => {
  const colors = {
    PAYMENT_NOTIFY: 'blue',
    REFUND_NOTIFY: 'orange',
    TRANSFER_NOTIFY: 'purple',
    BILL_NOTIFY: 'green',
    OTHER_NOTIFY: 'default'
  }
  return colors[type] || 'default'
}

const getCallbackTypeText = (type: string) => {
  const texts = {
    PAYMENT_NOTIFY: '支付通知',
    REFUND_NOTIFY: '退款通知',
    TRANSFER_NOTIFY: '转账通知',
    BILL_NOTIFY: '账单通知',
    OTHER_NOTIFY: '其他通知'
  }
  return texts[type] || type
}

const getStatusBadge = (status: string) => {
  const badges = {
    PENDING: 'processing',
    PROCESSING: 'processing',
    SUCCESS: 'success',
    FAILED: 'error',
    IGNORED: 'default',
    EXPIRED: 'warning'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    PENDING: '待处理',
    PROCESSING: '处理中',
    SUCCESS: '处理成功',
    FAILED: '处理失败',
    IGNORED: '已忽略',
    EXPIRED: '已过期'
  }
  return texts[status] || status
}

const getResponseStatusColor = (status: number) => {
  if (status >= 200 && status < 300) return 'green'
  if (status >= 400 && status < 500) return 'orange'
  if (status >= 500) return 'red'
  return 'default'
}

// 权限判断
const canRetry = (callback: CallbackRecord) => {
  return ['FAILED', 'EXPIRED'].includes(callback.status) && 
         callback.retryCount < callback.maxRetryCount
}

const canIgnore = (callback: CallbackRecord) => {
  return ['PENDING', 'FAILED'].includes(callback.status)
}

// 事件处理
const handleSearch = () => {
  callbackStore.setPagination(1, pagination.value.pageSize)
  fetchCallbacks()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    delete searchForm[key]
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  callbackStore.setPagination(pag.current, pag.pageSize)
  fetchCallbacks()
}

const refreshLogs = () => {
  fetchCallbacks()
}

const showRetryModal = () => {
  retryModalVisible.value = true
}

const viewCallback = (callback: CallbackRecord) => {
  currentCallbackData.value = callback
  detailModalVisible.value = true
}

const retryCallback = async (callback: CallbackRecord) => {
  try {
    await callbackStore.retryCallback(callback.id)
    message.success('重试回调成功')
  } catch (error) {
    message.error('重试回调失败')
  }
}

const ignoreCallback = (callback: CallbackRecord) => {
  Modal.confirm({
    title: '确认忽略回调',
    content: `确定要忽略回调"${callback.id}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await callbackStore.ignoreCallback(callback.id)
        message.success('忽略回调成功')
      } catch (error) {
        message.error('忽略回调失败')
      }
    }
  })
}

const copyCallbackUrl = (callback: CallbackRecord) => {
  copyToClipboard(callback.callbackUrl)
  message.success('回调URL已复制到剪贴板')
}

const viewRequestDetails = (callback: CallbackRecord) => {
  currentCallbackData.value = callback
  requestModalVisible.value = true
}

const viewResponseDetails = (callback: CallbackRecord) => {
  currentCallbackData.value = callback
  responseModalVisible.value = true
}

const handleRetrySuccess = () => {
  retryModalVisible.value = false
  selectedRowKeys.value = []
  fetchCallbacks()
}

const fetchCallbacks = () => {
  callbackStore.fetchCallbacks(searchForm)
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchCallbacks()
  }, 30000) // 30秒自动刷新
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  fetchCallbacks()
  callbackStore.fetchStatistics()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped lang="less">
.callback-log-viewer {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 16px;
  }
  
  .search-section {
    margin-bottom: 16px;
  }
  
  .callback-info {
    .callback-type {
      margin-bottom: 4px;
    }
    
    .callback-url {
      color: #595959;
      font-size: 12px;
      font-family: 'Monaco', 'Menlo', monospace;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .callback-time {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .order-info {
    .order-no {
      font-weight: 500;
      color: #262626;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
    }
    
    .third-party-no {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .channel-info {
    display: flex;
    align-items: center;
  }
  
  .status-info {
    .retry-info {
      margin-top: 4px;
    }
  }
  
  .response-info {
    .response-status {
      margin-bottom: 4px;
    }
    
    .processing-time {
      color: #8c8c8c;
      font-size: 12px;
      margin-bottom: 4px;
    }
    
    .signature-status {
      margin-top: 4px;
    }
  }
}
</style>
