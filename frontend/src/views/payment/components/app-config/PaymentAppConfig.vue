<!--
  支付应用配置组件
  @description 支持多支付渠道配置管理，包括支付宝、微信支付、银联、PayPal等
-->
<template>
  <div class="payment-app-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <PayCircleOutlined />
          支付应用配置
        </h2>
        <p class="page-description">管理支付应用和渠道配置，支持多种支付方式</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          新建应用
        </a-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="应用名称">
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入应用名称"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="应用状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="ENABLED">启用</a-select-option>
              <a-select-option value="DISABLED">禁用</a-select-option>
              <a-select-option value="MAINTENANCE">维护中</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="支付环境">
            <a-select
              v-model:value="searchForm.environment"
              placeholder="请选择环境"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="DEVELOPMENT">开发环境</a-select-option>
              <a-select-option value="TESTING">测试环境</a-select-option>
              <a-select-option value="STAGING">预生产</a-select-option>
              <a-select-option value="PRODUCTION">生产环境</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 应用列表 -->
    <div class="app-list-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="apps"
          :loading="loading"
          :pagination="paginationConfig"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 应用名称 -->
          <template #name="{ record }">
            <div class="app-name">
              <a-avatar :size="32" :src="record.icon" style="margin-right: 8px">
                {{ record.name.charAt(0) }}
              </a-avatar>
              <div>
                <div class="name">{{ record.name }}</div>
                <div class="description">{{ record.description }}</div>
              </div>
            </div>
          </template>

          <!-- 支付环境 -->
          <template #environment="{ record }">
            <a-tag :color="getEnvironmentColor(record.environment)">
              {{ getEnvironmentText(record.environment) }}
            </a-tag>
          </template>

          <!-- 支付渠道 -->
          <template #channels="{ record }">
            <div class="channels">
              <a-tag
                v-for="channel in record.channels.slice(0, 3)"
                :key="channel.id"
                :color="channel.enabled ? 'success' : 'default'"
                style="margin-bottom: 4px"
              >
                {{ getChannelText(channel.type) }}
              </a-tag>
              <a-tag v-if="record.channels.length > 3" color="blue">
                +{{ record.channels.length - 3 }}
              </a-tag>
            </div>
          </template>

          <!-- 应用状态 -->
          <template #status="{ record }">
            <a-badge
              :status="getStatusBadge(record.status)"
              :text="getStatusText(record.status)"
            />
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewApp(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="editApp(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="testApp(record)">
                测试
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="toggleAppStatus(record)">
                      {{ record.status === 'ENABLED' ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item @click="copyApp(record)">
                      复制配置
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteApp(record)" class="danger-item">
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑应用弹窗 -->
    <PaymentAppModal
      v-model:visible="modalVisible"
      :app-data="currentAppData"
      :mode="modalMode"
      @success="handleModalSuccess"
    />

    <!-- 应用详情抽屉 -->
    <PaymentAppDrawer
      v-model:visible="drawerVisible"
      :app-data="currentAppData"
    />

    <!-- 渠道测试弹窗 -->
    <ChannelTestModal
      v-model:visible="testModalVisible"
      :app-data="currentAppData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PayCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { usePaymentAppStore } from '../../stores/payment-app'
import PaymentAppModal from './PaymentAppModal.vue'
import PaymentAppDrawer from './PaymentAppDrawer.vue'
import ChannelTestModal from './ChannelTestModal.vue'
import type { PaymentAppConfig, PaymentAppQueryParams } from '../../types/payment-app'

// 状态管理
const paymentAppStore = usePaymentAppStore()
const { apps, loading, pagination } = storeToRefs(paymentAppStore)

// 响应式数据
const searchForm = reactive<PaymentAppQueryParams>({
  name: '',
  status: undefined,
  environment: undefined
})

const modalVisible = ref(false)
const drawerVisible = ref(false)
const testModalVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentAppData = ref<PaymentAppConfig | null>(null)

// 表格列配置
const columns = [
  {
    title: '应用名称',
    dataIndex: 'name',
    key: 'name',
    slots: { customRender: 'name' },
    width: 250
  },
  {
    title: '支付环境',
    dataIndex: 'environment',
    key: 'environment',
    slots: { customRender: 'environment' },
    width: 120
  },
  {
    title: '支付渠道',
    dataIndex: 'channels',
    key: 'channels',
    slots: { customRender: 'channels' },
    width: 200
  },
  {
    title: '应用状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 200,
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}))

// 工具方法
const getEnvironmentColor = (environment: string) => {
  const colors = {
    DEVELOPMENT: 'blue',
    TESTING: 'orange',
    STAGING: 'purple',
    PRODUCTION: 'green'
  }
  return colors[environment] || 'default'
}

const getEnvironmentText = (environment: string) => {
  const texts = {
    DEVELOPMENT: '开发环境',
    TESTING: '测试环境',
    STAGING: '预生产',
    PRODUCTION: '生产环境'
  }
  return texts[environment] || environment
}

const getChannelText = (type: string) => {
  const texts = {
    ALIPAY: '支付宝',
    WECHAT: '微信支付',
    UNIONPAY: '银联',
    PAYPAL: 'PayPal',
    STRIPE: 'Stripe',
    SQUARE: 'Square'
  }
  return texts[type] || type
}

const getStatusBadge = (status: string) => {
  const badges = {
    ENABLED: 'success',
    DISABLED: 'default',
    MAINTENANCE: 'warning',
    DELETED: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    ENABLED: '启用',
    DISABLED: '禁用',
    MAINTENANCE: '维护中',
    DELETED: '已删除'
  }
  return texts[status] || status
}

// 事件处理
const handleSearch = () => {
  paymentAppStore.setPagination(1, pagination.value.pageSize)
  fetchApps()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    status: undefined,
    environment: undefined
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  paymentAppStore.setPagination(pag.current, pag.pageSize)
  fetchApps()
}

const showCreateModal = () => {
  modalMode.value = 'create'
  currentAppData.value = null
  modalVisible.value = true
}

const viewApp = (app: PaymentAppConfig) => {
  currentAppData.value = app
  drawerVisible.value = true
}

const editApp = (app: PaymentAppConfig) => {
  modalMode.value = 'edit'
  currentAppData.value = app
  modalVisible.value = true
}

const testApp = (app: PaymentAppConfig) => {
  currentAppData.value = app
  testModalVisible.value = true
}

const toggleAppStatus = async (app: PaymentAppConfig) => {
  const newStatus = app.status === 'ENABLED' ? false : true
  await paymentAppStore.toggleApp(app.id, newStatus)
}

const copyApp = (app: PaymentAppConfig) => {
  modalMode.value = 'create'
  currentAppData.value = { ...app, name: `${app.name}_副本` }
  modalVisible.value = true
}

const deleteApp = (app: PaymentAppConfig) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除应用"${app.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await paymentAppStore.deleteApp(app.id)
    }
  })
}

const handleModalSuccess = () => {
  modalVisible.value = false
  fetchApps()
}

const fetchApps = () => {
  paymentAppStore.fetchApps(searchForm)
}

// 生命周期
onMounted(() => {
  fetchApps()
})
</script>

<style scoped lang="less">
.payment-app-config {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 16px;
  }
  
  .app-name {
    display: flex;
    align-items: center;
    
    .name {
      font-weight: 500;
      color: #262626;
    }
    
    .description {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }
  
  .channels {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .danger-item {
    color: #ff4d4f !important;
  }
}
</style>
