<!--
  订单状态跟踪组件
  @description 可视化展示订单状态流转过程，支持实时状态更新
-->
<template>
  <div class="order-status-tracker">
    <!-- 状态标签 -->
    <div class="status-badge">
      <a-badge
        :status="statusConfig.badge"
        :text="statusConfig.text"
        :color="statusConfig.color"
      />
      <div v-if="showProgress" class="status-progress">
        <a-progress
          :percent="statusConfig.progress"
          :status="statusConfig.progressStatus"
          :size="'small'"
          :show-info="false"
        />
      </div>
    </div>

    <!-- 详细状态信息 -->
    <div v-if="showDetail" class="status-detail">
      <div class="status-timeline">
        <a-timeline size="small">
          <a-timeline-item
            v-for="step in statusSteps"
            :key="step.status"
            :color="getStepColor(step)"
            :dot="getStepIcon(step)"
          >
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-time" v-if="step.time">
                {{ formatTime(step.time) }}
              </div>
              <div class="step-description" v-if="step.description">
                {{ step.description }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>

    <!-- 状态操作按钮 -->
    <div v-if="showActions" class="status-actions">
      <a-space size="small">
        <a-button
          v-if="canRetry"
          type="primary"
          size="small"
          @click="handleRetry"
          :loading="retryLoading"
        >
          <ReloadOutlined />
          重试支付
        </a-button>
        <a-button
          v-if="canCancel"
          size="small"
          @click="handleCancel"
        >
          <CloseOutlined />
          取消订单
        </a-button>
        <a-button
          v-if="canRefund"
          size="small"
          @click="handleRefund"
        >
          <RollbackOutlined />
          申请退款
        </a-button>
        <a-button
          v-if="canSync"
          size="small"
          @click="handleSync"
          :loading="syncLoading"
        >
          <SyncOutlined />
          同步状态
        </a-button>
      </a-space>
    </div>

    <!-- 状态变更历史 -->
    <div v-if="showHistory && statusHistory.length > 0" class="status-history">
      <a-collapse size="small">
        <a-collapse-panel key="history" header="状态变更历史">
          <div class="history-list">
            <div
              v-for="history in statusHistory"
              :key="history.id"
              class="history-item"
            >
              <div class="history-content">
                <div class="history-change">
                  <a-tag :color="getStatusColor(history.fromStatus)" size="small">
                    {{ getStatusText(history.fromStatus) }}
                  </a-tag>
                  <ArrowRightOutlined style="margin: 0 8px; color: #bfbfbf;" />
                  <a-tag :color="getStatusColor(history.toStatus)" size="small">
                    {{ getStatusText(history.toStatus) }}
                  </a-tag>
                </div>
                <div class="history-info">
                  <span class="history-time">{{ formatTime(history.changedAt) }}</span>
                  <span v-if="history.operatorName" class="history-operator">
                    操作人: {{ history.operatorName }}
                  </span>
                </div>
                <div v-if="history.reason" class="history-reason">
                  原因: {{ history.reason }}
                </div>
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  ReloadOutlined,
  CloseOutlined,
  RollbackOutlined,
  SyncOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import type { PaymentOrderStatus, OrderStatusHistory } from '../../types/payment-order'
import { formatTime } from '../../utils/payment-utils'

// 组件属性
interface Props {
  status: PaymentOrderStatus
  paidAt?: string
  createdAt?: string
  showDetail?: boolean
  showActions?: boolean
  showHistory?: boolean
  showProgress?: boolean
  statusHistory?: OrderStatusHistory[]
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  showDetail: false,
  showActions: false,
  showHistory: false,
  showProgress: false,
  statusHistory: () => []
})

// 组件事件
const emit = defineEmits<{
  retry: [orderId: string]
  cancel: [orderId: string]
  refund: [orderId: string]
  sync: [orderId: string]
}>()

// 响应式数据
const retryLoading = ref(false)
const syncLoading = ref(false)

// 状态配置
const statusConfig = computed(() => {
  const configs = {
    PENDING: {
      badge: 'processing',
      color: '#1890ff',
      text: '待支付',
      progress: 25,
      progressStatus: 'active'
    },
    PROCESSING: {
      badge: 'processing',
      color: '#722ed1',
      text: '处理中',
      progress: 50,
      progressStatus: 'active'
    },
    SUCCESS: {
      badge: 'success',
      color: '#52c41a',
      text: '支付成功',
      progress: 100,
      progressStatus: 'success'
    },
    FAILED: {
      badge: 'error',
      color: '#ff4d4f',
      text: '支付失败',
      progress: 100,
      progressStatus: 'exception'
    },
    CANCELLED: {
      badge: 'default',
      color: '#d9d9d9',
      text: '已取消',
      progress: 100,
      progressStatus: 'exception'
    },
    TIMEOUT: {
      badge: 'warning',
      color: '#faad14',
      text: '支付超时',
      progress: 100,
      progressStatus: 'exception'
    },
    PARTIAL_REFUNDED: {
      badge: 'warning',
      color: '#fa8c16',
      text: '部分退款',
      progress: 100,
      progressStatus: 'success'
    },
    FULL_REFUNDED: {
      badge: 'default',
      color: '#8c8c8c',
      text: '全额退款',
      progress: 100,
      progressStatus: 'success'
    }
  }
  return configs[props.status] || configs.PENDING
})

// 状态步骤
const statusSteps = computed(() => {
  const steps = [
    {
      status: 'CREATED',
      title: '订单创建',
      time: props.createdAt,
      description: '订单已创建，等待支付',
      completed: true
    },
    {
      status: 'PENDING',
      title: '等待支付',
      time: props.status === 'PENDING' ? new Date().toISOString() : undefined,
      description: '用户正在进行支付操作',
      completed: ['PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'TIMEOUT'].includes(props.status)
    },
    {
      status: 'PROCESSING',
      title: '支付处理中',
      time: props.status === 'PROCESSING' ? new Date().toISOString() : undefined,
      description: '支付系统正在处理支付请求',
      completed: ['PROCESSING', 'SUCCESS', 'FAILED'].includes(props.status)
    },
    {
      status: 'SUCCESS',
      title: '支付成功',
      time: props.paidAt,
      description: '支付已完成，订单处理成功',
      completed: props.status === 'SUCCESS'
    }
  ]

  // 根据当前状态添加特殊步骤
  if (props.status === 'FAILED') {
    steps.push({
      status: 'FAILED',
      title: '支付失败',
      time: new Date().toISOString(),
      description: '支付处理失败，请重试或联系客服',
      completed: true
    })
  } else if (props.status === 'CANCELLED') {
    steps.push({
      status: 'CANCELLED',
      title: '订单取消',
      time: new Date().toISOString(),
      description: '订单已被取消',
      completed: true
    })
  } else if (props.status === 'TIMEOUT') {
    steps.push({
      status: 'TIMEOUT',
      title: '支付超时',
      time: new Date().toISOString(),
      description: '支付超时，订单已关闭',
      completed: true
    })
  }

  return steps.filter(step => step.completed || step.status === props.status)
})

// 操作权限
const canRetry = computed(() => ['FAILED', 'TIMEOUT'].includes(props.status))
const canCancel = computed(() => ['PENDING', 'PROCESSING'].includes(props.status))
const canRefund = computed(() => ['SUCCESS', 'PARTIAL_REFUNDED'].includes(props.status))
const canSync = computed(() => ['PENDING', 'PROCESSING'].includes(props.status))

// 工具方法
const getStepColor = (step: any) => {
  if (step.completed) {
    return step.status === 'FAILED' ? 'red' : 'green'
  }
  return 'blue'
}

const getStepIcon = (step: any) => {
  if (step.completed) {
    if (step.status === 'FAILED') {
      return h(CloseCircleOutlined, { style: { color: '#ff4d4f' } })
    }
    return h(CheckCircleOutlined, { style: { color: '#52c41a' } })
  }
  if (step.status === props.status) {
    return h(LoadingOutlined, { style: { color: '#1890ff' } })
  }
  return h(ClockCircleOutlined, { style: { color: '#d9d9d9' } })
}

const getStatusColor = (status: string) => {
  const colors = {
    PENDING: 'blue',
    PROCESSING: 'purple',
    SUCCESS: 'green',
    FAILED: 'red',
    CANCELLED: 'default',
    TIMEOUT: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    PENDING: '待支付',
    PROCESSING: '处理中',
    SUCCESS: '支付成功',
    FAILED: '支付失败',
    CANCELLED: '已取消',
    TIMEOUT: '支付超时'
  }
  return texts[status] || status
}

// 事件处理
const handleRetry = async () => {
  if (!props.orderId) return
  retryLoading.value = true
  try {
    emit('retry', props.orderId)
  } finally {
    retryLoading.value = false
  }
}

const handleCancel = () => {
  if (!props.orderId) return
  emit('cancel', props.orderId)
}

const handleRefund = () => {
  if (!props.orderId) return
  emit('refund', props.orderId)
}

const handleSync = async () => {
  if (!props.orderId) return
  syncLoading.value = true
  try {
    emit('sync', props.orderId)
  } finally {
    syncLoading.value = false
  }
}
</script>

<style scoped lang="less">
.order-status-tracker {
  .status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .status-progress {
      flex: 1;
      min-width: 60px;
    }
  }
  
  .status-detail {
    margin-top: 16px;
    
    .status-timeline {
      .step-content {
        .step-title {
          font-weight: 500;
          color: #262626;
        }
        
        .step-time {
          color: #8c8c8c;
          font-size: 12px;
          margin-top: 2px;
        }
        
        .step-description {
          color: #595959;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
  
  .status-actions {
    margin-top: 12px;
  }
  
  .status-history {
    margin-top: 16px;
    
    .history-list {
      .history-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .history-content {
          .history-change {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
          }
          
          .history-info {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
          }
          
          .history-reason {
            font-size: 12px;
            color: #595959;
          }
        }
      }
    }
  }
}
</style>
