<!--
  支付订单列表组件
  @description 支持分页、排序、实时状态更新的支付订单管理界面
-->
<template>
  <div class="payment-order-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <CreditCardOutlined />
          支付订单管理
        </h2>
        <p class="page-description">管理和监控所有支付订单，支持实时状态跟踪</p>
      </div>
      <div class="header-actions">
        <a-button @click="refreshOrders" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          创建订单
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总订单数"
              :value="statistics.totalCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功订单"
              :value="statistics.successCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总金额"
              :value="statistics.totalAmount / 100"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <OrderSearchFilter
      v-model:filters="searchFilters"
      @search="handleSearch"
      @reset="handleResetSearch"
      @export="handleExport"
    />

    <!-- 订单列表 -->
    <div class="order-list-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="orders"
          :loading="loading"
          :pagination="paginationConfig"
          row-key="id"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
        >
          <!-- 订单信息 -->
          <template #orderInfo="{ record }">
            <div class="order-info">
              <div class="order-no">{{ record.merchantOrderNo }}</div>
              <div class="order-title">{{ record.title }}</div>
              <div class="order-time">{{ formatTime(record.createdAt) }}</div>
            </div>
          </template>

          <!-- 金额 -->
          <template #amount="{ record }">
            <div class="amount">
              <div class="amount-value">¥{{ (record.amount / 100).toFixed(2) }}</div>
              <div class="currency">{{ record.currency }}</div>
            </div>
          </template>

          <!-- 支付方式 -->
          <template #paymentMethod="{ record }">
            <div class="payment-method">
              <a-avatar :size="24" :src="getChannelIcon(record.channelType)" style="margin-right: 8px">
                {{ getChannelText(record.channelType).charAt(0) }}
              </a-avatar>
              <div>
                <div>{{ getChannelText(record.channelType) }}</div>
                <div class="method-detail">{{ getMethodText(record.paymentMethod) }}</div>
              </div>
            </div>
          </template>

          <!-- 订单状态 -->
          <template #status="{ record }">
            <OrderStatusTracker :status="record.status" :paid-at="record.paidAt" />
          </template>

          <!-- 用户信息 -->
          <template #userInfo="{ record }">
            <div class="user-info" v-if="record.userInfo">
              <a-avatar :size="32" :src="record.userInfo.avatar">
                {{ record.userInfo.nickname?.charAt(0) || 'U' }}
              </a-avatar>
              <div style="margin-left: 8px">
                <div>{{ record.userInfo.nickname || record.userInfo.username }}</div>
                <div class="user-contact">{{ record.userInfo.phone || record.userInfo.email }}</div>
              </div>
            </div>
            <span v-else class="no-user">未知用户</span>
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewOrder(record)">
                详情
              </a-button>
              <a-button
                v-if="record.status === 'PENDING'"
                type="link"
                size="small"
                @click="syncOrderStatus(record)"
              >
                同步状态
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-if="record.status === 'PENDING'"
                      @click="cancelOrder(record)"
                    >
                      取消订单
                    </a-menu-item>
                    <a-menu-item
                      v-if="record.status === 'SUCCESS'"
                      @click="createRefund(record)"
                    >
                      申请退款
                    </a-menu-item>
                    <a-menu-item @click="copyOrderNo(record)">
                      复制订单号
                    </a-menu-item>
                    <a-menu-item @click="viewOrderHistory(record)">
                      查看历史
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      v-model:visible="detailModalVisible"
      :order-data="currentOrderData"
    />

    <!-- 创建订单弹窗 -->
    <CreateOrderModal
      v-model:visible="createModalVisible"
      @success="handleCreateSuccess"
    />

    <!-- 订单历史弹窗 -->
    <OrderHistoryModal
      v-model:visible="historyModalVisible"
      :order-data="currentOrderData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  CreditCardOutlined,
  ReloadOutlined,
  PlusOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { usePaymentOrderStore } from '../../stores/payment-order'
import OrderSearchFilter from './OrderSearchFilter.vue'
import OrderStatusTracker from './OrderStatusTracker.vue'
import OrderDetailModal from './OrderDetailModal.vue'
import CreateOrderModal from './CreateOrderModal.vue'
import OrderHistoryModal from './OrderHistoryModal.vue'
import type { PaymentOrder, PaymentOrderQueryParams } from '../../types/payment-order'
import { formatTime, copyToClipboard } from '../../utils/payment-utils'

// 状态管理
const paymentOrderStore = usePaymentOrderStore()
const { orders, loading, pagination, statistics } = storeToRefs(paymentOrderStore)

// 响应式数据
const searchFilters = reactive<PaymentOrderQueryParams>({})
const detailModalVisible = ref(false)
const createModalVisible = ref(false)
const historyModalVisible = ref(false)
const currentOrderData = ref<PaymentOrder | null>(null)

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 表格列配置
const columns = [
  {
    title: '订单信息',
    key: 'orderInfo',
    slots: { customRender: 'orderInfo' },
    width: 200,
    fixed: 'left'
  },
  {
    title: '金额',
    key: 'amount',
    slots: { customRender: 'amount' },
    width: 120,
    sorter: true
  },
  {
    title: '支付方式',
    key: 'paymentMethod',
    slots: { customRender: 'paymentMethod' },
    width: 150
  },
  {
    title: '订单状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 150
  },
  {
    title: '用户信息',
    key: 'userInfo',
    slots: { customRender: 'userInfo' },
    width: 180
  },
  {
    title: '支付时间',
    dataIndex: 'paidAt',
    key: 'paidAt',
    width: 180,
    customRender: ({ text }) => text ? formatTime(text) : '-'
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}))

// 工具方法
const getChannelIcon = (channelType: string) => {
  const icons = {
    ALIPAY: '/icons/alipay.png',
    WECHAT: '/icons/wechat.png',
    UNIONPAY: '/icons/unionpay.png',
    PAYPAL: '/icons/paypal.png'
  }
  return icons[channelType]
}

const getChannelText = (channelType: string) => {
  const texts = {
    ALIPAY: '支付宝',
    WECHAT: '微信支付',
    UNIONPAY: '银联支付',
    PAYPAL: 'PayPal',
    STRIPE: 'Stripe',
    SQUARE: 'Square'
  }
  return texts[channelType] || channelType
}

const getMethodText = (method: string) => {
  const texts = {
    QR_CODE: '扫码支付',
    WEB: '网页支付',
    MOBILE: '手机支付',
    APP: 'APP支付',
    MINI_PROGRAM: '小程序支付',
    JSAPI: '公众号支付'
  }
  return texts[method] || method
}

// 事件处理
const handleSearch = () => {
  paymentOrderStore.setPagination(1, pagination.value.pageSize)
  fetchOrders()
}

const handleResetSearch = () => {
  Object.keys(searchFilters).forEach(key => {
    delete searchFilters[key]
  })
  handleSearch()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  paymentOrderStore.setPagination(pag.current, pag.pageSize)
  
  // 处理排序
  if (sorter.field) {
    searchFilters.sortBy = sorter.field
    searchFilters.sortOrder = sorter.order === 'ascend' ? 'ASC' : 'DESC'
  }
  
  fetchOrders()
}

const handleExport = async (params: any) => {
  try {
    await paymentOrderStore.exportOrders(params)
    message.success('导出任务已提交，请稍后下载')
  } catch (error) {
    message.error('导出失败')
  }
}

const refreshOrders = () => {
  fetchOrders()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const viewOrder = (order: PaymentOrder) => {
  currentOrderData.value = order
  detailModalVisible.value = true
}

const syncOrderStatus = async (order: PaymentOrder) => {
  try {
    await paymentOrderStore.syncOrderStatus(order.id)
    message.success('订单状态同步成功')
  } catch (error) {
    message.error('订单状态同步失败')
  }
}

const cancelOrder = (order: PaymentOrder) => {
  Modal.confirm({
    title: '确认取消订单',
    content: `确定要取消订单"${order.merchantOrderNo}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await paymentOrderStore.cancelOrder({
          orderId: order.id,
          cancelReason: '用户取消'
        })
        message.success('订单取消成功')
      } catch (error) {
        message.error('订单取消失败')
      }
    }
  })
}

const createRefund = (order: PaymentOrder) => {
  // 跳转到退款申请页面或打开退款弹窗
  message.info('跳转到退款申请页面')
}

const copyOrderNo = (order: PaymentOrder) => {
  copyToClipboard(order.merchantOrderNo)
  message.success('订单号已复制到剪贴板')
}

const viewOrderHistory = (order: PaymentOrder) => {
  currentOrderData.value = order
  historyModalVisible.value = true
}

const handleCreateSuccess = () => {
  createModalVisible.value = false
  fetchOrders()
}

const fetchOrders = () => {
  paymentOrderStore.fetchOrders(searchFilters)
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchOrders()
  }, 30000) // 30秒自动刷新
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  fetchOrders()
  paymentOrderStore.fetchStatistics()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped lang="less">
.payment-order-list {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 16px;
  }
  
  .order-info {
    .order-no {
      font-weight: 500;
      color: #262626;
      font-family: 'Monaco', 'Menlo', monospace;
    }
    
    .order-title {
      color: #595959;
      margin-top: 2px;
      font-size: 12px;
    }
    
    .order-time {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .amount {
    .amount-value {
      font-weight: 600;
      color: #262626;
      font-size: 16px;
    }
    
    .currency {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
  
  .payment-method {
    display: flex;
    align-items: center;
    
    .method-detail {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-contact {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .no-user {
    color: #bfbfbf;
    font-style: italic;
  }
}
</style>
