<!--
  退款订单列表组件
  @description 退款订单查看和管理，支持审批流程集成
-->
<template>
  <div class="refund-order-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <RollbackOutlined />
          退款订单管理
        </h2>
        <p class="page-description">管理退款申请和审批流程，支持部分退款和全额退款</p>
      </div>
      <div class="header-actions">
        <a-button @click="refreshRefunds" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          申请退款
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总退款申请"
              :value="statistics.totalCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <FileTextOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功退款"
              :value="statistics.successCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="退款金额"
              :value="statistics.successAmount / 100"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.successRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="退款单号">
            <a-input
              v-model:value="searchForm.refundNo"
              placeholder="请输入退款单号"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="原订单号">
            <a-input
              v-model:value="searchForm.paymentOrderNo"
              placeholder="请输入原订单号"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="退款状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option value="PENDING">待处理</a-select-option>
              <a-select-option value="APPROVED">已批准</a-select-option>
              <a-select-option value="PROCESSING">退款中</a-select-option>
              <a-select-option value="SUCCESS">退款成功</a-select-option>
              <a-select-option value="FAILED">退款失败</a-select-option>
              <a-select-option value="REJECTED">已拒绝</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="退款类型">
            <a-select
              v-model:value="searchForm.refundType"
              placeholder="请选择类型"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="FULL">全额退款</a-select-option>
              <a-select-option value="PARTIAL">部分退款</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 退款订单列表 -->
    <div class="refund-list-section">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="refunds"
          :loading="loading"
          :pagination="paginationConfig"
          row-key="id"
          :scroll="{ x: 1600 }"
          @change="handleTableChange"
        >
          <!-- 退款信息 -->
          <template #refundInfo="{ record }">
            <div class="refund-info">
              <div class="refund-no">{{ record.refundNo }}</div>
              <div class="refund-title">{{ record.title }}</div>
              <div class="refund-time">{{ formatTime(record.createdAt) }}</div>
            </div>
          </template>

          <!-- 原订单信息 -->
          <template #originalOrder="{ record }">
            <div class="original-order">
              <div class="order-no">{{ record.paymentOrderNo }}</div>
              <div class="order-amount">原金额: ¥{{ (record.originalAmount / 100).toFixed(2) }}</div>
            </div>
          </template>

          <!-- 退款金额 -->
          <template #refundAmount="{ record }">
            <div class="refund-amount">
              <div class="amount-value">¥{{ (record.refundAmount / 100).toFixed(2) }}</div>
              <div class="amount-type">
                <a-tag :color="record.refundType === 'FULL' ? 'blue' : 'orange'">
                  {{ record.refundType === 'FULL' ? '全额退款' : '部分退款' }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 退款原因 -->
          <template #refundReason="{ record }">
            <div class="refund-reason">
              <div class="reason-type">{{ getRefundReasonText(record.refundReason) }}</div>
              <div class="reason-desc" v-if="record.refundReasonDesc">
                {{ record.refundReasonDesc }}
              </div>
            </div>
          </template>

          <!-- 退款状态 -->
          <template #status="{ record }">
            <div class="refund-status">
              <a-badge
                :status="getRefundStatusBadge(record.status)"
                :text="getRefundStatusText(record.status)"
              />
              <div class="approval-status" v-if="record.approvalStatus !== record.status">
                <a-tag :color="getApprovalStatusColor(record.approvalStatus)" size="small">
                  {{ getApprovalStatusText(record.approvalStatus) }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 申请人 -->
          <template #applicant="{ record }">
            <div class="applicant-info">
              <a-avatar :size="24" style="margin-right: 8px">
                {{ record.applicantName.charAt(0) }}
              </a-avatar>
              <span>{{ record.applicantName }}</span>
            </div>
          </template>

          <!-- 审批人 -->
          <template #approver="{ record }">
            <div class="approver-info" v-if="record.approverName">
              <a-avatar :size="24" style="margin-right: 8px">
                {{ record.approverName.charAt(0) }}
              </a-avatar>
              <div>
                <div>{{ record.approverName }}</div>
                <div class="approve-time" v-if="record.approvedAt">
                  {{ formatTime(record.approvedAt) }}
                </div>
              </div>
            </div>
            <span v-else class="no-approver">待审批</span>
          </template>

          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewRefund(record)">
                详情
              </a-button>
              <a-button
                v-if="canApprove(record)"
                type="link"
                size="small"
                @click="approveRefund(record)"
              >
                审批
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-if="canCancel(record)"
                      @click="cancelRefund(record)"
                    >
                      取消申请
                    </a-menu-item>
                    <a-menu-item
                      v-if="canRetry(record)"
                      @click="retryRefund(record)"
                    >
                      重试退款
                    </a-menu-item>
                    <a-menu-item @click="copyRefundNo(record)">
                      复制退款单号
                    </a-menu-item>
                    <a-menu-item @click="viewRefundHistory(record)">
                      查看历史
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 退款详情弹窗 -->
    <RefundDetailModal
      v-model:visible="detailModalVisible"
      :refund-data="currentRefundData"
    />

    <!-- 创建退款申请弹窗 -->
    <CreateRefundModal
      v-model:visible="createModalVisible"
      @success="handleCreateSuccess"
    />

    <!-- 退款审批弹窗 -->
    <RefundApprovalModal
      v-model:visible="approvalModalVisible"
      :refund-data="currentRefundData"
      @success="handleApprovalSuccess"
    />

    <!-- 退款历史弹窗 -->
    <RefundHistoryModal
      v-model:visible="historyModalVisible"
      :refund-data="currentRefundData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  RollbackOutlined,
  ReloadOutlined,
  PlusOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { useRefundOrderStore } from '../../stores/refund-order'
import RefundDetailModal from './RefundDetailModal.vue'
import CreateRefundModal from './CreateRefundModal.vue'
import RefundApprovalModal from './RefundApprovalModal.vue'
import RefundHistoryModal from './RefundHistoryModal.vue'
import type { RefundOrder, RefundOrderQueryParams } from '../../types/refund-order'
import { formatTime, copyToClipboard } from '../../utils/payment-utils'

// 状态管理
const refundOrderStore = useRefundOrderStore()
const { refunds, loading, pagination, statistics } = storeToRefs(refundOrderStore)

// 响应式数据
const searchForm = reactive<RefundOrderQueryParams>({})
const detailModalVisible = ref(false)
const createModalVisible = ref(false)
const approvalModalVisible = ref(false)
const historyModalVisible = ref(false)
const currentRefundData = ref<RefundOrder | null>(null)

// 表格列配置
const columns = [
  {
    title: '退款信息',
    key: 'refundInfo',
    slots: { customRender: 'refundInfo' },
    width: 200,
    fixed: 'left'
  },
  {
    title: '原订单',
    key: 'originalOrder',
    slots: { customRender: 'originalOrder' },
    width: 180
  },
  {
    title: '退款金额',
    key: 'refundAmount',
    slots: { customRender: 'refundAmount' },
    width: 150,
    sorter: true
  },
  {
    title: '退款原因',
    key: 'refundReason',
    slots: { customRender: 'refundReason' },
    width: 200
  },
  {
    title: '退款状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 150
  },
  {
    title: '申请人',
    key: 'applicant',
    slots: { customRender: 'applicant' },
    width: 120
  },
  {
    title: '审批人',
    key: 'approver',
    slots: { customRender: 'approver' },
    width: 150
  },
  {
    title: '退款时间',
    dataIndex: 'refundedAt',
    key: 'refundedAt',
    width: 180,
    customRender: ({ text }) => text ? formatTime(text) : '-'
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}))

// 工具方法
const getRefundReasonText = (reason: string) => {
  const texts = {
    USER_REQUEST: '用户申请',
    QUALITY_ISSUE: '质量问题',
    OUT_OF_STOCK: '商品缺货',
    PRICE_ERROR: '价格错误',
    DUPLICATE_PAYMENT: '重复支付',
    SYSTEM_ERROR: '系统错误',
    RISK_CONTROL: '风控拦截',
    OTHER: '其他原因'
  }
  return texts[reason] || reason
}

const getRefundStatusBadge = (status: string) => {
  const badges = {
    PENDING: 'processing',
    APPROVED: 'success',
    PROCESSING: 'processing',
    SUCCESS: 'success',
    FAILED: 'error',
    REJECTED: 'error',
    CANCELLED: 'default'
  }
  return badges[status] || 'default'
}

const getRefundStatusText = (status: string) => {
  const texts = {
    PENDING: '待处理',
    APPROVED: '已批准',
    PROCESSING: '退款中',
    SUCCESS: '退款成功',
    FAILED: '退款失败',
    REJECTED: '已拒绝',
    CANCELLED: '已取消'
  }
  return texts[status] || status
}

const getApprovalStatusColor = (status: string) => {
  const colors = {
    PENDING: 'blue',
    IN_PROGRESS: 'purple',
    APPROVED: 'green',
    REJECTED: 'red',
    WITHDRAWN: 'default'
  }
  return colors[status] || 'default'
}

const getApprovalStatusText = (status: string) => {
  const texts = {
    PENDING: '待审批',
    IN_PROGRESS: '审批中',
    APPROVED: '已通过',
    REJECTED: '已拒绝',
    WITHDRAWN: '已撤回'
  }
  return texts[status] || status
}

// 权限判断
const canApprove = (refund: RefundOrder) => {
  return refund.status === 'PENDING' && refund.approvalStatus === 'PENDING'
}

const canCancel = (refund: RefundOrder) => {
  return ['PENDING', 'APPROVED'].includes(refund.status)
}

const canRetry = (refund: RefundOrder) => {
  return refund.status === 'FAILED'
}

// 事件处理
const handleSearch = () => {
  refundOrderStore.setPagination(1, pagination.value.pageSize)
  fetchRefunds()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    delete searchForm[key]
  })
  handleSearch()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  refundOrderStore.setPagination(pag.current, pag.pageSize)
  
  if (sorter.field) {
    searchForm.sortBy = sorter.field
    searchForm.sortOrder = sorter.order === 'ascend' ? 'ASC' : 'DESC'
  }
  
  fetchRefunds()
}

const refreshRefunds = () => {
  fetchRefunds()
}

const showCreateModal = () => {
  createModalVisible.value = true
}

const viewRefund = (refund: RefundOrder) => {
  currentRefundData.value = refund
  detailModalVisible.value = true
}

const approveRefund = (refund: RefundOrder) => {
  currentRefundData.value = refund
  approvalModalVisible.value = true
}

const cancelRefund = (refund: RefundOrder) => {
  Modal.confirm({
    title: '确认取消退款申请',
    content: `确定要取消退款申请"${refund.refundNo}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await refundOrderStore.cancelRefund(refund.id)
        message.success('取消退款申请成功')
      } catch (error) {
        message.error('取消退款申请失败')
      }
    }
  })
}

const retryRefund = async (refund: RefundOrder) => {
  try {
    await refundOrderStore.retryRefund(refund.id)
    message.success('重试退款成功')
  } catch (error) {
    message.error('重试退款失败')
  }
}

const copyRefundNo = (refund: RefundOrder) => {
  copyToClipboard(refund.refundNo)
  message.success('退款单号已复制到剪贴板')
}

const viewRefundHistory = (refund: RefundOrder) => {
  currentRefundData.value = refund
  historyModalVisible.value = true
}

const handleCreateSuccess = () => {
  createModalVisible.value = false
  fetchRefunds()
}

const handleApprovalSuccess = () => {
  approvalModalVisible.value = false
  fetchRefunds()
}

const fetchRefunds = () => {
  refundOrderStore.fetchRefunds(searchForm)
}

// 生命周期
onMounted(() => {
  fetchRefunds()
  refundOrderStore.fetchStatistics()
})
</script>

<style scoped lang="less">
.refund-order-list {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 16px;
  }
  
  .search-section {
    margin-bottom: 16px;
  }
  
  .refund-info {
    .refund-no {
      font-weight: 500;
      color: #262626;
      font-family: 'Monaco', 'Menlo', monospace;
    }
    
    .refund-title {
      color: #595959;
      margin-top: 2px;
      font-size: 12px;
    }
    
    .refund-time {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .original-order {
    .order-no {
      font-weight: 500;
      color: #262626;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
    }
    
    .order-amount {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .refund-amount {
    .amount-value {
      font-weight: 600;
      color: #262626;
      font-size: 16px;
    }
    
    .amount-type {
      margin-top: 4px;
    }
  }
  
  .refund-reason {
    .reason-type {
      font-weight: 500;
      color: #262626;
    }
    
    .reason-desc {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .refund-status {
    .approval-status {
      margin-top: 4px;
    }
  }
  
  .applicant-info,
  .approver-info {
    display: flex;
    align-items: center;
    
    .approve-time {
      color: #8c8c8c;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .no-approver {
    color: #bfbfbf;
    font-style: italic;
  }
}
</style>
