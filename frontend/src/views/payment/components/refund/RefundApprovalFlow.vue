<!--
  退款审批流程组件
  @description 集成现有工作流引擎进行退款审批，支持多级审批和条件审批
-->
<template>
  <div class="refund-approval-flow">
    <!-- 审批流程头部 -->
    <div class="flow-header">
      <div class="flow-info">
        <h3 class="flow-title">
          <AuditOutlined />
          退款审批流程
        </h3>
        <div class="flow-meta">
          <a-tag :color="getFlowStatusColor(flowData.status)">
            {{ getFlowStatusText(flowData.status) }}
          </a-tag>
          <span class="flow-time">
            创建时间: {{ formatTime(flowData.createdAt) }}
          </span>
          <span v-if="flowData.completedAt" class="flow-time">
            完成时间: {{ formatTime(flowData.completedAt) }}
          </span>
        </div>
      </div>
      <div class="flow-actions" v-if="showActions">
        <a-space>
          <a-button
            v-if="canWithdraw"
            @click="withdrawFlow"
            :loading="withdrawLoading"
          >
            <RollbackOutlined />
            撤回申请
          </a-button>
          <a-button
            v-if="canTerminate"
            danger
            @click="terminateFlow"
            :loading="terminateLoading"
          >
            <StopOutlined />
            终止流程
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 退款信息卡片 -->
    <div class="refund-info-card">
      <a-card title="退款申请信息" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="info-item">
              <span class="label">退款单号:</span>
              <span class="value">{{ refundData.refundNo }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">原订单号:</span>
              <span class="value">{{ refundData.paymentOrderNo }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">退款类型:</span>
              <a-tag :color="refundData.refundType === 'FULL' ? 'blue' : 'orange'">
                {{ refundData.refundType === 'FULL' ? '全额退款' : '部分退款' }}
              </a-tag>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">原订单金额:</span>
              <span class="value amount">¥{{ (refundData.originalAmount / 100).toFixed(2) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">申请退款金额:</span>
              <span class="value amount highlight">¥{{ (refundData.refundAmount / 100).toFixed(2) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">申请人:</span>
              <span class="value">{{ refundData.applicantName }}</span>
            </div>
          </a-col>
          <a-col :span="24">
            <div class="info-item">
              <span class="label">退款原因:</span>
              <span class="value">{{ getRefundReasonText(refundData.refundReason) }}</span>
            </div>
          </a-col>
          <a-col :span="24" v-if="refundData.refundReasonDesc">
            <div class="info-item">
              <span class="label">详细说明:</span>
              <span class="value">{{ refundData.refundReasonDesc }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 审批流程步骤 -->
    <div class="approval-steps">
      <a-card title="审批进度" size="small">
        <a-steps
          :current="currentStepIndex"
          :status="getStepsStatus()"
          direction="vertical"
          size="small"
        >
          <a-step
            v-for="(node, index) in flowData.nodes"
            :key="node.id"
            :title="node.name"
            :description="getStepDescription(node)"
          >
            <template #icon>
              <component :is="getStepIcon(node)" />
            </template>
            <template #subTitle>
              <div class="step-subtitle">
                <span v-if="node.approverName" class="approver">
                  审批人: {{ node.approverName }}
                </span>
                <span v-if="node.approvedAt" class="approve-time">
                  {{ formatTime(node.approvedAt) }}
                </span>
              </div>
            </template>
          </a-step>
        </a-steps>
      </a-card>
    </div>

    <!-- 当前待审批节点 -->
    <div v-if="currentApprovalNode" class="current-approval">
      <a-card title="待审批" size="small">
        <div class="approval-form">
          <a-form
            :model="approvalForm"
            layout="vertical"
            @finish="handleApproval"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item
                  label="审批结果"
                  name="approved"
                  :rules="[{ required: true, message: '请选择审批结果' }]"
                >
                  <a-radio-group v-model:value="approvalForm.approved">
                    <a-radio :value="true">
                      <CheckCircleOutlined style="color: #52c41a" />
                      通过
                    </a-radio>
                    <a-radio :value="false">
                      <CloseCircleOutlined style="color: #ff4d4f" />
                      拒绝
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="12" v-if="approvalForm.approved">
                <a-form-item label="实际退款金额" name="actualRefundAmount">
                  <a-input-number
                    v-model:value="approvalForm.actualRefundAmount"
                    :min="0"
                    :max="refundData.refundAmount / 100"
                    :precision="2"
                    style="width: 100%"
                    placeholder="可调整退款金额"
                  >
                    <template #addonBefore>¥</template>
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="审批意见"
                  name="comment"
                  :rules="[{ required: true, message: '请填写审批意见' }]"
                >
                  <a-textarea
                    v-model:value="approvalForm.comment"
                    :rows="3"
                    placeholder="请填写审批意见"
                    show-count
                    :maxlength="500"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button
                      type="primary"
                      html-type="submit"
                      :loading="approvalLoading"
                    >
                      提交审批
                    </a-button>
                    <a-button @click="resetApprovalForm">
                      重置
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
    </div>

    <!-- 审批历史 -->
    <div class="approval-history">
      <a-card title="审批历史" size="small">
        <a-timeline>
          <a-timeline-item
            v-for="node in completedNodes"
            :key="node.id"
            :color="getNodeTimelineColor(node)"
          >
            <template #dot>
              <component :is="getNodeIcon(node)" />
            </template>
            <div class="history-item">
              <div class="history-header">
                <span class="node-name">{{ node.name }}</span>
                <span class="node-status">
                  <a-tag :color="getNodeStatusColor(node.status)">
                    {{ getNodeStatusText(node.status) }}
                  </a-tag>
                </span>
                <span class="node-time">{{ formatTime(node.approvedAt) }}</span>
              </div>
              <div class="history-content">
                <div v-if="node.approverName" class="approver-info">
                  <UserOutlined />
                  审批人: {{ node.approverName }}
                </div>
                <div v-if="node.comment" class="approval-comment">
                  <MessageOutlined />
                  审批意见: {{ node.comment }}
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  AuditOutlined,
  RollbackOutlined,
  StopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  MessageOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import type {
  RefundOrder,
  RefundApprovalFlow,
  RefundApprovalNode,
  RefundApprovalParams
} from '../../types/refund-order'
import { useRefundOrderStore } from '../../stores/refund-order'
import { formatTime } from '../../utils/payment-utils'

// 组件属性
interface Props {
  refundData: RefundOrder
  flowData: RefundApprovalFlow
  showActions?: boolean
  currentUserId?: string
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

// 组件事件
const emit = defineEmits<{
  success: []
  withdraw: []
  terminate: []
}>()

// 状态管理
const refundOrderStore = useRefundOrderStore()

// 响应式数据
const approvalLoading = ref(false)
const withdrawLoading = ref(false)
const terminateLoading = ref(false)

const approvalForm = reactive<RefundApprovalParams>({
  refundOrderId: props.refundData.id,
  approved: true,
  comment: '',
  approverId: props.currentUserId || '',
  actualRefundAmount: undefined
})

// 计算属性
const currentStepIndex = computed(() => {
  const currentNode = props.flowData.currentNode
  if (!currentNode) return props.flowData.nodes.length
  return props.flowData.nodes.findIndex(node => node.id === currentNode.id)
})

const currentApprovalNode = computed(() => {
  return props.flowData.currentNode?.status === 'PENDING' ? props.flowData.currentNode : null
})

const completedNodes = computed(() => {
  return props.flowData.nodes.filter(node => 
    node.status !== 'PENDING' && node.approvedAt
  )
})

const canWithdraw = computed(() => {
  return props.flowData.status === 'PENDING' && 
         props.refundData.applicantId === props.currentUserId
})

const canTerminate = computed(() => {
  // 只有管理员可以终止流程
  return props.flowData.status === 'IN_PROGRESS'
})

// 工具方法
const getRefundReasonText = (reason: string) => {
  const texts = {
    USER_REQUEST: '用户申请',
    QUALITY_ISSUE: '质量问题',
    OUT_OF_STOCK: '商品缺货',
    PRICE_ERROR: '价格错误',
    DUPLICATE_PAYMENT: '重复支付',
    SYSTEM_ERROR: '系统错误',
    RISK_CONTROL: '风控拦截',
    OTHER: '其他原因'
  }
  return texts[reason] || reason
}

const getFlowStatusColor = (status: string) => {
  const colors = {
    PENDING: 'blue',
    IN_PROGRESS: 'purple',
    APPROVED: 'green',
    REJECTED: 'red',
    WITHDRAWN: 'default'
  }
  return colors[status] || 'default'
}

const getFlowStatusText = (status: string) => {
  const texts = {
    PENDING: '待审批',
    IN_PROGRESS: '审批中',
    APPROVED: '已通过',
    REJECTED: '已拒绝',
    WITHDRAWN: '已撤回'
  }
  return texts[status] || status
}

const getStepsStatus = () => {
  if (props.flowData.status === 'REJECTED') return 'error'
  if (props.flowData.status === 'APPROVED') return 'finish'
  return 'process'
}

const getStepDescription = (node: RefundApprovalNode) => {
  if (node.status === 'PENDING') {
    return '等待审批'
  }
  if (node.comment) {
    return node.comment
  }
  return getNodeStatusText(node.status)
}

const getStepIcon = (node: RefundApprovalNode) => {
  if (node.status === 'APPROVED') return CheckCircleOutlined
  if (node.status === 'REJECTED') return CloseCircleOutlined
  if (node.status === 'PENDING') return PlayCircleOutlined
  return ExclamationCircleOutlined
}

const getNodeTimelineColor = (node: RefundApprovalNode) => {
  if (node.status === 'APPROVED') return 'green'
  if (node.status === 'REJECTED') return 'red'
  return 'blue'
}

const getNodeIcon = (node: RefundApprovalNode) => {
  if (node.status === 'APPROVED') return CheckCircleOutlined
  if (node.status === 'REJECTED') return CloseCircleOutlined
  return UserOutlined
}

const getNodeStatusColor = (status: string) => {
  const colors = {
    PENDING: 'blue',
    APPROVED: 'green',
    REJECTED: 'red'
  }
  return colors[status] || 'default'
}

const getNodeStatusText = (status: string) => {
  const texts = {
    PENDING: '待审批',
    APPROVED: '已通过',
    REJECTED: '已拒绝'
  }
  return texts[status] || status
}

// 事件处理
const handleApproval = async () => {
  try {
    approvalLoading.value = true
    
    // 转换金额单位
    if (approvalForm.actualRefundAmount) {
      approvalForm.actualRefundAmount = Math.round(approvalForm.actualRefundAmount * 100)
    }
    
    await refundOrderStore.approveRefund(approvalForm)
    message.success('审批提交成功')
    emit('success')
  } catch (error) {
    message.error('审批提交失败')
  } finally {
    approvalLoading.value = false
  }
}

const resetApprovalForm = () => {
  Object.assign(approvalForm, {
    approved: true,
    comment: '',
    actualRefundAmount: undefined
  })
}

const withdrawFlow = () => {
  Modal.confirm({
    title: '确认撤回申请',
    content: '确定要撤回退款申请吗？撤回后需要重新提交审批。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        withdrawLoading.value = true
        await refundOrderStore.withdrawRefund(props.refundData.id)
        message.success('撤回申请成功')
        emit('withdraw')
      } catch (error) {
        message.error('撤回申请失败')
      } finally {
        withdrawLoading.value = false
      }
    }
  })
}

const terminateFlow = () => {
  Modal.confirm({
    title: '确认终止流程',
    content: '确定要终止审批流程吗？此操作不可恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        terminateLoading.value = true
        await refundOrderStore.terminateRefund(props.refundData.id)
        message.success('终止流程成功')
        emit('terminate')
      } catch (error) {
        message.error('终止流程失败')
      } finally {
        terminateLoading.value = false
      }
    }
  })
}
</script>

<style scoped lang="less">
.refund-approval-flow {
  .flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .flow-info {
      .flow-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .flow-meta {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 16px;
        
        .flow-time {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
    }
  }
  
  .refund-info-card {
    margin-bottom: 16px;
    
    .info-item {
      margin-bottom: 8px;
      
      .label {
        color: #8c8c8c;
        margin-right: 8px;
      }
      
      .value {
        color: #262626;
        
        &.amount {
          font-weight: 600;
          
          &.highlight {
            color: #fa8c16;
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .approval-steps {
    margin-bottom: 16px;
    
    .step-subtitle {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .approver {
        color: #595959;
        font-size: 12px;
      }
      
      .approve-time {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
  
  .current-approval {
    margin-bottom: 16px;
    
    .approval-form {
      background: #fafafa;
      padding: 16px;
      border-radius: 6px;
    }
  }
  
  .approval-history {
    .history-item {
      .history-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        .node-name {
          font-weight: 500;
          color: #262626;
        }
        
        .node-time {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
      
      .history-content {
        .approver-info,
        .approval-comment {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          margin-bottom: 4px;
          color: #595959;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
