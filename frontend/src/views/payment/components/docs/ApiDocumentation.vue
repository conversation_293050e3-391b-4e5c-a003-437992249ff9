<!--
  API文档组件
  @description 提供交互式API文档，支持在线测试和代码示例
-->
<template>
  <div class="api-documentation">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <BookOutlined />
          支付API文档
        </h2>
        <p class="page-description">完整的支付API接口文档和使用示例</p>
      </div>
      <div class="header-actions">
        <a-button @click="exportApiDoc">
          <DownloadOutlined />
          导出文档
        </a-button>
        <a-button type="primary" @click="showApiTester">
          <ExperimentOutlined />
          API测试
        </a-button>
      </div>
    </div>

    <!-- 文档导航 -->
    <div class="doc-navigation">
      <a-card :bordered="false">
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="horizontal"
          @click="handleMenuClick"
        >
          <a-menu-item key="overview">
            <FileTextOutlined />
            概览
          </a-menu-item>
          <a-menu-item key="authentication">
            <SafetyOutlined />
            认证授权
          </a-menu-item>
          <a-menu-item key="payment">
            <CreditCardOutlined />
            支付接口
          </a-menu-item>
          <a-menu-item key="refund">
            <RollbackOutlined />
            退款接口
          </a-menu-item>
          <a-menu-item key="callback">
            <ApiOutlined />
            回调通知
          </a-menu-item>
          <a-menu-item key="errors">
            <ExclamationCircleOutlined />
            错误码
          </a-menu-item>
          <a-menu-item key="sdk">
            <CodeOutlined />
            SDK示例
          </a-menu-item>
        </a-menu>
      </a-card>
    </div>

    <!-- 文档内容 -->
    <div class="doc-content">
      <!-- 概览 -->
      <div v-if="activeSection === 'overview'" class="doc-section">
        <a-card title="API概览" :bordered="false">
          <div class="overview-content">
            <h3>接口说明</h3>
            <p>支付系统提供完整的RESTful API接口，支持支付、退款、查询等核心功能。</p>
            
            <h3>基础信息</h3>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="协议">HTTPS</a-descriptions-item>
              <a-descriptions-item label="请求格式">JSON</a-descriptions-item>
              <a-descriptions-item label="响应格式">JSON</a-descriptions-item>
              <a-descriptions-item label="字符编码">UTF-8</a-descriptions-item>
              <a-descriptions-item label="API版本">v1</a-descriptions-item>
              <a-descriptions-item label="基础URL">https://api.example.com/payment/v1</a-descriptions-item>
            </a-descriptions>

            <h3>请求头</h3>
            <a-table
              :columns="headerColumns"
              :data-source="requestHeaders"
              :pagination="false"
              size="small"
            />

            <h3>响应格式</h3>
            <CodeBlock
              language="json"
              :code="responseFormatExample"
              title="标准响应格式"
            />
          </div>
        </a-card>
      </div>

      <!-- 认证授权 -->
      <div v-if="activeSection === 'authentication'" class="doc-section">
        <a-card title="认证授权" :bordered="false">
          <div class="auth-content">
            <h3>OAuth 2.0认证</h3>
            <p>API使用OAuth 2.0进行身份认证，支持多种授权模式。</p>

            <h4>1. 获取访问令牌</h4>
            <ApiEndpoint
              method="POST"
              path="/oauth/token"
              title="获取访问令牌"
              :parameters="tokenParameters"
              :response="tokenResponse"
            />

            <h4>2. 使用访问令牌</h4>
            <p>在请求头中添加Authorization字段：</p>
            <CodeBlock
              language="http"
              :code="authHeaderExample"
              title="请求头示例"
            />

            <h4>3. 刷新令牌</h4>
            <ApiEndpoint
              method="POST"
              path="/oauth/refresh"
              title="刷新访问令牌"
              :parameters="refreshParameters"
              :response="tokenResponse"
            />
          </div>
        </a-card>
      </div>

      <!-- 支付接口 -->
      <div v-if="activeSection === 'payment'" class="doc-section">
        <a-card title="支付接口" :bordered="false">
          <div class="payment-apis">
            <h3>支付相关接口</h3>
            
            <ApiEndpoint
              method="POST"
              path="/orders"
              title="创建支付订单"
              description="创建新的支付订单"
              :parameters="createOrderParameters"
              :response="createOrderResponse"
              :examples="createOrderExamples"
            />

            <ApiEndpoint
              method="GET"
              path="/orders/{orderId}"
              title="查询支付订单"
              description="根据订单ID查询支付订单详情"
              :parameters="queryOrderParameters"
              :response="queryOrderResponse"
            />

            <ApiEndpoint
              method="POST"
              path="/orders/{orderId}/cancel"
              title="取消支付订单"
              description="取消待支付的订单"
              :parameters="cancelOrderParameters"
              :response="cancelOrderResponse"
            />

            <ApiEndpoint
              method="POST"
              path="/orders/{orderId}/sync"
              title="同步订单状态"
              description="主动同步订单支付状态"
              :parameters="syncOrderParameters"
              :response="syncOrderResponse"
            />
          </div>
        </a-card>
      </div>

      <!-- 退款接口 -->
      <div v-if="activeSection === 'refund'" class="doc-section">
        <a-card title="退款接口" :bordered="false">
          <div class="refund-apis">
            <h3>退款相关接口</h3>
            
            <ApiEndpoint
              method="POST"
              path="/refunds"
              title="申请退款"
              description="创建退款申请"
              :parameters="createRefundParameters"
              :response="createRefundResponse"
            />

            <ApiEndpoint
              method="GET"
              path="/refunds/{refundId}"
              title="查询退款状态"
              description="根据退款ID查询退款详情"
              :parameters="queryRefundParameters"
              :response="queryRefundResponse"
            />

            <ApiEndpoint
              method="POST"
              path="/refunds/{refundId}/approve"
              title="审批退款"
              description="审批退款申请"
              :parameters="approveRefundParameters"
              :response="approveRefundResponse"
            />
          </div>
        </a-card>
      </div>

      <!-- 回调通知 -->
      <div v-if="activeSection === 'callback'" class="doc-section">
        <a-card title="回调通知" :bordered="false">
          <div class="callback-content">
            <h3>回调机制</h3>
            <p>支付系统通过HTTP POST方式向商户系统发送异步通知。</p>

            <h4>回调配置</h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="通知方式">HTTP POST</a-descriptions-item>
              <a-descriptions-item label="通知格式">JSON</a-descriptions-item>
              <a-descriptions-item label="重试机制">指数退避，最多重试5次</a-descriptions-item>
              <a-descriptions-item label="超时时间">30秒</a-descriptions-item>
              <a-descriptions-item label="签名算法">HMAC-SHA256</a-descriptions-item>
            </a-descriptions>

            <h4>支付结果通知</h4>
            <CodeBlock
              language="json"
              :code="paymentNotifyExample"
              title="支付通知示例"
            />

            <h4>退款结果通知</h4>
            <CodeBlock
              language="json"
              :code="refundNotifyExample"
              title="退款通知示例"
            />

            <h4>签名验证</h4>
            <p>为确保通知的安全性，所有回调通知都包含签名信息。</p>
            <CodeBlock
              language="javascript"
              :code="signatureVerifyExample"
              title="签名验证示例"
            />
          </div>
        </a-card>
      </div>

      <!-- 错误码 -->
      <div v-if="activeSection === 'errors'" class="doc-section">
        <a-card title="错误码说明" :bordered="false">
          <div class="error-codes">
            <h3>HTTP状态码</h3>
            <a-table
              :columns="httpStatusColumns"
              :data-source="httpStatusCodes"
              :pagination="false"
              size="small"
            />

            <h3>业务错误码</h3>
            <a-table
              :columns="errorCodeColumns"
              :data-source="businessErrorCodes"
              :pagination="false"
              size="small"
            />
          </div>
        </a-card>
      </div>

      <!-- SDK示例 -->
      <div v-if="activeSection === 'sdk'" class="doc-section">
        <a-card title="SDK示例" :bordered="false">
          <div class="sdk-examples">
            <h3>多语言SDK</h3>
            <a-tabs v-model:activeKey="activeSdkTab">
              <a-tab-pane key="java" tab="Java">
                <CodeBlock
                  language="java"
                  :code="javaExample"
                  title="Java SDK示例"
                />
              </a-tab-pane>
              <a-tab-pane key="php" tab="PHP">
                <CodeBlock
                  language="php"
                  :code="phpExample"
                  title="PHP SDK示例"
                />
              </a-tab-pane>
              <a-tab-pane key="python" tab="Python">
                <CodeBlock
                  language="python"
                  :code="pythonExample"
                  title="Python SDK示例"
                />
              </a-tab-pane>
              <a-tab-pane key="nodejs" tab="Node.js">
                <CodeBlock
                  language="javascript"
                  :code="nodejsExample"
                  title="Node.js SDK示例"
                />
              </a-tab-pane>
            </a-tabs>
          </div>
        </a-card>
      </div>
    </div>

    <!-- API测试器弹窗 -->
    <ApiTesterModal
      v-model:visible="testerModalVisible"
      :api-endpoints="allApiEndpoints"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  BookOutlined,
  DownloadOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  SafetyOutlined,
  CreditCardOutlined,
  RollbackOutlined,
  ApiOutlined,
  ExclamationCircleOutlined,
  CodeOutlined
} from '@ant-design/icons-vue'
import CodeBlock from './CodeBlock.vue'
import ApiEndpoint from './ApiEndpoint.vue'
import ApiTesterModal from './ApiTesterModal.vue'

// 响应式数据
const selectedKeys = ref(['overview'])
const activeSection = ref('overview')
const activeSdkTab = ref('java')
const testerModalVisible = ref(false)

// 表格列配置
const headerColumns = [
  { title: '参数名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '必填', dataIndex: 'required', key: 'required' },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

const httpStatusColumns = [
  { title: '状态码', dataIndex: 'code', key: 'code' },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

const errorCodeColumns = [
  { title: '错误码', dataIndex: 'code', key: 'code' },
  { title: '错误信息', dataIndex: 'message', key: 'message' },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

// 数据定义
const requestHeaders = [
  { name: 'Content-Type', type: 'string', required: '是', description: 'application/json' },
  { name: 'Authorization', type: 'string', required: '是', description: 'Bearer {access_token}' },
  { name: 'X-Request-ID', type: 'string', required: '否', description: '请求唯一标识' }
]

const httpStatusCodes = [
  { code: '200', description: '请求成功' },
  { code: '400', description: '请求参数错误' },
  { code: '401', description: '未授权' },
  { code: '403', description: '禁止访问' },
  { code: '404', description: '资源不存在' },
  { code: '500', description: '服务器内部错误' }
]

const businessErrorCodes = [
  { code: 'INVALID_AMOUNT', message: '金额无效', description: '支付金额必须大于0' },
  { code: 'ORDER_NOT_FOUND', message: '订单不存在', description: '指定的订单ID不存在' },
  { code: 'ORDER_PAID', message: '订单已支付', description: '订单已经支付完成' },
  { code: 'INSUFFICIENT_BALANCE', message: '余额不足', description: '账户余额不足以完成支付' },
  { code: 'PAYMENT_TIMEOUT', message: '支付超时', description: '支付请求超时' }
]

// 代码示例
const responseFormatExample = `{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000,
  "traceId": "abc123def456"
}`

const authHeaderExample = `Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

const paymentNotifyExample = `{
  "type": "PAYMENT_NOTIFY",
  "orderId": "PAY202312010001",
  "merchantOrderNo": "ORDER_123456",
  "amount": 10000,
  "currency": "CNY",
  "status": "SUCCESS",
  "paidAt": "2023-12-01T10:30:00Z",
  "channelType": "ALIPAY",
  "thirdPartyOrderNo": "2023120122001234567890",
  "signature": "abc123def456..."
}`

const refundNotifyExample = `{
  "type": "REFUND_NOTIFY",
  "refundId": "REF202312010001",
  "orderId": "PAY202312010001",
  "refundAmount": 5000,
  "status": "SUCCESS",
  "refundedAt": "2023-12-01T15:30:00Z",
  "signature": "def456ghi789..."
}`

const signatureVerifyExample = `// JavaScript签名验证示例
function verifySignature(data, signature, secret) {
  const crypto = require('crypto');
  const sortedParams = Object.keys(data)
    .sort()
    .map(key => \`\${key}=\${data[key]}\`)
    .join('&');
  
  const signString = sortedParams + '&key=' + secret;
  const calculatedSignature = crypto
    .createHmac('sha256', secret)
    .update(signString)
    .digest('hex');
  
  return calculatedSignature === signature;
}`

const javaExample = `// Java SDK示例
PaymentClient client = new PaymentClient("your-app-id", "your-secret");

// 创建支付订单
CreateOrderRequest request = new CreateOrderRequest()
    .setMerchantOrderNo("ORDER_123456")
    .setTitle("商品购买")
    .setAmount(10000)
    .setCurrency("CNY")
    .setChannelType("ALIPAY")
    .setPaymentMethod("QR_CODE");

PaymentOrder order = client.createOrder(request);
System.out.println("订单创建成功: " + order.getId());`

const phpExample = `<?php
// PHP SDK示例
$client = new PaymentClient('your-app-id', 'your-secret');

// 创建支付订单
$request = [
    'merchantOrderNo' => 'ORDER_123456',
    'title' => '商品购买',
    'amount' => 10000,
    'currency' => 'CNY',
    'channelType' => 'ALIPAY',
    'paymentMethod' => 'QR_CODE'
];

$order = $client->createOrder($request);
echo "订单创建成功: " . $order['id'];
?>`

const pythonExample = `# Python SDK示例
from payment_sdk import PaymentClient

client = PaymentClient('your-app-id', 'your-secret')

# 创建支付订单
request = {
    'merchantOrderNo': 'ORDER_123456',
    'title': '商品购买',
    'amount': 10000,
    'currency': 'CNY',
    'channelType': 'ALIPAY',
    'paymentMethod': 'QR_CODE'
}

order = client.create_order(request)
print(f"订单创建成功: {order['id']}")`

const nodejsExample = `// Node.js SDK示例
const PaymentClient = require('payment-sdk');

const client = new PaymentClient('your-app-id', 'your-secret');

// 创建支付订单
const request = {
  merchantOrderNo: 'ORDER_123456',
  title: '商品购买',
  amount: 10000,
  currency: 'CNY',
  channelType: 'ALIPAY',
  paymentMethod: 'QR_CODE'
};

const order = await client.createOrder(request);
console.log(\`订单创建成功: \${order.id}\`);`

// API参数定义
const tokenParameters = [
  { name: 'grant_type', type: 'string', required: true, description: '授权类型，固定值：client_credentials' },
  { name: 'client_id', type: 'string', required: true, description: '应用ID' },
  { name: 'client_secret', type: 'string', required: true, description: '应用密钥' }
]

const tokenResponse = {
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  token_type: 'Bearer',
  expires_in: 3600,
  scope: 'payment:read payment:write'
}

const refreshParameters = [
  { name: 'grant_type', type: 'string', required: true, description: '授权类型，固定值：refresh_token' },
  { name: 'refresh_token', type: 'string', required: true, description: '刷新令牌' }
]

const createOrderParameters = [
  { name: 'merchantOrderNo', type: 'string', required: true, description: '商户订单号' },
  { name: 'title', type: 'string', required: true, description: '订单标题' },
  { name: 'amount', type: 'integer', required: true, description: '订单金额(分)' },
  { name: 'currency', type: 'string', required: true, description: '货币类型' },
  { name: 'channelType', type: 'string', required: true, description: '支付渠道' },
  { name: 'paymentMethod', type: 'string', required: true, description: '支付方式' }
]

const createOrderResponse = {
  id: 'PAY202312010001',
  merchantOrderNo: 'ORDER_123456',
  title: '商品购买',
  amount: 10000,
  currency: 'CNY',
  status: 'PENDING',
  paymentUrl: 'https://payment.example.com/pay/PAY202312010001',
  qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  expiresAt: '2023-12-01T11:00:00Z',
  createdAt: '2023-12-01T10:30:00Z'
}

const createOrderExamples = [
  {
    title: '支付宝扫码支付',
    request: {
      merchantOrderNo: 'ORDER_123456',
      title: '商品购买',
      amount: 10000,
      currency: 'CNY',
      channelType: 'ALIPAY',
      paymentMethod: 'QR_CODE'
    }
  },
  {
    title: '微信公众号支付',
    request: {
      merchantOrderNo: 'ORDER_123457',
      title: '商品购买',
      amount: 20000,
      currency: 'CNY',
      channelType: 'WECHAT',
      paymentMethod: 'JSAPI',
      userInfo: {
        openid: 'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o'
      }
    }
  }
]

// 其他API参数定义...
const queryOrderParameters = [
  { name: 'orderId', type: 'string', required: true, description: '订单ID' }
]

const queryOrderResponse = createOrderResponse

const cancelOrderParameters = [
  { name: 'orderId', type: 'string', required: true, description: '订单ID' },
  { name: 'reason', type: 'string', required: false, description: '取消原因' }
]

const cancelOrderResponse = {
  success: true,
  message: '订单取消成功'
}

const syncOrderParameters = [
  { name: 'orderId', type: 'string', required: true, description: '订单ID' }
]

const syncOrderResponse = createOrderResponse

const createRefundParameters = [
  { name: 'orderId', type: 'string', required: true, description: '原订单ID' },
  { name: 'refundAmount', type: 'integer', required: true, description: '退款金额(分)' },
  { name: 'refundReason', type: 'string', required: true, description: '退款原因' },
  { name: 'refundReasonDesc', type: 'string', required: false, description: '退款原因描述' }
]

const createRefundResponse = {
  id: 'REF202312010001',
  refundNo: 'REF202312010001',
  orderId: 'PAY202312010001',
  refundAmount: 5000,
  status: 'PENDING',
  createdAt: '2023-12-01T15:30:00Z'
}

const queryRefundParameters = [
  { name: 'refundId', type: 'string', required: true, description: '退款ID' }
]

const queryRefundResponse = createRefundResponse

const approveRefundParameters = [
  { name: 'refundId', type: 'string', required: true, description: '退款ID' },
  { name: 'approved', type: 'boolean', required: true, description: '是否批准' },
  { name: 'comment', type: 'string', required: true, description: '审批意见' }
]

const approveRefundResponse = {
  success: true,
  message: '退款审批成功'
}

// 所有API端点
const allApiEndpoints = [
  { method: 'POST', path: '/oauth/token', title: '获取访问令牌' },
  { method: 'POST', path: '/orders', title: '创建支付订单' },
  { method: 'GET', path: '/orders/{orderId}', title: '查询支付订单' },
  { method: 'POST', path: '/orders/{orderId}/cancel', title: '取消支付订单' },
  { method: 'POST', path: '/refunds', title: '申请退款' },
  { method: 'GET', path: '/refunds/{refundId}', title: '查询退款状态' }
]

// 事件处理
const handleMenuClick = ({ key }: { key: string }) => {
  activeSection.value = key
}

const exportApiDoc = () => {
  // 导出API文档逻辑
  console.log('导出API文档')
}

const showApiTester = () => {
  testerModalVisible.value = true
}
</script>

<style scoped lang="less">
.api-documentation {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .page-description {
        margin: 4px 0 0 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .doc-navigation {
    margin-bottom: 16px;
  }
  
  .doc-content {
    .doc-section {
      h3 {
        margin-top: 24px;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
      
      h4 {
        margin-top: 20px;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
      
      p {
        margin-bottom: 16px;
        color: #595959;
        line-height: 1.6;
      }
    }
  }
}
</style>
