/**
 * 支付应用配置相关类型定义
 * @description 定义支付应用、渠道配置、密钥管理等相关的TypeScript类型
 */

/**
 * 支付渠道类型枚举
 */
export enum PaymentChannelType {
  /** 支付宝 */
  ALIPAY = 'ALIPAY',
  /** 微信支付 */
  WECHAT = 'WECHAT',
  /** 银联支付 */
  UNIONPAY = 'UNIONPAY',
  /** PayPal */
  PAYPAL = 'PAYPAL',
  /** Stripe */
  STRIPE = 'STRIPE',
  /** Square */
  SQUARE = 'SQUARE',
  /** 自定义渠道 */
  CUSTOM = 'CUSTOM'
}

/**
 * 支付环境类型
 */
export enum PaymentEnvironment {
  /** 开发环境 */
  DEVELOPMENT = 'DEVELOPMENT',
  /** 测试环境 */
  TESTING = 'TESTING',
  /** 预生产环境 */
  STAGING = 'STAGING',
  /** 生产环境 */
  PRODUCTION = 'PRODUCTION'
}

/**
 * 应用状态枚举
 */
export enum PaymentAppStatus {
  /** 启用 */
  ENABLED = 'ENABLED',
  /** 禁用 */
  DISABLED = 'DISABLED',
  /** 维护中 */
  MAINTENANCE = 'MAINTENANCE',
  /** 已删除 */
  DELETED = 'DELETED'
}

/**
 * 密钥类型枚举
 */
export enum KeyType {
  /** 应用密钥 */
  APP_KEY = 'APP_KEY',
  /** 私钥 */
  PRIVATE_KEY = 'PRIVATE_KEY',
  /** 公钥 */
  PUBLIC_KEY = 'PUBLIC_KEY',
  /** API密钥 */
  API_KEY = 'API_KEY',
  /** 签名密钥 */
  SIGN_KEY = 'SIGN_KEY'
}

/**
 * 支付渠道配置接口
 */
export interface PaymentChannelConfig {
  /** 渠道ID */
  id: string
  /** 渠道类型 */
  type: PaymentChannelType
  /** 渠道名称 */
  name: string
  /** 渠道描述 */
  description?: string
  /** 渠道图标 */
  icon?: string
  /** 是否启用 */
  enabled: boolean
  /** 排序权重 */
  sortOrder: number
  /** 支持的支付方式 */
  supportedMethods: string[]
  /** 渠道特定配置 */
  config: Record<string, any>
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 支付应用配置接口
 */
export interface PaymentAppConfig {
  /** 应用ID */
  id: string
  /** 应用名称 */
  name: string
  /** 应用描述 */
  description?: string
  /** 应用状态 */
  status: PaymentAppStatus
  /** 支付环境 */
  environment: PaymentEnvironment
  /** 支持的支付渠道 */
  channels: PaymentChannelConfig[]
  /** 回调配置 */
  callbackConfig: CallbackConfig
  /** 安全配置 */
  securityConfig: SecurityConfig
  /** 创建者ID */
  createdBy: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 租户ID */
  tenantId: string
}

/**
 * 回调配置接口
 */
export interface CallbackConfig {
  /** 异步通知URL */
  notifyUrl: string
  /** 同步返回URL */
  returnUrl: string
  /** 超时时间(秒) */
  timeout: number
  /** 重试次数 */
  retryCount: number
  /** 重试间隔(秒) */
  retryInterval: number
  /** 是否验证签名 */
  verifySignature: boolean
  /** 白名单IP */
  whitelistIps?: string[]
}

/**
 * 安全配置接口
 */
export interface SecurityConfig {
  /** 是否启用签名验证 */
  enableSignature: boolean
  /** 签名算法 */
  signatureAlgorithm: string
  /** 是否启用加密 */
  enableEncryption: boolean
  /** 加密算法 */
  encryptionAlgorithm: string
  /** 密钥轮换周期(天) */
  keyRotationDays: number
  /** IP白名单 */
  ipWhitelist?: string[]
  /** 频率限制 */
  rateLimit?: RateLimit
}

/**
 * 频率限制配置
 */
export interface RateLimit {
  /** 时间窗口(秒) */
  windowSeconds: number
  /** 最大请求数 */
  maxRequests: number
  /** 是否启用 */
  enabled: boolean
}

/**
 * 密钥信息接口
 */
export interface KeyInfo {
  /** 密钥ID */
  id: string
  /** 密钥名称 */
  name: string
  /** 密钥类型 */
  type: KeyType
  /** 密钥值(加密存储) */
  value: string
  /** 是否启用 */
  enabled: boolean
  /** 过期时间 */
  expiresAt?: string
  /** 创建时间 */
  createdAt: string
  /** 最后使用时间 */
  lastUsedAt?: string
  /** 使用次数 */
  usageCount: number
  /** 关联应用ID */
  appId: string
}

/**
 * 渠道测试结果接口
 */
export interface ChannelTestResult {
  /** 渠道ID */
  channelId: string
  /** 测试状态 */
  status: 'SUCCESS' | 'FAILED' | 'TIMEOUT'
  /** 响应时间(毫秒) */
  responseTime: number
  /** 错误信息 */
  errorMessage?: string
  /** 测试时间 */
  testTime: string
  /** 测试详情 */
  details?: Record<string, any>
}

/**
 * 支付应用查询参数
 */
export interface PaymentAppQueryParams {
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
  /** 应用名称(模糊搜索) */
  name?: string
  /** 应用状态 */
  status?: PaymentAppStatus
  /** 支付环境 */
  environment?: PaymentEnvironment
  /** 渠道类型 */
  channelType?: PaymentChannelType
  /** 创建时间范围-开始 */
  createdAtStart?: string
  /** 创建时间范围-结束 */
  createdAtEnd?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 支付应用创建/更新参数
 */
export interface PaymentAppFormData {
  /** 应用名称 */
  name: string
  /** 应用描述 */
  description?: string
  /** 支付环境 */
  environment: PaymentEnvironment
  /** 支付渠道配置 */
  channels: Omit<PaymentChannelConfig, 'id' | 'createdAt' | 'updatedAt'>[]
  /** 回调配置 */
  callbackConfig: CallbackConfig
  /** 安全配置 */
  securityConfig: SecurityConfig
}

/**
 * 密钥管理表单数据
 */
export interface KeyFormData {
  /** 密钥名称 */
  name: string
  /** 密钥类型 */
  type: KeyType
  /** 密钥值 */
  value: string
  /** 过期时间 */
  expiresAt?: string
  /** 关联应用ID */
  appId: string
}

/**
 * 渠道测试参数
 */
export interface ChannelTestParams {
  /** 应用ID */
  appId: string
  /** 渠道ID */
  channelId: string
  /** 测试金额(分) */
  amount?: number
  /** 测试超时时间(秒) */
  timeout?: number
}

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 时间戳 */
  timestamp: number
  /** 追踪ID */
  traceId: string
}

/**
 * 分页响应接口
 */
export interface PageResponse<T = any> {
  /** 数据列表 */
  records: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 总页数 */
  pages: number
}

/**
 * 支付渠道特定配置类型
 */
export interface AlipayConfig {
  /** 应用ID */
  appId: string
  /** 商户私钥 */
  privateKey: string
  /** 支付宝公钥 */
  alipayPublicKey: string
  /** 网关地址 */
  gatewayUrl: string
  /** 字符集 */
  charset: string
  /** 签名类型 */
  signType: string
  /** 格式 */
  format: string
}

export interface WechatConfig {
  /** 应用ID */
  appId: string
  /** 商户号 */
  mchId: string
  /** 商户密钥 */
  mchKey: string
  /** 证书路径 */
  certPath?: string
  /** 网关地址 */
  gatewayUrl: string
}

export interface PaypalConfig {
  /** 客户端ID */
  clientId: string
  /** 客户端密钥 */
  clientSecret: string
  /** 环境 */
  environment: 'sandbox' | 'production'
  /** 网关地址 */
  gatewayUrl: string
}

/**
 * 渠道配置联合类型
 */
export type ChannelSpecificConfig = AlipayConfig | WechatConfig | PaypalConfig | Record<string, any>
