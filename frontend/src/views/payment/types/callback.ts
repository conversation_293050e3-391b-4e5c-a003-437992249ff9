/**
 * 回调通知相关类型定义
 * @description 定义支付回调、通知处理、重试机制等相关的TypeScript类型
 */

import type { PaymentChannelType } from './payment-app'

/**
 * 回调类型枚举
 */
export enum CallbackType {
  /** 支付结果通知 */
  PAYMENT_NOTIFY = 'PAYMENT_NOTIFY',
  /** 退款结果通知 */
  REFUND_NOTIFY = 'REFUND_NOTIFY',
  /** 转账结果通知 */
  TRANSFER_NOTIFY = 'TRANSFER_NOTIFY',
  /** 账单通知 */
  BILL_NOTIFY = 'BILL_NOTIFY',
  /** 其他通知 */
  OTHER_NOTIFY = 'OTHER_NOTIFY'
}

/**
 * 回调状态枚举
 */
export enum CallbackStatus {
  /** 待处理 */
  PENDING = 'PENDING',
  /** 处理中 */
  PROCESSING = 'PROCESSING',
  /** 处理成功 */
  SUCCESS = 'SUCCESS',
  /** 处理失败 */
  FAILED = 'FAILED',
  /** 已忽略 */
  IGNORED = 'IGNORED',
  /** 已过期 */
  EXPIRED = 'EXPIRED'
}

/**
 * 签名算法枚举
 */
export enum SignatureAlgorithm {
  /** MD5 */
  MD5 = 'MD5',
  /** SHA1 */
  SHA1 = 'SHA1',
  /** SHA256 */
  SHA256 = 'SHA256',
  /** RSA */
  RSA = 'RSA',
  /** RSA2 */
  RSA2 = 'RSA2',
  /** HMAC-SHA256 */
  HMAC_SHA256 = 'HMAC_SHA256'
}

/**
 * 回调记录基础信息
 */
export interface CallbackRecord {
  /** 回调记录ID */
  id: string
  /** 回调类型 */
  type: CallbackType
  /** 支付渠道 */
  channelType: PaymentChannelType
  /** 关联订单ID */
  orderId: string
  /** 关联订单号 */
  orderNo: string
  /** 第三方订单号 */
  thirdPartyOrderNo?: string
  /** 回调URL */
  callbackUrl: string
  /** 请求方法 */
  requestMethod: 'GET' | 'POST' | 'PUT'
  /** 请求头 */
  requestHeaders: Record<string, string>
  /** 请求参数 */
  requestParams?: Record<string, any>
  /** 请求体 */
  requestBody?: string
  /** 响应状态码 */
  responseStatus?: number
  /** 响应头 */
  responseHeaders?: Record<string, string>
  /** 响应体 */
  responseBody?: string
  /** 处理状态 */
  status: CallbackStatus
  /** 错误信息 */
  errorMessage?: string
  /** 重试次数 */
  retryCount: number
  /** 最大重试次数 */
  maxRetryCount: number
  /** 下次重试时间 */
  nextRetryAt?: string
  /** 签名验证结果 */
  signatureVerified?: boolean
  /** 处理耗时(毫秒) */
  processingTime?: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 完成时间 */
  completedAt?: string
  /** 租户ID */
  tenantId: string
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 回调查询参数
 */
export interface CallbackQueryParams {
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
  /** 回调类型 */
  type?: CallbackType[]
  /** 支付渠道 */
  channelType?: PaymentChannelType[]
  /** 关联订单号 */
  orderNo?: string
  /** 第三方订单号 */
  thirdPartyOrderNo?: string
  /** 处理状态 */
  status?: CallbackStatus[]
  /** 回调URL(模糊搜索) */
  callbackUrl?: string
  /** 是否签名验证通过 */
  signatureVerified?: boolean
  /** 重试次数范围-最小值 */
  retryCountMin?: number
  /** 重试次数范围-最大值 */
  retryCountMax?: number
  /** 创建时间范围-开始 */
  createdAtStart?: string
  /** 创建时间范围-结束 */
  createdAtEnd?: string
  /** 完成时间范围-开始 */
  completedAtStart?: string
  /** 完成时间范围-结束 */
  completedAtEnd?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 重试配置
 */
export interface RetryConfig {
  /** 是否启用重试 */
  enabled: boolean
  /** 最大重试次数 */
  maxRetryCount: number
  /** 重试间隔(秒) */
  retryInterval: number
  /** 重试间隔递增因子 */
  backoffMultiplier: number
  /** 最大重试间隔(秒) */
  maxRetryInterval: number
  /** 重试条件 */
  retryConditions: RetryCondition[]
}

/**
 * 重试条件
 */
export interface RetryCondition {
  /** 条件类型 */
  type: 'HTTP_STATUS' | 'RESPONSE_CONTENT' | 'TIMEOUT' | 'EXCEPTION'
  /** 条件值 */
  value: string | number
  /** 操作符 */
  operator: 'EQUALS' | 'NOT_EQUALS' | 'CONTAINS' | 'NOT_CONTAINS' | 'GREATER_THAN' | 'LESS_THAN'
  /** 是否启用 */
  enabled: boolean
}

/**
 * 签名验证配置
 */
export interface SignatureConfig {
  /** 是否启用签名验证 */
  enabled: boolean
  /** 签名算法 */
  algorithm: SignatureAlgorithm
  /** 签名字段名 */
  signField: string
  /** 签名密钥 */
  signKey: string
  /** 参与签名的字段 */
  signFields?: string[]
  /** 字段排序方式 */
  sortType: 'ASC' | 'DESC' | 'NONE'
  /** 连接符 */
  separator: string
  /** 是否包含空值 */
  includeEmpty: boolean
}

/**
 * 回调处理结果
 */
export interface CallbackProcessResult {
  /** 处理是否成功 */
  success: boolean
  /** 处理消息 */
  message?: string
  /** 错误码 */
  errorCode?: string
  /** 处理耗时(毫秒) */
  processingTime: number
  /** 是否需要重试 */
  needRetry: boolean
  /** 下次重试时间 */
  nextRetryAt?: string
  /** 处理详情 */
  details?: Record<string, any>
}

/**
 * 回调统计数据
 */
export interface CallbackStatistics {
  /** 总回调数 */
  totalCount: number
  /** 成功回调数 */
  successCount: number
  /** 失败回调数 */
  failedCount: number
  /** 待处理回调数 */
  pendingCount: number
  /** 成功率 */
  successRate: number
  /** 平均处理时间(毫秒) */
  averageProcessingTime: number
  /** 平均重试次数 */
  averageRetryCount: number
  /** 按渠道统计 */
  channelStats: ChannelCallbackStats[]
  /** 按类型统计 */
  typeStats: TypeCallbackStats[]
  /** 统计时间范围 */
  dateRange: {
    start: string
    end: string
  }
}

/**
 * 渠道回调统计
 */
export interface ChannelCallbackStats {
  /** 支付渠道 */
  channelType: PaymentChannelType
  /** 回调总数 */
  totalCount: number
  /** 成功数 */
  successCount: number
  /** 失败数 */
  failedCount: number
  /** 成功率 */
  successRate: number
  /** 平均处理时间(毫秒) */
  averageProcessingTime: number
}

/**
 * 类型回调统计
 */
export interface TypeCallbackStats {
  /** 回调类型 */
  type: CallbackType
  /** 回调总数 */
  totalCount: number
  /** 成功数 */
  successCount: number
  /** 失败数 */
  failedCount: number
  /** 成功率 */
  successRate: number
  /** 平均处理时间(毫秒) */
  averageProcessingTime: number
}

/**
 * 回调趋势数据
 */
export interface CallbackTrendData {
  /** 日期 */
  date: string
  /** 回调总数 */
  totalCount: number
  /** 成功数 */
  successCount: number
  /** 失败数 */
  failedCount: number
  /** 成功率 */
  successRate: number
  /** 平均处理时间(毫秒) */
  averageProcessingTime: number
}

/**
 * 回调重试参数
 */
export interface CallbackRetryParams {
  /** 回调记录ID */
  callbackId: string
  /** 是否立即重试 */
  immediate?: boolean
  /** 重试原因 */
  reason?: string
  /** 操作人ID */
  operatorId?: string
}

/**
 * 批量回调操作参数
 */
export interface BatchCallbackParams {
  /** 回调记录ID列表 */
  callbackIds: string[]
  /** 操作类型 */
  action: 'RETRY' | 'IGNORE' | 'DELETE'
  /** 操作原因 */
  reason?: string
  /** 操作人ID */
  operatorId?: string
}

/**
 * 回调监控告警配置
 */
export interface CallbackAlertConfig {
  /** 是否启用告警 */
  enabled: boolean
  /** 失败率阈值(%) */
  failureRateThreshold: number
  /** 处理时间阈值(毫秒) */
  processingTimeThreshold: number
  /** 重试次数阈值 */
  retryCountThreshold: number
  /** 告警接收人 */
  recipients: string[]
  /** 告警方式 */
  alertMethods: ('EMAIL' | 'SMS' | 'WEBHOOK')[]
  /** 告警间隔(分钟) */
  alertInterval: number
}

/**
 * 回调详情扩展信息
 */
export interface CallbackDetailInfo extends CallbackRecord {
  /** 重试历史 */
  retryHistory: CallbackRetryHistory[]
  /** 签名验证详情 */
  signatureDetails?: SignatureVerificationDetails
  /** 关联订单信息 */
  orderInfo?: any
}

/**
 * 回调重试历史
 */
export interface CallbackRetryHistory {
  /** 重试记录ID */
  id: string
  /** 回调记录ID */
  callbackId: string
  /** 重试次数 */
  retryCount: number
  /** 重试时间 */
  retryAt: string
  /** 重试结果 */
  result: CallbackProcessResult
  /** 重试原因 */
  reason?: string
}

/**
 * 签名验证详情
 */
export interface SignatureVerificationDetails {
  /** 验证结果 */
  verified: boolean
  /** 签名算法 */
  algorithm: SignatureAlgorithm
  /** 原始签名 */
  originalSignature: string
  /** 计算签名 */
  calculatedSignature: string
  /** 签名字符串 */
  signString: string
  /** 验证时间 */
  verifiedAt: string
  /** 错误信息 */
  errorMessage?: string
}
