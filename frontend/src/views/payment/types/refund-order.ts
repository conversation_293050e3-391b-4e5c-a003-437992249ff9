/**
 * 退款订单相关类型定义
 * @description 定义退款订单、退款状态、退款操作等相关的TypeScript类型
 */

import type { PaymentChannelType, Currency } from './payment-order'

/**
 * 退款订单状态枚举
 */
export enum RefundOrderStatus {
  /** 待处理 */
  PENDING = 'PENDING',
  /** 已批准 */
  APPROVED = 'APPROVED',
  /** 退款中 */
  PROCESSING = 'PROCESSING',
  /** 退款成功 */
  SUCCESS = 'SUCCESS',
  /** 退款失败 */
  FAILED = 'FAILED',
  /** 已拒绝 */
  REJECTED = 'REJECTED',
  /** 已取消 */
  CANCELLED = 'CANCELLED'
}

/**
 * 退款类型枚举
 */
export enum RefundType {
  /** 全额退款 */
  FULL = 'FULL',
  /** 部分退款 */
  PARTIAL = 'PARTIAL'
}

/**
 * 退款原因枚举
 */
export enum RefundReason {
  /** 用户申请 */
  USER_REQUEST = 'USER_REQUEST',
  /** 商品质量问题 */
  QUALITY_ISSUE = 'QUALITY_ISSUE',
  /** 商品缺货 */
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  /** 价格错误 */
  PRICE_ERROR = 'PRICE_ERROR',
  /** 重复支付 */
  DUPLICATE_PAYMENT = 'DUPLICATE_PAYMENT',
  /** 系统错误 */
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  /** 风控拦截 */
  RISK_CONTROL = 'RISK_CONTROL',
  /** 其他原因 */
  OTHER = 'OTHER'
}

/**
 * 退款审批状态枚举
 */
export enum RefundApprovalStatus {
  /** 待审批 */
  PENDING = 'PENDING',
  /** 审批中 */
  IN_PROGRESS = 'IN_PROGRESS',
  /** 已通过 */
  APPROVED = 'APPROVED',
  /** 已拒绝 */
  REJECTED = 'REJECTED',
  /** 已撤回 */
  WITHDRAWN = 'WITHDRAWN'
}

/**
 * 退款订单基础信息
 */
export interface RefundOrder {
  /** 退款订单ID */
  id: string
  /** 退款单号 */
  refundNo: string
  /** 原支付订单ID */
  paymentOrderId: string
  /** 原支付订单号 */
  paymentOrderNo: string
  /** 第三方退款单号 */
  thirdPartyRefundNo?: string
  /** 退款标题 */
  title: string
  /** 退款描述 */
  description?: string
  /** 退款类型 */
  refundType: RefundType
  /** 退款原因 */
  refundReason: RefundReason
  /** 退款原因描述 */
  refundReasonDesc?: string
  /** 原订单金额(分) */
  originalAmount: number
  /** 申请退款金额(分) */
  refundAmount: number
  /** 实际退款金额(分) */
  actualRefundAmount?: number
  /** 货币类型 */
  currency: Currency
  /** 退款状态 */
  status: RefundOrderStatus
  /** 支付渠道 */
  channelType: PaymentChannelType
  /** 申请人ID */
  applicantId: string
  /** 申请人姓名 */
  applicantName: string
  /** 审批流程ID */
  approvalFlowId?: string
  /** 审批状态 */
  approvalStatus: RefundApprovalStatus
  /** 审批人ID */
  approverId?: string
  /** 审批人姓名 */
  approverName?: string
  /** 审批时间 */
  approvedAt?: string
  /** 审批意见 */
  approvalComment?: string
  /** 退款完成时间 */
  refundedAt?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 租户ID */
  tenantId: string
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 退款申请参数
 */
export interface CreateRefundOrderParams {
  /** 原支付订单ID */
  paymentOrderId: string
  /** 退款类型 */
  refundType: RefundType
  /** 退款原因 */
  refundReason: RefundReason
  /** 退款原因描述 */
  refundReasonDesc?: string
  /** 申请退款金额(分) */
  refundAmount: number
  /** 申请人ID */
  applicantId: string
  /** 是否需要审批 */
  needApproval?: boolean
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 退款订单查询参数
 */
export interface RefundOrderQueryParams {
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
  /** 退款单号 */
  refundNo?: string
  /** 原支付订单号 */
  paymentOrderNo?: string
  /** 第三方退款单号 */
  thirdPartyRefundNo?: string
  /** 退款标题(模糊搜索) */
  title?: string
  /** 退款状态 */
  status?: RefundOrderStatus[]
  /** 退款类型 */
  refundType?: RefundType[]
  /** 退款原因 */
  refundReason?: RefundReason[]
  /** 审批状态 */
  approvalStatus?: RefundApprovalStatus[]
  /** 支付渠道 */
  channelType?: PaymentChannelType[]
  /** 申请人ID */
  applicantId?: string
  /** 审批人ID */
  approverId?: string
  /** 退款金额范围-最小值(分) */
  refundAmountMin?: number
  /** 退款金额范围-最大值(分) */
  refundAmountMax?: number
  /** 创建时间范围-开始 */
  createdAtStart?: string
  /** 创建时间范围-结束 */
  createdAtEnd?: string
  /** 退款时间范围-开始 */
  refundedAtStart?: string
  /** 退款时间范围-结束 */
  refundedAtEnd?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 退款审批参数
 */
export interface RefundApprovalParams {
  /** 退款订单ID */
  refundOrderId: string
  /** 审批结果 */
  approved: boolean
  /** 审批意见 */
  comment?: string
  /** 审批人ID */
  approverId: string
  /** 实际退款金额(分) - 审批时可调整 */
  actualRefundAmount?: number
}

/**
 * 退款状态变更记录
 */
export interface RefundStatusHistory {
  /** 记录ID */
  id: string
  /** 退款订单ID */
  refundOrderId: string
  /** 原状态 */
  fromStatus: RefundOrderStatus
  /** 新状态 */
  toStatus: RefundOrderStatus
  /** 变更原因 */
  reason?: string
  /** 操作人ID */
  operatorId?: string
  /** 操作人姓名 */
  operatorName?: string
  /** 变更时间 */
  changedAt: string
  /** 备注 */
  remark?: string
}

/**
 * 退款统计数据
 */
export interface RefundStatistics {
  /** 总退款订单数 */
  totalCount: number
  /** 总退款金额(分) */
  totalAmount: number
  /** 成功退款订单数 */
  successCount: number
  /** 成功退款金额(分) */
  successAmount: number
  /** 失败退款订单数 */
  failedCount: number
  /** 失败退款金额(分) */
  failedAmount: number
  /** 待处理退款订单数 */
  pendingCount: number
  /** 待处理退款金额(分) */
  pendingAmount: number
  /** 退款成功率 */
  successRate: number
  /** 平均退款金额(分) */
  averageAmount: number
  /** 统计时间范围 */
  dateRange: {
    start: string
    end: string
  }
}

/**
 * 退款趋势数据
 */
export interface RefundTrendData {
  /** 日期 */
  date: string
  /** 退款订单数量 */
  refundCount: number
  /** 退款金额(分) */
  refundAmount: number
  /** 成功退款数 */
  successCount: number
  /** 成功退款金额(分) */
  successAmount: number
  /** 退款成功率 */
  successRate: number
}

/**
 * 部分退款计算参数
 */
export interface PartialRefundCalculation {
  /** 原订单金额(分) */
  originalAmount: number
  /** 已退款金额(分) */
  refundedAmount: number
  /** 可退款金额(分) */
  refundableAmount: number
  /** 申请退款金额(分) */
  requestAmount: number
  /** 手续费(分) */
  fee?: number
  /** 实际退款金额(分) */
  actualAmount: number
  /** 退款明细 */
  details: RefundAmountDetail[]
}

/**
 * 退款金额明细
 */
export interface RefundAmountDetail {
  /** 明细类型 */
  type: 'PRINCIPAL' | 'FEE' | 'INTEREST' | 'OTHER'
  /** 明细名称 */
  name: string
  /** 金额(分) */
  amount: number
  /** 说明 */
  description?: string
}

/**
 * 退款审批流程节点
 */
export interface RefundApprovalNode {
  /** 节点ID */
  id: string
  /** 节点名称 */
  name: string
  /** 节点类型 */
  type: 'START' | 'APPROVAL' | 'END'
  /** 审批人ID */
  approverId?: string
  /** 审批人姓名 */
  approverName?: string
  /** 审批状态 */
  status: RefundApprovalStatus
  /** 审批时间 */
  approvedAt?: string
  /** 审批意见 */
  comment?: string
  /** 节点顺序 */
  order: number
}

/**
 * 退款审批流程
 */
export interface RefundApprovalFlow {
  /** 流程ID */
  id: string
  /** 流程名称 */
  name: string
  /** 退款订单ID */
  refundOrderId: string
  /** 流程状态 */
  status: RefundApprovalStatus
  /** 当前节点 */
  currentNode?: RefundApprovalNode
  /** 所有节点 */
  nodes: RefundApprovalNode[]
  /** 创建时间 */
  createdAt: string
  /** 完成时间 */
  completedAt?: string
}

/**
 * 退款导出参数
 */
export interface RefundExportParams {
  /** 查询参数 */
  queryParams: RefundOrderQueryParams
  /** 导出格式 */
  format: 'EXCEL' | 'CSV'
  /** 导出字段 */
  fields?: string[]
  /** 文件名 */
  filename?: string
}

/**
 * 退款详情扩展信息
 */
export interface RefundDetailInfo extends RefundOrder {
  /** 原支付订单信息 */
  paymentOrder?: any
  /** 状态变更历史 */
  statusHistory: RefundStatusHistory[]
  /** 审批流程 */
  approvalFlow?: RefundApprovalFlow
  /** 退款计算详情 */
  calculation?: PartialRefundCalculation
}
