/**
 * 支付订单相关类型定义
 * @description 定义支付订单、订单状态、订单操作等相关的TypeScript类型
 */

import type { PaymentChannelType } from './payment-app'

/**
 * 支付订单状态枚举
 */
export enum PaymentOrderStatus {
  /** 待支付 */
  PENDING = 'PENDING',
  /** 处理中 */
  PROCESSING = 'PROCESSING',
  /** 支付成功 */
  SUCCESS = 'SUCCESS',
  /** 支付失败 */
  FAILED = 'FAILED',
  /** 已取消 */
  CANCELLED = 'CANCELLED',
  /** 超时 */
  TIMEOUT = 'TIMEOUT',
  /** 部分退款 */
  PARTIAL_REFUNDED = 'PARTIAL_REFUNDED',
  /** 全额退款 */
  FULL_REFUNDED = 'FULL_REFUNDED'
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  /** 扫码支付 */
  QR_CODE = 'QR_CODE',
  /** 网页支付 */
  WEB = 'WEB',
  /** 手机支付 */
  MOBILE = 'MOBILE',
  /** APP支付 */
  APP = 'APP',
  /** 小程序支付 */
  MINI_PROGRAM = 'MINI_PROGRAM',
  /** 公众号支付 */
  JSAPI = 'JSAPI',
  /** 刷卡支付 */
  MICROPAY = 'MICROPAY',
  /** 快捷支付 */
  QUICK_PAY = 'QUICK_PAY'
}

/**
 * 货币类型枚举
 */
export enum Currency {
  /** 人民币 */
  CNY = 'CNY',
  /** 美元 */
  USD = 'USD',
  /** 欧元 */
  EUR = 'EUR',
  /** 日元 */
  JPY = 'JPY',
  /** 英镑 */
  GBP = 'GBP',
  /** 港币 */
  HKD = 'HKD'
}

/**
 * 订单来源枚举
 */
export enum OrderSource {
  /** 网站 */
  WEB = 'WEB',
  /** 移动端 */
  MOBILE = 'MOBILE',
  /** APP */
  APP = 'APP',
  /** 小程序 */
  MINI_PROGRAM = 'MINI_PROGRAM',
  /** API */
  API = 'API',
  /** 后台管理 */
  ADMIN = 'ADMIN'
}

/**
 * 支付订单基础信息
 */
export interface PaymentOrder {
  /** 订单ID */
  id: string
  /** 商户订单号 */
  merchantOrderNo: string
  /** 第三方订单号 */
  thirdPartyOrderNo?: string
  /** 订单标题 */
  title: string
  /** 订单描述 */
  description?: string
  /** 订单金额(分) */
  amount: number
  /** 实际支付金额(分) */
  paidAmount?: number
  /** 货币类型 */
  currency: Currency
  /** 支付状态 */
  status: PaymentOrderStatus
  /** 支付渠道 */
  channelType: PaymentChannelType
  /** 支付方式 */
  paymentMethod: PaymentMethod
  /** 支付应用ID */
  appId: string
  /** 支付用户ID */
  userId?: string
  /** 支付用户信息 */
  userInfo?: PaymentUserInfo
  /** 订单来源 */
  source: OrderSource
  /** 客户端IP */
  clientIp?: string
  /** 用户代理 */
  userAgent?: string
  /** 异步通知URL */
  notifyUrl?: string
  /** 同步返回URL */
  returnUrl?: string
  /** 订单过期时间 */
  expiresAt?: string
  /** 支付完成时间 */
  paidAt?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 租户ID */
  tenantId: string
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 支付用户信息
 */
export interface PaymentUserInfo {
  /** 用户ID */
  userId?: string
  /** 用户名 */
  username?: string
  /** 用户昵称 */
  nickname?: string
  /** 用户邮箱 */
  email?: string
  /** 用户手机号 */
  phone?: string
  /** 用户头像 */
  avatar?: string
  /** 用户类型 */
  userType?: string
}

/**
 * 订单状态变更记录
 */
export interface OrderStatusHistory {
  /** 记录ID */
  id: string
  /** 订单ID */
  orderId: string
  /** 原状态 */
  fromStatus: PaymentOrderStatus
  /** 新状态 */
  toStatus: PaymentOrderStatus
  /** 变更原因 */
  reason?: string
  /** 操作人ID */
  operatorId?: string
  /** 操作人姓名 */
  operatorName?: string
  /** 变更时间 */
  changedAt: string
  /** 备注 */
  remark?: string
}

/**
 * 支付订单查询参数
 */
export interface PaymentOrderQueryParams {
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
  /** 商户订单号 */
  merchantOrderNo?: string
  /** 第三方订单号 */
  thirdPartyOrderNo?: string
  /** 订单标题(模糊搜索) */
  title?: string
  /** 支付状态 */
  status?: PaymentOrderStatus[]
  /** 支付渠道 */
  channelType?: PaymentChannelType[]
  /** 支付方式 */
  paymentMethod?: PaymentMethod[]
  /** 支付应用ID */
  appId?: string
  /** 支付用户ID */
  userId?: string
  /** 订单来源 */
  source?: OrderSource[]
  /** 金额范围-最小值(分) */
  amountMin?: number
  /** 金额范围-最大值(分) */
  amountMax?: number
  /** 创建时间范围-开始 */
  createdAtStart?: string
  /** 创建时间范围-结束 */
  createdAtEnd?: string
  /** 支付时间范围-开始 */
  paidAtStart?: string
  /** 支付时间范围-结束 */
  paidAtEnd?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 支付订单创建参数
 */
export interface CreatePaymentOrderParams {
  /** 商户订单号 */
  merchantOrderNo: string
  /** 订单标题 */
  title: string
  /** 订单描述 */
  description?: string
  /** 订单金额(分) */
  amount: number
  /** 货币类型 */
  currency: Currency
  /** 支付渠道 */
  channelType: PaymentChannelType
  /** 支付方式 */
  paymentMethod: PaymentMethod
  /** 支付应用ID */
  appId: string
  /** 支付用户ID */
  userId?: string
  /** 支付用户信息 */
  userInfo?: PaymentUserInfo
  /** 订单来源 */
  source: OrderSource
  /** 客户端IP */
  clientIp?: string
  /** 用户代理 */
  userAgent?: string
  /** 异步通知URL */
  notifyUrl?: string
  /** 同步返回URL */
  returnUrl?: string
  /** 订单过期时间(分钟) */
  expireMinutes?: number
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 支付订单更新参数
 */
export interface UpdatePaymentOrderParams {
  /** 订单ID */
  id: string
  /** 订单标题 */
  title?: string
  /** 订单描述 */
  description?: string
  /** 异步通知URL */
  notifyUrl?: string
  /** 同步返回URL */
  returnUrl?: string
  /** 扩展字段 */
  extra?: Record<string, any>
}

/**
 * 订单操作参数
 */
export interface OrderActionParams {
  /** 订单ID */
  orderId: string
  /** 操作原因 */
  reason?: string
  /** 操作人ID */
  operatorId?: string
  /** 备注 */
  remark?: string
}

/**
 * 订单取消参数
 */
export interface CancelOrderParams extends OrderActionParams {
  /** 取消原因 */
  cancelReason: string
}

/**
 * 订单统计数据
 */
export interface OrderStatistics {
  /** 总订单数 */
  totalCount: number
  /** 总金额(分) */
  totalAmount: number
  /** 成功订单数 */
  successCount: number
  /** 成功金额(分) */
  successAmount: number
  /** 失败订单数 */
  failedCount: number
  /** 失败金额(分) */
  failedAmount: number
  /** 待支付订单数 */
  pendingCount: number
  /** 待支付金额(分) */
  pendingAmount: number
  /** 成功率 */
  successRate: number
  /** 统计时间范围 */
  dateRange: {
    start: string
    end: string
  }
}

/**
 * 订单趋势数据
 */
export interface OrderTrendData {
  /** 日期 */
  date: string
  /** 订单数量 */
  orderCount: number
  /** 订单金额(分) */
  orderAmount: number
  /** 成功订单数 */
  successCount: number
  /** 成功金额(分) */
  successAmount: number
  /** 成功率 */
  successRate: number
}

/**
 * 订单导出参数
 */
export interface OrderExportParams {
  /** 查询参数 */
  queryParams: PaymentOrderQueryParams
  /** 导出格式 */
  format: 'EXCEL' | 'CSV'
  /** 导出字段 */
  fields?: string[]
  /** 文件名 */
  filename?: string
}

/**
 * 订单详情扩展信息
 */
export interface OrderDetailInfo extends PaymentOrder {
  /** 状态变更历史 */
  statusHistory: OrderStatusHistory[]
  /** 退款记录 */
  refundRecords?: any[]
  /** 回调记录 */
  callbackRecords?: any[]
  /** 相关订单 */
  relatedOrders?: PaymentOrder[]
}
