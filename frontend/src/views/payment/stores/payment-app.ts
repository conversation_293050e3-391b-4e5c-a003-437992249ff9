/**
 * 支付应用配置状态管理
 * @description 使用Pinia管理支付应用配置相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import type {
  PaymentAppConfig,
  PaymentAppQueryParams,
  PaymentAppFormData,
  PaymentChannelConfig,
  KeyInfo,
  ChannelTestResult,
  PageResponse
} from '../types/payment-app'
import { paymentAppApi, keyManagementApi } from '../api/payment-app'

/**
 * 支付应用配置状态管理
 */
export const usePaymentAppStore = defineStore('paymentApp', () => {
  // 状态定义
  const apps = ref<PaymentAppConfig[]>([])
  const currentApp = ref<PaymentAppConfig | null>(null)
  const channels = ref<PaymentChannelConfig[]>([])
  const keys = ref<KeyInfo[]>([])
  const testResults = ref<ChannelTestResult[]>([])
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const enabledApps = computed(() => apps.value.filter(app => app.status === 'ENABLED'))
  const enabledChannels = computed(() => channels.value.filter(channel => channel.enabled))
  const activeKeys = computed(() => keys.value.filter(key => key.enabled))

  // 获取支付应用列表
  const fetchApps = async (params?: PaymentAppQueryParams) => {
    try {
      loading.value = true
      const response = await paymentAppApi.getPaymentApps({
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...params
      })
      
      if (response.code === 200) {
        apps.value = response.data.records
        pagination.value.total = response.data.total
        pagination.value.current = response.data.current
      }
    } catch (error) {
      console.error('获取支付应用列表失败:', error)
      message.error('获取支付应用列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取支付应用详情
  const fetchApp = async (id: string) => {
    try {
      loading.value = true
      const response = await paymentAppApi.getPaymentApp(id)
      
      if (response.code === 200) {
        currentApp.value = response.data
        channels.value = response.data.channels
      }
    } catch (error) {
      console.error('获取支付应用详情失败:', error)
      message.error('获取支付应用详情失败')
    } finally {
      loading.value = false
    }
  }

  // 创建支付应用
  const createApp = async (data: PaymentAppFormData) => {
    try {
      loading.value = true
      const response = await paymentAppApi.createPaymentApp(data)
      
      if (response.code === 200) {
        message.success('创建支付应用成功')
        await fetchApps()
        return response.data
      }
    } catch (error) {
      console.error('创建支付应用失败:', error)
      message.error('创建支付应用失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新支付应用
  const updateApp = async (id: string, data: Partial<PaymentAppFormData>) => {
    try {
      loading.value = true
      const response = await paymentAppApi.updatePaymentApp(id, data)
      
      if (response.code === 200) {
        message.success('更新支付应用成功')
        await fetchApps()
        if (currentApp.value?.id === id) {
          currentApp.value = response.data
        }
        return response.data
      }
    } catch (error) {
      console.error('更新支付应用失败:', error)
      message.error('更新支付应用失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除支付应用
  const deleteApp = async (id: string) => {
    try {
      loading.value = true
      const response = await paymentAppApi.deletePaymentApp(id)
      
      if (response.code === 200) {
        message.success('删除支付应用成功')
        await fetchApps()
      }
    } catch (error) {
      console.error('删除支付应用失败:', error)
      message.error('删除支付应用失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 启用/禁用支付应用
  const toggleApp = async (id: string, enabled: boolean) => {
    try {
      const response = await paymentAppApi.togglePaymentApp(id, enabled)
      
      if (response.code === 200) {
        message.success(`${enabled ? '启用' : '禁用'}支付应用成功`)
        await fetchApps()
      }
    } catch (error) {
      console.error('切换支付应用状态失败:', error)
      message.error('切换支付应用状态失败')
      throw error
    }
  }

  // 测试支付渠道
  const testChannel = async (appId: string, channelId: string) => {
    try {
      loading.value = true
      const response = await paymentAppApi.testChannel({ appId, channelId })
      
      if (response.code === 200) {
        const existingIndex = testResults.value.findIndex(r => r.channelId === channelId)
        if (existingIndex >= 0) {
          testResults.value[existingIndex] = response.data
        } else {
          testResults.value.push(response.data)
        }
        
        if (response.data.status === 'SUCCESS') {
          message.success('渠道测试成功')
        } else {
          message.warning(`渠道测试失败: ${response.data.errorMessage}`)
        }
        
        return response.data
      }
    } catch (error) {
      console.error('测试支付渠道失败:', error)
      message.error('测试支付渠道失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 批量测试支付渠道
  const batchTestChannels = async (appId: string) => {
    try {
      loading.value = true
      const response = await paymentAppApi.batchTestChannels(appId)
      
      if (response.code === 200) {
        testResults.value = response.data
        const successCount = response.data.filter(r => r.status === 'SUCCESS').length
        message.success(`批量测试完成，成功 ${successCount}/${response.data.length} 个渠道`)
        return response.data
      }
    } catch (error) {
      console.error('批量测试支付渠道失败:', error)
      message.error('批量测试支付渠道失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取密钥列表
  const fetchKeys = async (appId: string) => {
    try {
      const response = await keyManagementApi.getKeys(appId)
      
      if (response.code === 200) {
        keys.value = response.data
      }
    } catch (error) {
      console.error('获取密钥列表失败:', error)
      message.error('获取密钥列表失败')
    }
  }

  // 创建密钥
  const createKey = async (data: any) => {
    try {
      const response = await keyManagementApi.createKey(data)
      
      if (response.code === 200) {
        message.success('创建密钥成功')
        await fetchKeys(data.appId)
        return response.data
      }
    } catch (error) {
      console.error('创建密钥失败:', error)
      message.error('创建密钥失败')
      throw error
    }
  }

  // 删除密钥
  const deleteKey = async (id: string, appId: string) => {
    try {
      const response = await keyManagementApi.deleteKey(id)
      
      if (response.code === 200) {
        message.success('删除密钥成功')
        await fetchKeys(appId)
      }
    } catch (error) {
      console.error('删除密钥失败:', error)
      message.error('删除密钥失败')
      throw error
    }
  }

  // 轮换密钥
  const rotateKey = async (id: string, appId: string) => {
    try {
      const response = await keyManagementApi.rotateKey(id)
      
      if (response.code === 200) {
        message.success('密钥轮换成功')
        await fetchKeys(appId)
        return response.data
      }
    } catch (error) {
      console.error('密钥轮换失败:', error)
      message.error('密钥轮换失败')
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    apps.value = []
    currentApp.value = null
    channels.value = []
    keys.value = []
    testResults.value = []
    loading.value = false
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }

  return {
    // 状态
    apps,
    currentApp,
    channels,
    keys,
    testResults,
    loading,
    pagination,
    
    // 计算属性
    enabledApps,
    enabledChannels,
    activeKeys,
    
    // 方法
    fetchApps,
    fetchApp,
    createApp,
    updateApp,
    deleteApp,
    toggleApp,
    testChannel,
    batchTestChannels,
    fetchKeys,
    createKey,
    deleteKey,
    rotateKey,
    resetState,
    setPagination
  }
})
