/**
 * 支付订单状态管理
 * @description 使用Pinia管理支付订单相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import type {
  PaymentOrder,
  PaymentOrderQueryParams,
  CreatePaymentOrderParams,
  UpdatePaymentOrderParams,
  CancelOrderParams,
  OrderStatistics,
  OrderTrendData,
  OrderExportParams,
  OrderDetailInfo,
  PageResponse
} from '../types/payment-order'
import { paymentOrderApi } from '../api/payment-order'

/**
 * 支付订单状态管理
 */
export const usePaymentOrderStore = defineStore('paymentOrder', () => {
  // 状态定义
  const orders = ref<PaymentOrder[]>([])
  const currentOrder = ref<OrderDetailInfo | null>(null)
  const statistics = ref<OrderStatistics>({
    totalCount: 0,
    totalAmount: 0,
    successCount: 0,
    successAmount: 0,
    failedCount: 0,
    failedAmount: 0,
    pendingCount: 0,
    pendingAmount: 0,
    successRate: 0,
    dateRange: {
      start: '',
      end: ''
    }
  })
  const trendData = ref<OrderTrendData[]>([])
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const successOrders = computed(() => orders.value.filter(order => order.status === 'SUCCESS'))
  const pendingOrders = computed(() => orders.value.filter(order => order.status === 'PENDING'))
  const failedOrders = computed(() => orders.value.filter(order => order.status === 'FAILED'))
  const totalAmount = computed(() => orders.value.reduce((sum, order) => sum + order.amount, 0))

  // 获取支付订单列表
  const fetchOrders = async (params?: PaymentOrderQueryParams) => {
    try {
      loading.value = true
      const response = await paymentOrderApi.getPaymentOrders({
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...params
      })
      
      if (response.code === 200) {
        orders.value = response.data.records
        pagination.value.total = response.data.total
        pagination.value.current = response.data.current
      }
    } catch (error) {
      console.error('获取支付订单列表失败:', error)
      message.error('获取支付订单列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取支付订单详情
  const fetchOrder = async (id: string) => {
    try {
      loading.value = true
      const response = await paymentOrderApi.getPaymentOrder(id)
      
      if (response.code === 200) {
        currentOrder.value = response.data
      }
    } catch (error) {
      console.error('获取支付订单详情失败:', error)
      message.error('获取支付订单详情失败')
    } finally {
      loading.value = false
    }
  }

  // 创建支付订单
  const createOrder = async (data: CreatePaymentOrderParams) => {
    try {
      loading.value = true
      const response = await paymentOrderApi.createPaymentOrder(data)
      
      if (response.code === 200) {
        message.success('创建支付订单成功')
        await fetchOrders()
        return response.data
      }
    } catch (error) {
      console.error('创建支付订单失败:', error)
      message.error('创建支付订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新支付订单
  const updateOrder = async (id: string, data: UpdatePaymentOrderParams) => {
    try {
      loading.value = true
      const response = await paymentOrderApi.updatePaymentOrder(id, data)
      
      if (response.code === 200) {
        message.success('更新支付订单成功')
        await fetchOrders()
        if (currentOrder.value?.id === id) {
          await fetchOrder(id)
        }
        return response.data
      }
    } catch (error) {
      console.error('更新支付订单失败:', error)
      message.error('更新支付订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 取消支付订单
  const cancelOrder = async (params: CancelOrderParams) => {
    try {
      const response = await paymentOrderApi.cancelPaymentOrder(params)
      
      if (response.code === 200) {
        message.success('取消支付订单成功')
        await fetchOrders()
      }
    } catch (error) {
      console.error('取消支付订单失败:', error)
      message.error('取消支付订单失败')
      throw error
    }
  }

  // 同步订单状态
  const syncOrderStatus = async (id: string) => {
    try {
      const response = await paymentOrderApi.syncOrderStatus(id)
      
      if (response.code === 200) {
        message.success('订单状态同步成功')
        await fetchOrders()
        return response.data
      }
    } catch (error) {
      console.error('订单状态同步失败:', error)
      message.error('订单状态同步失败')
      throw error
    }
  }

  // 批量同步订单状态
  const batchSyncOrderStatus = async (orderIds: string[]) => {
    try {
      loading.value = true
      const response = await paymentOrderApi.batchSyncOrderStatus(orderIds)
      
      if (response.code === 200) {
        message.success(`批量同步完成，共处理 ${orderIds.length} 个订单`)
        await fetchOrders()
        return response.data
      }
    } catch (error) {
      console.error('批量同步订单状态失败:', error)
      message.error('批量同步订单状态失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 导出支付订单
  const exportOrders = async (params: OrderExportParams) => {
    try {
      const response = await paymentOrderApi.exportPaymentOrders(params)
      
      if (response.code === 200) {
        // 下载文件
        const link = document.createElement('a')
        link.href = response.data.downloadUrl
        link.download = params.filename || `payment_orders_${Date.now()}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        message.success('订单导出成功')
      }
    } catch (error) {
      console.error('导出支付订单失败:', error)
      message.error('导出支付订单失败')
      throw error
    }
  }

  // 获取订单统计数据
  const fetchStatistics = async (dateRange?: { start: string; end: string }) => {
    try {
      const range = dateRange || {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      }
      
      const response = await paymentOrderApi.getOrderStatistics(range)
      
      if (response.code === 200) {
        statistics.value = response.data
      }
    } catch (error) {
      console.error('获取订单统计数据失败:', error)
    }
  }

  // 获取订单趋势数据
  const fetchTrendData = async (
    dateRange: { start: string; end: string },
    granularity: 'day' | 'hour' | 'month' = 'day'
  ) => {
    try {
      const response = await paymentOrderApi.getOrderTrend(dateRange, granularity)
      
      if (response.code === 200) {
        trendData.value = response.data
      }
    } catch (error) {
      console.error('获取订单趋势数据失败:', error)
    }
  }

  // 重新发起支付
  const retryPayment = async (id: string) => {
    try {
      const response = await paymentOrderApi.retryPayment(id)
      
      if (response.code === 200) {
        message.success('重新发起支付成功')
        await fetchOrders()
        return response.data
      }
    } catch (error) {
      console.error('重新发起支付失败:', error)
      message.error('重新发起支付失败')
      throw error
    }
  }

  // 获取支付二维码
  const getPaymentQrCode = async (id: string) => {
    try {
      const response = await paymentOrderApi.getPaymentQrCode(id)
      
      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('获取支付二维码失败:', error)
      message.error('获取支付二维码失败')
      throw error
    }
  }

  // 获取支付链接
  const getPaymentUrl = async (id: string) => {
    try {
      const response = await paymentOrderApi.getPaymentUrl(id)
      
      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('获取支付链接失败:', error)
      message.error('获取支付链接失败')
      throw error
    }
  }

  // 查询支付状态
  const queryPaymentStatus = async (id: string) => {
    try {
      const response = await paymentOrderApi.queryPaymentStatus(id)
      
      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    orders.value = []
    currentOrder.value = null
    statistics.value = {
      totalCount: 0,
      totalAmount: 0,
      successCount: 0,
      successAmount: 0,
      failedCount: 0,
      failedAmount: 0,
      pendingCount: 0,
      pendingAmount: 0,
      successRate: 0,
      dateRange: { start: '', end: '' }
    }
    trendData.value = []
    loading.value = false
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }

  return {
    // 状态
    orders,
    currentOrder,
    statistics,
    trendData,
    loading,
    pagination,
    
    // 计算属性
    successOrders,
    pendingOrders,
    failedOrders,
    totalAmount,
    
    // 方法
    fetchOrders,
    fetchOrder,
    createOrder,
    updateOrder,
    cancelOrder,
    syncOrderStatus,
    batchSyncOrderStatus,
    exportOrders,
    fetchStatistics,
    fetchTrendData,
    retryPayment,
    getPaymentQrCode,
    getPaymentUrl,
    queryPaymentStatus,
    resetState,
    setPagination
  }
})
