<!--
  优惠券管理组件
  @description 优惠券模板创建、发放、使用统计等功能
-->
<template>
  <div class="coupon-management">
    <!-- 统计卡片 -->
    <div class="coupon-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic
              title="优惠券模板"
              :value="couponStats.templateCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <TicketOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic
              title="已发放"
              :value="couponStats.issuedCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <SendOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic
              title="已使用"
              :value="couponStats.usedCount"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic
              title="使用率"
              :value="couponStats.useRate"
              :precision="2"
              suffix="%"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <PercentageOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="优惠券名称" name="name">
          <a-input
            v-model:value="searchForm.name"
            placeholder="请输入优惠券名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="优惠券类型" name="type">
          <a-select
            v-model:value="searchForm.type"
            placeholder="请选择优惠券类型"
            allow-clear
            style="width: 150px"
          >
            <a-select-option :value="1">满减券</a-select-option>
            <a-select-option :value="2">折扣券</a-select-option>
            <a-select-option :value="3">立减券</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="1">开启</a-select-option>
            <a-select-option :value="0">关闭</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <PlusOutlined />
          创建优惠券
        </a-button>
        <a-button @click="handleBatchIssue" :disabled="!hasSelected">
          <SendOutlined />
          批量发放
        </a-button>
        <a-button @click="handleExport">
          <ExportOutlined />
          导出
        </a-button>
      </a-space>
    </div>

    <!-- 优惠券表格 -->
    <a-table
      ref="tableRef"
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      :scroll="{ x: 1500 }"
      row-key="id"
      @change="handleTableChange"
    >
      <!-- 优惠券信息 -->
      <template #couponInfo="{ record }">
        <div class="coupon-info">
          <div class="coupon-name">{{ record.name }}</div>
          <div class="coupon-type">
            <a-tag :color="getCouponTypeColor(record.type)">
              {{ getCouponTypeText(record.type) }}
            </a-tag>
          </div>
          <div v-if="record.description" class="coupon-desc">
            {{ record.description }}
          </div>
        </div>
      </template>

      <!-- 优惠规则 -->
      <template #discountRule="{ record }">
        <div class="discount-rule">
          <div v-if="record.type === 1" class="rule-text">
            满{{ record.minPrice }}减{{ record.discountPrice }}
          </div>
          <div v-else-if="record.type === 2" class="rule-text">
            {{ record.discountPercent }}折
            <span v-if="record.discountLimitPrice">
              (最高减{{ record.discountLimitPrice }})
            </span>
          </div>
          <div v-else-if="record.type === 3" class="rule-text">
            立减{{ record.discountPrice }}
          </div>
        </div>
      </template>

      <!-- 有效期 -->
      <template #validity="{ record }">
        <div class="validity-info">
          <div v-if="record.validityType === 1" class="validity-text">
            {{ record.validStartTime }} 至 {{ record.validEndTime }}
          </div>
          <div v-else class="validity-text">
            领取后{{ record.fixedStartTerm }}天生效，
            有效期{{ record.fixedEndTerm }}天
          </div>
        </div>
      </template>

      <!-- 发放统计 -->
      <template #issueStats="{ record }">
        <div class="issue-stats">
          <div class="stats-item">
            <span class="label">总量:</span>
            <span class="value">{{ record.totalCount === -1 ? '无限制' : record.totalCount }}</span>
          </div>
          <div class="stats-item">
            <span class="label">已领:</span>
            <span class="value">{{ record.takeCount }}</span>
          </div>
          <div class="stats-item">
            <span class="label">已用:</span>
            <span class="value">{{ record.useCount }}</span>
          </div>
        </div>
      </template>

      <!-- 状态 -->
      <template #status="{ record }">
        <a-switch
          v-model:checked="record.status"
          :checked-value="1"
          :unchecked-value="0"
          @change="handleStatusChange(record)"
        />
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button type="link" size="small" @click="handleView(record)">
            查看
          </a-button>
          <a-button type="link" size="small" @click="handleEdit(record)">
            编辑
          </a-button>
          <a-dropdown>
            <a-button type="link" size="small">
              更多
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleIssue(record)">
                  <SendOutlined />
                  发放优惠券
                </a-menu-item>
                <a-menu-item @click="handleViewCoupons(record)">
                  <EyeOutlined />
                  查看优惠券
                </a-menu-item>
                <a-menu-item @click="handleCopy(record)">
                  <CopyOutlined />
                  复制
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item danger @click="handleDelete(record)">
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </a-table>

    <!-- 优惠券详情抽屉 -->
    <CouponDetailDrawer
      v-model:open="detailDrawerVisible"
      :template-id="currentTemplateId"
    />

    <!-- 优惠券编辑抽屉 -->
    <CouponEditDrawer
      v-model:open="editDrawerVisible"
      :template-id="currentTemplateId"
      @success="handleEditSuccess"
    />

    <!-- 优惠券发放弹窗 -->
    <CouponIssueModal
      v-model:open="issueModalVisible"
      :template-id="currentTemplateId"
      @success="handleIssueSuccess"
    />

    <!-- 优惠券列表抽屉 -->
    <CouponListDrawer
      v-model:open="couponListDrawerVisible"
      :template-id="currentTemplateId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  SendOutlined,
  ExportOutlined,
  DownOutlined,
  TicketOutlined,
  CheckCircleOutlined,
  PercentageOutlined,
  EyeOutlined,
  CopyOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'

// 模拟组件导入
const CouponDetailDrawer = defineAsyncComponent(() => import('./CouponDetailDrawer.vue'))
const CouponEditDrawer = defineAsyncComponent(() => import('./CouponEditDrawer.vue'))
const CouponIssueModal = defineAsyncComponent(() => import('./CouponIssueModal.vue'))
const CouponListDrawer = defineAsyncComponent(() => import('./CouponListDrawer.vue'))

// 响应式数据
const loading = ref(false)
const dataSource = ref<any[]>([])
const selectedRowKeys = ref<string[]>([])
const couponStats = ref<any>({})
const detailDrawerVisible = ref(false)
const editDrawerVisible = ref(false)
const issueModalVisible = ref(false)
const couponListDrawerVisible = ref(false)
const currentTemplateId = ref<number | null>(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '优惠券信息',
    key: 'couponInfo',
    width: 250,
    fixed: 'left',
    slots: { customRender: 'couponInfo' }
  },
  {
    title: '优惠规则',
    key: 'discountRule',
    width: 200,
    slots: { customRender: 'discountRule' }
  },
  {
    title: '有效期',
    key: 'validity',
    width: 200,
    slots: { customRender: 'validity' }
  },
  {
    title: '发放统计',
    key: 'issueStats',
    width: 150,
    slots: { customRender: 'issueStats' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

/**
 * 获取优惠券类型颜色
 */
const getCouponTypeColor = (type: number): string => {
  const colorMap: Record<number, string> = {
    1: 'red',     // 满减券
    2: 'blue',    // 折扣券
    3: 'green'    // 立减券
  }
  return colorMap[type] || 'default'
}

/**
 * 获取优惠券类型文本
 */
const getCouponTypeText = (type: number): string => {
  const textMap: Record<number, string> = {
    1: '满减券',
    2: '折扣券',
    3: '立减券'
  }
  return textMap[type] || '未知'
}

// 模拟数据加载函数
const loadData = async () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    dataSource.value = [
      {
        id: 1,
        name: '新用户专享券',
        type: 1,
        status: 1,
        totalCount: 1000,
        takeCount: 500,
        useCount: 200,
        minPrice: 100,
        discountPrice: 20,
        validityType: 1,
        validStartTime: '2024-01-01 00:00:00',
        validEndTime: '2024-12-31 23:59:59',
        createTime: '2024-01-01 10:00:00',
        description: '新用户注册专享优惠券'
      }
    ]
    pagination.total = 1
    loading.value = false
  }, 1000)
}

const loadCouponStats = async () => {
  couponStats.value = {
    templateCount: 10,
    issuedCount: 5000,
    usedCount: 2000,
    useRate: 40.00
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  loadData()
}

const handleAdd = () => {
  currentTemplateId.value = null
  editDrawerVisible.value = true
}

const handleView = (record: any) => {
  currentTemplateId.value = record.id
  detailDrawerVisible.value = true
}

const handleEdit = (record: any) => {
  currentTemplateId.value = record.id
  editDrawerVisible.value = true
}

const handleEditSuccess = () => {
  loadData()
  loadCouponStats()
}

const handleStatusChange = (record: any) => {
  message.success(`${record.status ? '启用' : '禁用'}成功`)
}

const handleIssue = (record: any) => {
  currentTemplateId.value = record.id
  issueModalVisible.value = true
}

const handleIssueSuccess = () => {
  loadData()
  loadCouponStats()
}

const handleViewCoupons = (record: any) => {
  currentTemplateId.value = record.id
  couponListDrawerVisible.value = true
}

const handleCopy = (record: any) => {
  message.success('复制成功')
}

const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除优惠券"${record.name}"吗？`,
    onOk: () => {
      message.success('删除成功')
      loadData()
      loadCouponStats()
    }
  })
}

const handleBatchIssue = () => {
  message.info('批量发放功能开发中...')
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

// 页面加载时初始化数据
onMounted(() => {
  loadData()
  loadCouponStats()
})
</script>

<style scoped lang="less">
.coupon-management {
  .coupon-stats {
    margin-bottom: 24px;
    
    .stat-card {
      text-align: center;
    }
  }
  
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .action-buttons {
    margin-bottom: 16px;
  }
  
  .coupon-info {
    .coupon-name {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }
    
    .coupon-type {
      margin-bottom: 4px;
    }
    
    .coupon-desc {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
  
  .discount-rule {
    .rule-text {
      font-weight: 500;
      color: #f5222d;
    }
  }
  
  .validity-info {
    .validity-text {
      font-size: 12px;
      color: #595959;
      line-height: 1.4;
    }
  }
  
  .issue-stats {
    .stats-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px;
      font-size: 12px;
      
      .label {
        color: #8c8c8c;
      }
      
      .value {
        color: #262626;
        font-weight: 500;
      }
    }
  }
}
</style>
