<!--
  营销活动管理页面
  @description 优惠券、满减、秒杀、拼团等营销活动管理
-->
<template>
  <div class="promotion-activity">
    <a-card :bordered="false">
      <!-- 活动类型标签页 -->
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="coupon" tab="优惠券">
          <CouponManagement />
        </a-tab-pane>
        <a-tab-pane key="discount" tab="满减活动">
          <DiscountManagement />
        </a-tab-pane>
        <a-tab-pane key="seckill" tab="秒杀活动">
          <SeckillManagement />
        </a-tab-pane>
        <a-tab-pane key="combination" tab="拼团活动">
          <CombinationManagement />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CouponManagement from './components/CouponManagement.vue'
import DiscountManagement from './components/DiscountManagement.vue'
import SeckillManagement from './components/SeckillManagement.vue'
import CombinationManagement from './components/CombinationManagement.vue'

// 当前活跃的标签页
const activeTab = ref('coupon')

/**
 * 标签页切换处理
 */
const handleTabChange = (key: string) => {
  activeTab.value = key
}
</script>

<style scoped lang="less">
.promotion-activity {
  .ant-tabs {
    .ant-tabs-content-holder {
      padding: 0;
    }
  }
}
</style>
