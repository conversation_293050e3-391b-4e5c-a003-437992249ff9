<template>
  <div class="notification-list">
    <!-- 搜索表单 -->
    <div class="search-form">
      <a-card :bordered="false" class="search-card">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          layout="inline"
          class="search-form-content"
        >
          <a-form-item label="通知标题" name="title">
            <a-input
              v-model:value="searchForm.title"
              placeholder="请输入通知标题"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="事件类型" name="eventType">
            <a-select
              v-model:value="searchForm.eventType"
              placeholder="请选择事件类型"
              allow-clear
              style="width: 150px"
            >
              <a-select-option :value="1">商品发货</a-select-option>
              <a-select-option :value="2">商品退货</a-select-option>
              <a-select-option :value="3">商品上架</a-select-option>
              <a-select-option :value="4">商品下架</a-select-option>
              <a-select-option :value="5">取消供应</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="通知渠道" name="channelType">
            <a-select
              v-model:value="searchForm.channelType"
              placeholder="请选择通知渠道"
              allow-clear
              style="width: 150px"
            >
              <a-select-option :value="1">站内消息</a-select-option>
              <a-select-option :value="2">邮件</a-select-option>
              <a-select-option :value="3">短信</a-select-option>
              <a-select-option :value="4">微信推送</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="发送状态" name="sendStatus">
            <a-select
              v-model:value="searchForm.sendStatus"
              placeholder="请选择发送状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option :value="0">待发送</a-select-option>
              <a-select-option :value="1">发送中</a-select-option>
              <a-select-option :value="2">发送成功</a-select-option>
              <a-select-option :value="3">发送失败</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="创建时间" name="createTime">
            <a-range-picker
              v-model:value="searchForm.createTime"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              style="width: 300px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <a-card :bordered="false">
        <a-space>
          <a-button type="primary" @click="handleSendNotification">
            <template #icon><PlusOutlined /></template>
            发送通知
          </a-button>
          <a-button 
            type="default" 
            :disabled="!hasSelected"
            @click="handleBatchMarkRead"
          >
            <template #icon><CheckOutlined /></template>
            批量标记已读
          </a-button>
          <a-button 
            type="default" 
            :disabled="!hasSelected"
            @click="handleBatchResend"
          >
            <template #icon><RedoOutlined /></template>
            批量重发
          </a-button>
          <a-button type="default" @click="handleExport">
            <template #icon><ExportOutlined /></template>
            导出
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 通知列表 -->
    <div class="notification-table">
      <a-card :bordered="false">
        <a-table
          ref="tableRef"
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
        >
          <!-- 事件类型 -->
          <template #eventType="{ record }">
            <a-tag :color="getEventTypeColor(record.eventType)">
              {{ getEventTypeName(record.eventType) }}
            </a-tag>
          </template>
          
          <!-- 通知渠道 -->
          <template #channelType="{ record }">
            <a-tag :color="getChannelTypeColor(record.channelType)">
              {{ getChannelTypeName(record.channelType) }}
            </a-tag>
          </template>
          
          <!-- 发送状态 -->
          <template #sendStatus="{ record }">
            <a-badge 
              :status="getSendStatusBadge(record.sendStatus)" 
              :text="getSendStatusName(record.sendStatus)"
            />
          </template>
          
          <!-- 读取状态 -->
          <template #readStatus="{ record }">
            <a-tag :color="record.readStatus === 1 ? 'green' : 'orange'">
              {{ record.readStatus === 1 ? '已读' : '未读' }}
            </a-tag>
          </template>
          
          <!-- 优先级 -->
          <template #priorityLevel="{ record }">
            <a-tag :color="getPriorityColor(record.priorityLevel)">
              {{ getPriorityName(record.priorityLevel) }}
            </a-tag>
          </template>
          
          <!-- 操作 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button 
                v-if="record.readStatus === 0"
                type="link" 
                size="small" 
                @click="handleMarkRead(record)"
              >
                标记已读
              </a-button>
              <a-button 
                v-if="record.sendStatus === 3"
                type="link" 
                size="small" 
                @click="handleResend(record)"
              >
                重新发送
              </a-button>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 发送通知弹窗 -->
    <NotificationSendModal
      v-model:visible="sendModalVisible"
      @success="handleSendSuccess"
    />

    <!-- 通知详情弹窗 -->
    <NotificationDetailModal
      v-model:visible="detailModalVisible"
      :notification="currentNotification"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  CheckOutlined,
  RedoOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { notificationApi } from '@/api/notification'
import NotificationSendModal from './components/NotificationSendModal.vue'
import NotificationDetailModal from './components/NotificationDetailModal.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRowKeys = ref<number[]>([])
const sendModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentNotification = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  eventType: undefined,
  channelType: undefined,
  sendStatus: undefined,
  createTime: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '通知编码',
    dataIndex: 'notificationCode',
    width: 180,
    fixed: 'left'
  },
  {
    title: '通知标题',
    dataIndex: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    width: 120,
    slots: { customRender: 'eventType' }
  },
  {
    title: '通知渠道',
    dataIndex: 'channelType',
    width: 120,
    slots: { customRender: 'channelType' }
  },
  {
    title: '目标对象',
    dataIndex: 'targetInfo',
    width: 150,
    customRender: ({ record }) => {
      const targetInfo = record.targetInfo || {}
      return targetInfo.name || `${record.targetType}:${record.targetId}`
    }
  },
  {
    title: '发送状态',
    dataIndex: 'sendStatus',
    width: 120,
    slots: { customRender: 'sendStatus' }
  },
  {
    title: '读取状态',
    dataIndex: 'readStatus',
    width: 100,
    slots: { customRender: 'readStatus' }
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevel',
    width: 100,
    slots: { customRender: 'priorityLevel' }
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    width: 180,
    customRender: ({ text }) => text || '-'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 方法定义
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const { data } = await notificationApi.getNotificationPage(params)
    
    tableData.value = data.list
    pagination.total = data.total
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    eventType: undefined,
    channelType: undefined,
    sendStatus: undefined,
    createTime: undefined
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleSendNotification = () => {
  sendModalVisible.value = true
}

const handleSendSuccess = () => {
  message.success('通知发送成功')
  loadData()
}

const handleView = (record: any) => {
  currentNotification.value = record
  detailModalVisible.value = true
}

const handleMarkRead = async (record: any) => {
  try {
    await notificationApi.markAsRead(record.id)
    message.success('标记已读成功')
    loadData()
  } catch (error) {
    message.error('标记已读失败')
  }
}

const handleResend = async (record: any) => {
  try {
    await notificationApi.resendNotification(record.id)
    message.success('重新发送成功')
    loadData()
  } catch (error) {
    message.error('重新发送失败')
  }
}

const handleBatchMarkRead = () => {
  Modal.confirm({
    title: '确认操作',
    content: `确定要将选中的 ${selectedRowKeys.value.length} 条通知标记为已读吗？`,
    onOk: async () => {
      try {
        await notificationApi.markAsReadBatch(selectedRowKeys.value)
        message.success('批量标记已读成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量标记已读失败')
      }
    }
  })
}

const handleBatchResend = () => {
  Modal.confirm({
    title: '确认操作',
    content: `确定要重新发送选中的 ${selectedRowKeys.value.length} 条通知吗？`,
    onOk: async () => {
      try {
        await notificationApi.resendNotificationBatch(selectedRowKeys.value)
        message.success('批量重新发送成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量重新发送失败')
      }
    }
  })
}

const handleExport = async () => {
  try {
    const params = { ...searchForm }
    await notificationApi.exportNotificationExcel(params)
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 辅助方法
const getEventTypeName = (type: number) => {
  const map = { 1: '商品发货', 2: '商品退货', 3: '商品上架', 4: '商品下架', 5: '取消供应' }
  return map[type] || '未知'
}

const getEventTypeColor = (type: number) => {
  const map = { 1: 'blue', 2: 'orange', 3: 'green', 4: 'red', 5: 'purple' }
  return map[type] || 'default'
}

const getChannelTypeName = (type: number) => {
  const map = { 1: '站内消息', 2: '邮件', 3: '短信', 4: '微信推送' }
  return map[type] || '未知'
}

const getChannelTypeColor = (type: number) => {
  const map = { 1: 'cyan', 2: 'blue', 3: 'green', 4: 'orange' }
  return map[type] || 'default'
}

const getSendStatusName = (status: number) => {
  const map = { 0: '待发送', 1: '发送中', 2: '发送成功', 3: '发送失败' }
  return map[status] || '未知'
}

const getSendStatusBadge = (status: number) => {
  const map = { 0: 'default', 1: 'processing', 2: 'success', 3: 'error' }
  return map[status] || 'default'
}

const getPriorityName = (level: number) => {
  const map = { 1: '低', 2: '中', 3: '高', 4: '紧急' }
  return map[level] || '未知'
}

const getPriorityColor = (level: number) => {
  const map = { 1: 'default', 2: 'blue', 3: 'orange', 4: 'red' }
  return map[level] || 'default'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.notification-list {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-form-content {
  padding: 16px 0;
}

.action-bar {
  margin-bottom: 16px;
}

.notification-table {
  background: #fff;
}
</style>
