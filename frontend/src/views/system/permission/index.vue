<!--
  权限管理主页面
  功能：权限列表展示、搜索、新增、编辑、删除、树形结构展示
-->
<template>
  <div class="permission-management">
    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="权限编码/权限名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="权限类型">
          <a-select
            v-model:value="searchForm.permissionType"
            placeholder="请选择权限类型"
            allow-clear
            style="width: 150px"
          >
            <a-select-option
              v-for="option in PERMISSION_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in PERMISSION_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 权限列表 -->
    <a-card :bordered="false">
      <template #title>
        <a-space>
          <span>权限列表</span>
          <a-switch
            v-model:checked="treeMode"
            checked-children="树形"
            un-checked-children="列表"
            @change="handleViewModeChange"
          />
        </a-space>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增权限
          </a-button>
          <a-button
            type="primary"
            danger
            :disabled="!hasSelected"
            @click="handleBatchDelete"
          >
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
        </a-space>
      </template>

      <!-- 树形模式 -->
      <a-tree
        v-if="treeMode"
        :tree-data="permissionTree"
        :field-names="{ children: 'children', title: 'permissionName', key: 'id' }"
        show-line
        :show-icon="false"
        :selectable="false"
        :checkable="true"
        v-model:checkedKeys="selectedRowKeys"
        @check="handleTreeCheck"
      >
        <template #title="{ permissionCode, permissionName, permissionType, status, isSystem }">
          <div class="tree-node-content">
            <span class="permission-info">
              <a-tag :color="getPermissionTypeColor(permissionType)" size="small">
                {{ getPermissionTypeDesc(permissionType) }}
              </a-tag>
              <span class="permission-name">{{ permissionName }}</span>
              <span class="permission-code">{{ permissionCode }}</span>
            </span>
            <span class="permission-actions">
              <a-tag v-if="isSystem" color="red" size="small">系统</a-tag>
              <a-tag :color="getPermissionStatusColor(status)" size="small">
                {{ getPermissionStatusDesc(status) }}
              </a-tag>
              <a-space size="small">
                <a-button type="link" size="small" @click="handleView({ id, permissionCode, permissionName, permissionType, status, isSystem })">
                  <EyeOutlined />
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  :disabled="isSystem"
                  @click="handleEdit({ id, permissionCode, permissionName, permissionType, status, isSystem })"
                >
                  <EditOutlined />
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  danger
                  :disabled="isSystem"
                  @click="handleDelete({ id, permissionCode, permissionName, permissionType, status, isSystem })"
                >
                  <DeleteOutlined />
                </a-button>
              </a-space>
            </span>
          </div>
        </template>
      </a-tree>

      <!-- 表格模式 -->
      <a-table
        v-else
        :columns="columns"
        :data-source="permissionList"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 权限类型 -->
        <template #permissionType="{ record }">
          <a-tag :color="getPermissionTypeColor(record.permissionType)">
            {{ getPermissionTypeDesc(record.permissionType) }}
          </a-tag>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getPermissionStatusColor(record.status)">
            {{ getPermissionStatusDesc(record.status) }}
          </a-tag>
        </template>

        <!-- 系统标识 -->
        <template #isSystem="{ record }">
          <a-tag v-if="record.isSystem" color="red">系统</a-tag>
          <a-tag v-else color="blue">自定义</a-tag>
        </template>

        <!-- 创建时间 -->
        <template #createTime="{ record }">
          {{ formatDateTime(record.createTime) }}
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              <template #icon><EyeOutlined /></template>
              查看
            </a-button>
            <a-button
              type="link"
              size="small"
              :disabled="record.isSystem"
              @click="handleEdit(record)"
            >
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-button
              type="link"
              size="small"
              danger
              :disabled="record.isSystem"
              @click="handleDelete(record)"
            >
              <template #icon><DeleteOutlined /></template>
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 权限表单模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="权限编码" name="permissionCode">
          <a-input
            v-model:value="formData.permissionCode"
            placeholder="格式：resource:action，如 user:create"
            :disabled="isEdit"
          />
          <div class="form-help-text">
            权限编码格式：资源:动作，例如 user:create、role:edit
          </div>
        </a-form-item>
        <a-form-item label="权限名称" name="permissionName">
          <a-input v-model:value="formData.permissionName" placeholder="请输入权限名称" />
        </a-form-item>
        <a-form-item label="权限类型" name="permissionType">
          <a-select v-model:value="formData.permissionType" placeholder="请选择权限类型">
            <a-select-option
              v-for="option in PERMISSION_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="父权限">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="parentPermissionOptions"
            :field-names="{ children: 'children', label: 'permissionName', value: 'id' }"
            placeholder="请选择父权限（可选）"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        <a-form-item label="资源类型">
          <a-input v-model:value="formData.resourceType" placeholder="请输入资源类型" />
        </a-form-item>
        <a-form-item label="资源路径">
          <a-input v-model:value="formData.resourcePath" placeholder="请输入资源路径" />
        </a-form-item>
        <a-form-item label="操作动作">
          <a-input v-model:value="formData.action" placeholder="请输入操作动作" />
        </a-form-item>
        <a-form-item label="是否系统内置">
          <a-switch v-model:checked="formData.isSystem" />
        </a-form-item>
        <a-form-item label="排序" name="sortOrder">
          <a-input-number
            v-model:value="formData.sortOrder"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="权限描述">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入权限描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons-vue'
import PermissionApi from '@/api/permission'
import type {
  Permission,
  PermissionCreateRequest,
  PermissionUpdateRequest,
  PermissionQueryParams,
  PermissionType
} from '@/types/permission'
import {
  PERMISSION_TYPE_OPTIONS,
  PERMISSION_STATUS_OPTIONS,
  getPermissionTypeDesc,
  getPermissionStatusDesc,
  getPermissionStatusColor,
  convertToTreeNode
} from '@/types/permission'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const permissionList = ref<Permission[]>([])
const permissionTree = ref<any[]>([])
const parentPermissionOptions = ref<any[]>([])
const selectedRowKeys = ref<number[]>([])
const treeMode = ref(true) // 默认树形模式

// 搜索表单
const searchForm = reactive<PermissionQueryParams>({
  keyword: '',
  permissionType: undefined,
  status: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '权限编码',
    dataIndex: 'permissionCode',
    key: 'permissionCode',
    width: 200
  },
  {
    title: '权限名称',
    dataIndex: 'permissionName',
    key: 'permissionName',
    width: 150
  },
  {
    title: '权限类型',
    dataIndex: 'permissionType',
    key: 'permissionType',
    width: 120,
    slots: { customRender: 'permissionType' }
  },
  {
    title: '资源类型',
    dataIndex: 'resourceType',
    key: 'resourceType',
    width: 120
  },
  {
    title: '操作动作',
    dataIndex: 'action',
    key: 'action',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '系统标识',
    dataIndex: 'isSystem',
    key: 'isSystem',
    width: 100,
    slots: { customRender: 'isSystem' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
    slots: { customRender: 'createTime' }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Permission) => ({
    disabled: record.isSystem
  })
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 模态框相关
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<PermissionCreateRequest>({
  permissionCode: '',
  permissionName: '',
  permissionType: 1, // 默认API权限
  parentId: undefined,
  resourceType: '',
  resourcePath: '',
  action: '',
  isSystem: false,
  sortOrder: 0,
  description: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  permissionCode: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]+$/, message: '权限编码格式：resource:action', trigger: 'blur' }
  ],
  permissionName: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { max: 100, message: '权限名称不能超过100个字符', trigger: 'blur' }
  ],
  permissionType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 计算模态框标题
const modalTitle = computed(() => {
  return isEdit.value ? '编辑权限' : '新增权限'
})

// 获取权限类型颜色
function getPermissionTypeColor(permissionType: PermissionType): string {
  const colorMap = {
    1: 'blue',    // API
    2: 'green',   // MENU
    3: 'orange',  // BUTTON
    4: 'purple'   // DATA
  }
  return colorMap[permissionType] || 'default'
}

// 加载权限列表
async function loadPermissionList() {
  try {
    loading.value = true

    if (treeMode.value) {
      // 树形模式：加载所有权限并构建树结构
      const response = await PermissionApi.getTree()
      if (response.success && response.data) {
        permissionTree.value = convertToTreeNode(response.data)
      }
    } else {
      // 列表模式：分页加载
      const params = {
        ...searchForm,
        page: pagination.current,
        size: pagination.pageSize
      }

      const response = await PermissionApi.getList(params)
      if (response.success && response.data) {
        permissionList.value = response.data.records || []
        pagination.total = response.data.total || 0
      }
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
    message.error('加载权限列表失败')
  } finally {
    loading.value = false
  }
}

// 加载父权限选项
async function loadParentPermissionOptions() {
  try {
    const response = await PermissionApi.getTree()
    if (response.success && response.data) {
      parentPermissionOptions.value = convertToTreeNode(response.data)
    }
  } catch (error) {
    console.error('加载父权限选项失败:', error)
  }
}

// 搜索处理
function handleSearch() {
  pagination.current = 1
  loadPermissionList()
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    permissionType: undefined,
    status: undefined,
    page: 1,
    size: 10
  })
  pagination.current = 1
  loadPermissionList()
}

// 视图模式切换
function handleViewModeChange(checked: boolean) {
  treeMode.value = checked
  selectedRowKeys.value = []
  loadPermissionList()
}

// 树形选择处理
function handleTreeCheck(checkedKeys: any) {
  selectedRowKeys.value = checkedKeys.checked || checkedKeys
}

// 表格变化处理
function handleTableChange(pag: any) {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPermissionList()
}

// 新增权限
function handleCreate() {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑权限
function handleEdit(record: Permission) {
  isEdit.value = true
  Object.assign(formData, {
    id: record.id,
    permissionCode: record.permissionCode,
    permissionName: record.permissionName,
    permissionType: record.permissionType,
    parentId: record.parentId,
    resourceType: record.resourceType || '',
    resourcePath: record.resourcePath || '',
    action: record.action || '',
    isSystem: record.isSystem,
    sortOrder: record.sortOrder || 0,
    description: record.description || '',
    remark: record.remark || '',
    version: record.version
  })
  modalVisible.value = true
}

// 查看权限详情
function handleView(record: Permission) {
  // TODO: 实现查看详情功能
  message.info('查看功能待实现')
}

// 删除权限
function handleDelete(record: Permission) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除权限"${record.permissionName}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await PermissionApi.delete(record.id!)
        message.success('删除成功')
        loadPermissionList()
      } catch (error) {
        console.error('删除权限失败:', error)
        message.error('删除权限失败')
      }
    }
  })
}

// 批量删除
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的权限')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个权限吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await PermissionApi.batchDelete(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadPermissionList()
      } catch (error) {
        console.error('批量删除权限失败:', error)
        message.error('批量删除权限失败')
      }
    }
  })
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      // 更新权限
      await PermissionApi.update(formData.id!, formData as PermissionUpdateRequest)
      message.success('更新成功')
    } else {
      // 创建权限
      await PermissionApi.create(formData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadPermissionList()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
function handleCancel() {
  modalVisible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    permissionCode: '',
    permissionName: '',
    permissionType: 1,
    parentId: undefined,
    resourceType: '',
    resourcePath: '',
    action: '',
    isSystem: false,
    sortOrder: 0,
    description: '',
    remark: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPermissionList()
  loadParentPermissionOptions()
})
</script>

<style scoped lang="less">
.permission-management {
  .search-card {
    margin-bottom: 16px;
  }

  .tree-node-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 4px 0;

    .permission-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .permission-name {
        font-weight: 500;
      }

      .permission-code {
        color: #666;
        font-size: 12px;
        font-family: monospace;
      }
    }

    .permission-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .ant-table {
    .ant-tag {
      margin: 0;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .form-help-text {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
  }
}
</style>
