<!--
  日志管理组件
  @description 系统操作日志、错误日志、访问日志的查看和管理
-->
<template>
  <div class="log-management">
    <!-- 日志类型标签页 -->
    <a-tabs v-model:activeKey="logType" @change="handleLogTypeChange">
      <!-- 操作日志 -->
      <a-tab-pane key="operate" tab="操作日志">
        <div class="log-section">
          <!-- 搜索表单 -->
          <div class="search-form">
            <a-form
              ref="operateSearchFormRef"
              :model="operateSearchForm"
              layout="inline"
              @finish="handleOperateSearch"
            >
              <a-form-item label="操作模块" name="module">
                <a-input
                  v-model:value="operateSearchForm.module"
                  placeholder="请输入操作模块"
                  allow-clear
                  style="width: 150px"
                />
              </a-form-item>
              
              <a-form-item label="操作类型" name="type">
                <a-select
                  v-model:value="operateSearchForm.type"
                  placeholder="请选择操作类型"
                  allow-clear
                  style="width: 120px"
                >
                  <a-select-option :value="1">新增</a-select-option>
                  <a-select-option :value="2">修改</a-select-option>
                  <a-select-option :value="3">删除</a-select-option>
                  <a-select-option :value="4">查询</a-select-option>
                  <a-select-option :value="5">导出</a-select-option>
                  <a-select-option :value="6">导入</a-select-option>
                  <a-select-option :value="7">其他</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="操作人" name="userId">
                <a-input
                  v-model:value="operateSearchForm.userId"
                  placeholder="请输入用户ID"
                  allow-clear
                  style="width: 120px"
                />
              </a-form-item>
              
              <a-form-item label="操作时间" name="operateTime">
                <a-range-picker
                  v-model:value="operateSearchForm.operateTime"
                  style="width: 240px"
                />
              </a-form-item>
              
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleOperateReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-space>
              <a-button @click="handleOperateExport">
                <ExportOutlined />
                导出日志
              </a-button>
              <a-button @click="handleOperateClean" danger>
                <DeleteOutlined />
                清理日志
              </a-button>
            </a-space>
          </div>

          <!-- 操作日志表格 -->
          <a-table
            :columns="operateColumns"
            :data-source="operateDataSource"
            :loading="operateLoading"
            :pagination="operatePagination"
            :scroll="{ x: 1200 }"
            row-key="id"
            @change="handleOperateTableChange"
          >
            <!-- 操作类型 -->
            <template #operateType="{ record }">
              <a-tag :color="getOperateTypeColor(record.type)">
                {{ getOperateTypeText(record.type) }}
              </a-tag>
            </template>

            <!-- 执行时长 -->
            <template #duration="{ record }">
              <span :class="getDurationClass(record.duration)">
                {{ record.duration }}ms
              </span>
            </template>

            <!-- 结果状态 -->
            <template #resultCode="{ record }">
              <a-tag :color="record.resultCode === 0 ? 'green' : 'red'">
                {{ record.resultCode === 0 ? '成功' : '失败' }}
              </a-tag>
            </template>

            <!-- 操作 -->
            <template #action="{ record }">
              <a-button type="link" size="small" @click="handleViewOperateDetail(record)">
                查看详情
              </a-button>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 错误日志 -->
      <a-tab-pane key="error" tab="错误日志">
        <div class="log-section">
          <!-- 搜索表单 -->
          <div class="search-form">
            <a-form
              ref="errorSearchFormRef"
              :model="errorSearchForm"
              layout="inline"
              @finish="handleErrorSearch"
            >
              <a-form-item label="应用名称" name="applicationName">
                <a-input
                  v-model:value="errorSearchForm.applicationName"
                  placeholder="请输入应用名称"
                  allow-clear
                  style="width: 150px"
                />
              </a-form-item>
              
              <a-form-item label="异常类型" name="exceptionName">
                <a-input
                  v-model:value="errorSearchForm.exceptionName"
                  placeholder="请输入异常类型"
                  allow-clear
                  style="width: 200px"
                />
              </a-form-item>
              
              <a-form-item label="处理状态" name="processStatus">
                <a-select
                  v-model:value="errorSearchForm.processStatus"
                  placeholder="请选择处理状态"
                  allow-clear
                  style="width: 120px"
                >
                  <a-select-option :value="0">未处理</a-select-option>
                  <a-select-option :value="1">已处理</a-select-option>
                  <a-select-option :value="2">已忽略</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="异常时间" name="exceptionTime">
                <a-range-picker
                  v-model:value="errorSearchForm.exceptionTime"
                  style="width: 240px"
                />
              </a-form-item>
              
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleErrorReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-space>
              <a-button @click="handleErrorExport">
                <ExportOutlined />
                导出日志
              </a-button>
              <a-button @click="handleErrorClean" danger>
                <DeleteOutlined />
                清理日志
              </a-button>
            </a-space>
          </div>

          <!-- 错误日志表格 -->
          <a-table
            :columns="errorColumns"
            :data-source="errorDataSource"
            :loading="errorLoading"
            :pagination="errorPagination"
            :scroll="{ x: 1400 }"
            row-key="id"
            @change="handleErrorTableChange"
          >
            <!-- 处理状态 -->
            <template #processStatus="{ record }">
              <a-tag :color="getProcessStatusColor(record.processStatus)">
                {{ getProcessStatusText(record.processStatus) }}
              </a-tag>
            </template>

            <!-- 操作 -->
            <template #action="{ record }">
              <a-space>
                <a-button type="link" size="small" @click="handleViewErrorDetail(record)">
                  查看详情
                </a-button>
                <a-button
                  v-if="record.processStatus === 0"
                  type="link"
                  size="small"
                  @click="handleProcessError(record)"
                >
                  标记处理
                </a-button>
              </a-space>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 访问日志 -->
      <a-tab-pane key="access" tab="访问日志">
        <div class="log-section">
          <!-- 搜索表单 -->
          <div class="search-form">
            <a-form
              ref="accessSearchFormRef"
              :model="accessSearchForm"
              layout="inline"
              @finish="handleAccessSearch"
            >
              <a-form-item label="请求地址" name="requestUrl">
                <a-input
                  v-model:value="accessSearchForm.requestUrl"
                  placeholder="请输入请求地址"
                  allow-clear
                  style="width: 200px"
                />
              </a-form-item>
              
              <a-form-item label="请求方法" name="requestMethod">
                <a-select
                  v-model:value="accessSearchForm.requestMethod"
                  placeholder="请选择请求方法"
                  allow-clear
                  style="width: 120px"
                >
                  <a-select-option value="GET">GET</a-select-option>
                  <a-select-option value="POST">POST</a-select-option>
                  <a-select-option value="PUT">PUT</a-select-option>
                  <a-select-option value="DELETE">DELETE</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="用户IP" name="userIp">
                <a-input
                  v-model:value="accessSearchForm.userIp"
                  placeholder="请输入用户IP"
                  allow-clear
                  style="width: 150px"
                />
              </a-form-item>
              
              <a-form-item label="访问时间" name="accessTime">
                <a-range-picker
                  v-model:value="accessSearchForm.accessTime"
                  style="width: 240px"
                />
              </a-form-item>
              
              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleAccessReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-space>
              <a-button @click="handleAccessExport">
                <ExportOutlined />
                导出日志
              </a-button>
              <a-button @click="handleAccessClean" danger>
                <DeleteOutlined />
                清理日志
              </a-button>
            </a-space>
          </div>

          <!-- 访问日志表格 -->
          <a-table
            :columns="accessColumns"
            :data-source="accessDataSource"
            :loading="accessLoading"
            :pagination="accessPagination"
            :scroll="{ x: 1200 }"
            row-key="id"
            @change="handleAccessTableChange"
          >
            <!-- 请求方法 -->
            <template #requestMethod="{ record }">
              <a-tag :color="getMethodColor(record.requestMethod)">
                {{ record.requestMethod }}
              </a-tag>
            </template>

            <!-- 执行时长 -->
            <template #duration="{ record }">
              <span :class="getDurationClass(record.duration)">
                {{ record.duration }}ms
              </span>
            </template>

            <!-- 结果状态 -->
            <template #resultCode="{ record }">
              <a-tag :color="record.resultCode === 200 ? 'green' : 'red'">
                {{ record.resultCode }}
              </a-tag>
            </template>

            <!-- 操作 -->
            <template #action="{ record }">
              <a-button type="link" size="small" @click="handleViewAccessDetail(record)">
                查看详情
              </a-button>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 日志详情弹窗 -->
    <LogDetailModal
      v-model:open="detailModalVisible"
      :log-type="currentLogType"
      :log-data="currentLogData"
    />

    <!-- 错误处理弹窗 -->
    <ErrorProcessModal
      v-model:open="processModalVisible"
      :error-log="currentErrorLog"
      @success="handleProcessSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import LogDetailModal from './LogDetailModal.vue'
import ErrorProcessModal from './ErrorProcessModal.vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'

// 响应式数据
const logType = ref('operate')
const detailModalVisible = ref(false)
const processModalVisible = ref(false)
const currentLogType = ref('')
const currentLogData = ref<any>(null)
const currentErrorLog = ref<any>(null)

// 操作日志相关
const operateLoading = ref(false)
const operateDataSource = ref<any[]>([])
const operateSearchFormRef = ref()
const operateSearchForm = reactive({
  module: '',
  type: undefined,
  userId: '',
  operateTime: undefined
})
const operatePagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 错误日志相关
const errorLoading = ref(false)
const errorDataSource = ref<any[]>([])
const errorSearchFormRef = ref()
const errorSearchForm = reactive({
  applicationName: '',
  exceptionName: '',
  processStatus: undefined,
  exceptionTime: undefined
})
const errorPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 访问日志相关
const accessLoading = ref(false)
const accessDataSource = ref<any[]>([])
const accessSearchFormRef = ref()
const accessSearchForm = reactive({
  requestUrl: '',
  requestMethod: undefined,
  userIp: '',
  accessTime: undefined
})
const accessPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const operateColumns: TableColumnsType = [
  { title: '操作模块', dataIndex: 'module', width: 120 },
  { title: '操作名称', dataIndex: 'name', width: 150 },
  { title: '操作类型', key: 'type', width: 100, slots: { customRender: 'operateType' } },
  { title: '操作人', dataIndex: 'userName', width: 100 },
  { title: '用户IP', dataIndex: 'userIp', width: 120 },
  { title: '执行时长', key: 'duration', width: 100, slots: { customRender: 'duration' } },
  { title: '结果状态', key: 'resultCode', width: 100, slots: { customRender: 'resultCode' } },
  { title: '操作时间', dataIndex: 'startTime', width: 180 },
  { title: '操作', key: 'action', width: 100, fixed: 'right', slots: { customRender: 'action' } }
]

const errorColumns: TableColumnsType = [
  { title: '应用名称', dataIndex: 'applicationName', width: 120 },
  { title: '异常类型', dataIndex: 'exceptionName', width: 200 },
  { title: '异常消息', dataIndex: 'exceptionMessage', width: 300, ellipsis: true },
  { title: '用户IP', dataIndex: 'userIp', width: 120 },
  { title: '处理状态', key: 'processStatus', width: 100, slots: { customRender: 'processStatus' } },
  { title: '异常时间', dataIndex: 'exceptionTime', width: 180 },
  { title: '操作', key: 'action', width: 150, fixed: 'right', slots: { customRender: 'action' } }
]

const accessColumns: TableColumnsType = [
  { title: '请求方法', key: 'requestMethod', width: 100, slots: { customRender: 'requestMethod' } },
  { title: '请求地址', dataIndex: 'requestUrl', width: 300, ellipsis: true },
  { title: '用户IP', dataIndex: 'userIp', width: 120 },
  { title: '执行时长', key: 'duration', width: 100, slots: { customRender: 'duration' } },
  { title: '结果状态', key: 'resultCode', width: 100, slots: { customRender: 'resultCode' } },
  { title: '访问时间', dataIndex: 'startTime', width: 180 },
  { title: '操作', key: 'action', width: 100, fixed: 'right', slots: { customRender: 'action' } }
]

/**
 * 获取操作类型颜色
 */
const getOperateTypeColor = (type: number): string => {
  const colorMap: Record<number, string> = {
    1: 'green',   // 新增
    2: 'blue',    // 修改
    3: 'red',     // 删除
    4: 'default', // 查询
    5: 'orange',  // 导出
    6: 'purple',  // 导入
    7: 'cyan'     // 其他
  }
  return colorMap[type] || 'default'
}

/**
 * 获取操作类型文本
 */
const getOperateTypeText = (type: number): string => {
  const textMap: Record<number, string> = {
    1: '新增',
    2: '修改',
    3: '删除',
    4: '查询',
    5: '导出',
    6: '导入',
    7: '其他'
  }
  return textMap[type] || '未知'
}

/**
 * 获取处理状态颜色
 */
const getProcessStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'red',     // 未处理
    1: 'green',   // 已处理
    2: 'orange'   // 已忽略
  }
  return colorMap[status] || 'default'
}

/**
 * 获取处理状态文本
 */
const getProcessStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    0: '未处理',
    1: '已处理',
    2: '已忽略'
  }
  return textMap[status] || '未知'
}

/**
 * 获取请求方法颜色
 */
const getMethodColor = (method: string): string => {
  const colorMap: Record<string, string> = {
    'GET': 'blue',
    'POST': 'green',
    'PUT': 'orange',
    'DELETE': 'red'
  }
  return colorMap[method] || 'default'
}

/**
 * 获取执行时长样式类
 */
const getDurationClass = (duration: number): string => {
  if (duration < 100) return 'duration-fast'
  if (duration < 1000) return 'duration-normal'
  return 'duration-slow'
}

// 模拟数据加载函数
const loadOperateData = async () => {
  operateLoading.value = true
  setTimeout(() => {
    operateDataSource.value = [
      {
        id: 1,
        module: '商品管理',
        name: '创建商品',
        type: 1,
        userName: '管理员',
        userIp: '*************',
        duration: 156,
        resultCode: 0,
        startTime: '2024-01-01 10:30:25'
      }
    ]
    operatePagination.total = 1
    operateLoading.value = false
  }, 1000)
}

const loadErrorData = async () => {
  errorLoading.value = true
  setTimeout(() => {
    errorDataSource.value = [
      {
        id: 1,
        applicationName: 'erp-admin',
        exceptionName: 'NullPointerException',
        exceptionMessage: 'Cannot invoke method on null object',
        userIp: '*************',
        processStatus: 0,
        exceptionTime: '2024-01-01 10:30:25'
      }
    ]
    errorPagination.total = 1
    errorLoading.value = false
  }, 1000)
}

const loadAccessData = async () => {
  accessLoading.value = true
  setTimeout(() => {
    accessDataSource.value = [
      {
        id: 1,
        requestMethod: 'GET',
        requestUrl: '/admin-api/product/list',
        userIp: '*************',
        duration: 89,
        resultCode: 200,
        startTime: '2024-01-01 10:30:25'
      }
    ]
    accessPagination.total = 1
    accessLoading.value = false
  }, 1000)
}

// 事件处理函数
const handleLogTypeChange = (key: string) => {
  logType.value = key
  if (key === 'operate') {
    loadOperateData()
  } else if (key === 'error') {
    loadErrorData()
  } else if (key === 'access') {
    loadAccessData()
  }
}

const handleOperateSearch = () => {
  operatePagination.current = 1
  loadOperateData()
}

const handleOperateReset = () => {
  operateSearchFormRef.value?.resetFields()
  operatePagination.current = 1
  loadOperateData()
}

const handleOperateTableChange: TableProps['onChange'] = (pag) => {
  operatePagination.current = pag.current || 1
  operatePagination.pageSize = pag.pageSize || 20
  loadOperateData()
}

const handleViewOperateDetail = (record: any) => {
  currentLogType.value = 'operate'
  currentLogData.value = record
  detailModalVisible.value = true
}

const handleOperateExport = () => {
  message.info('导出操作日志功能开发中...')
}

const handleOperateClean = () => {
  Modal.confirm({
    title: '确认清理',
    content: '确定要清理操作日志吗？此操作不可恢复。',
    onOk: () => {
      message.success('清理成功')
    }
  })
}

// 错误日志事件处理
const handleErrorSearch = () => {
  errorPagination.current = 1
  loadErrorData()
}

const handleErrorReset = () => {
  errorSearchFormRef.value?.resetFields()
  errorPagination.current = 1
  loadErrorData()
}

const handleErrorTableChange: TableProps['onChange'] = (pag) => {
  errorPagination.current = pag.current || 1
  errorPagination.pageSize = pag.pageSize || 20
  loadErrorData()
}

const handleViewErrorDetail = (record: any) => {
  currentLogType.value = 'error'
  currentLogData.value = record
  detailModalVisible.value = true
}

const handleProcessError = (record: any) => {
  currentErrorLog.value = record
  processModalVisible.value = true
}

const handleProcessSuccess = () => {
  loadErrorData()
}

const handleErrorExport = () => {
  message.info('导出错误日志功能开发中...')
}

const handleErrorClean = () => {
  Modal.confirm({
    title: '确认清理',
    content: '确定要清理错误日志吗？此操作不可恢复。',
    onOk: () => {
      message.success('清理成功')
    }
  })
}

// 访问日志事件处理
const handleAccessSearch = () => {
  accessPagination.current = 1
  loadAccessData()
}

const handleAccessReset = () => {
  accessSearchFormRef.value?.resetFields()
  accessPagination.current = 1
  loadAccessData()
}

const handleAccessTableChange: TableProps['onChange'] = (pag) => {
  accessPagination.current = pag.current || 1
  accessPagination.pageSize = pag.pageSize || 20
  loadAccessData()
}

const handleViewAccessDetail = (record: any) => {
  currentLogType.value = 'access'
  currentLogData.value = record
  detailModalVisible.value = true
}

const handleAccessExport = () => {
  message.info('导出访问日志功能开发中...')
}

const handleAccessClean = () => {
  Modal.confirm({
    title: '确认清理',
    content: '确定要清理访问日志吗？此操作不可恢复。',
    onOk: () => {
      message.success('清理成功')
    }
  })
}

// 页面加载时初始化数据
onMounted(() => {
  loadOperateData()
})
</script>

<style scoped lang="less">
.log-management {
  .log-section {
    .search-form {
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
    }
    
    .action-buttons {
      margin-bottom: 16px;
    }
  }
  
  :deep(.duration-fast) {
    color: #52c41a;
  }
  
  :deep(.duration-normal) {
    color: #fa8c16;
  }
  
  :deep(.duration-slow) {
    color: #f5222d;
  }
}
</style>
