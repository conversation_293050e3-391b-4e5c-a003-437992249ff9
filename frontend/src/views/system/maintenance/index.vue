<!--
  系统维护页面
  @description 日志管理、文件管理、缓存管理、任务调度等功能
-->
<template>
  <div class="system-maintenance">
    <a-card :bordered="false">
      <!-- 系统状态概览 -->
      <div class="system-overview">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small" class="status-card">
              <a-statistic
                title="系统运行时间"
                :value="systemStatus.uptime"
                suffix="小时"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <ClockCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="status-card">
              <a-statistic
                title="内存使用率"
                :value="systemStatus.memoryUsage"
                suffix="%"
                :value-style="{ color: getMemoryColor(systemStatus.memoryUsage) }"
              >
                <template #prefix>
                  <DatabaseOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="status-card">
              <a-statistic
                title="CPU使用率"
                :value="systemStatus.cpuUsage"
                suffix="%"
                :value-style="{ color: getCpuColor(systemStatus.cpuUsage) }"
              >
                <template #prefix>
                  <ThunderboltOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small" class="status-card">
              <a-statistic
                title="磁盘使用率"
                :value="systemStatus.diskUsage"
                suffix="%"
                :value-style="{ color: getDiskColor(systemStatus.diskUsage) }"
              >
                <template #prefix>
                  <HddOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 维护功能标签页 -->
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- 日志管理 -->
        <a-tab-pane key="logs" tab="日志管理">
          <LogManagement />
        </a-tab-pane>

        <!-- 文件管理 -->
        <a-tab-pane key="files" tab="文件管理">
          <FileManagement />
        </a-tab-pane>

        <!-- 缓存管理 -->
        <a-tab-pane key="cache" tab="缓存管理">
          <CacheManagement />
        </a-tab-pane>

        <!-- 任务调度 -->
        <a-tab-pane key="jobs" tab="任务调度">
          <JobManagement />
        </a-tab-pane>

        <!-- 系统监控 -->
        <a-tab-pane key="monitor" tab="系统监控">
          <SystemMonitor />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import {
  ClockCircleOutlined,
  DatabaseOutlined,
  ThunderboltOutlined,
  HddOutlined
} from '@ant-design/icons-vue'
import LogManagement from './components/LogManagement.vue'
import FileManagement from './components/FileManagement.vue'
import CacheManagement from './components/CacheManagement.vue'
import JobManagement from './components/JobManagement.vue'
import SystemMonitor from './components/SystemMonitor.vue'

// 响应式数据
const activeTab = ref('logs')
const systemStatus = reactive({
  uptime: 168.5,
  memoryUsage: 65.2,
  cpuUsage: 23.8,
  diskUsage: 45.6
})

// 定时器
let statusTimer: NodeJS.Timeout | null = null

/**
 * 获取内存使用率颜色
 */
const getMemoryColor = (usage: number): string => {
  if (usage < 60) return '#52c41a'
  if (usage < 80) return '#fa8c16'
  return '#f5222d'
}

/**
 * 获取CPU使用率颜色
 */
const getCpuColor = (usage: number): string => {
  if (usage < 50) return '#52c41a'
  if (usage < 80) return '#fa8c16'
  return '#f5222d'
}

/**
 * 获取磁盘使用率颜色
 */
const getDiskColor = (usage: number): string => {
  if (usage < 70) return '#52c41a'
  if (usage < 90) return '#fa8c16'
  return '#f5222d'
}

/**
 * 标签页切换处理
 */
const handleTabChange = (key: string) => {
  activeTab.value = key
}

/**
 * 加载系统状态
 */
const loadSystemStatus = () => {
  // 模拟系统状态数据更新
  systemStatus.uptime += 0.1
  systemStatus.memoryUsage = Math.random() * 20 + 50
  systemStatus.cpuUsage = Math.random() * 30 + 10
  systemStatus.diskUsage = Math.random() * 10 + 40
}

/**
 * 启动状态监控
 */
const startStatusMonitor = () => {
  statusTimer = setInterval(() => {
    loadSystemStatus()
  }, 5000) // 5秒更新一次
}

/**
 * 停止状态监控
 */
const stopStatusMonitor = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 页面加载时初始化
onMounted(() => {
  loadSystemStatus()
  startStatusMonitor()
})

// 页面卸载时清理
onUnmounted(() => {
  stopStatusMonitor()
})
</script>

<style scoped lang="less">
.system-maintenance {
  .system-overview {
    margin-bottom: 24px;
    
    .status-card {
      text-align: center;
    }
  }
  
  .ant-tabs {
    .ant-tabs-content-holder {
      padding: 0;
    }
  }
}
</style>
