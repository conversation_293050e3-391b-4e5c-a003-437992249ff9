<!--
  角色管理主页面
  功能：角色列表展示、搜索、新增、编辑、删除、权限分配
-->
<template>
  <div class="role-management">
    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="角色编码/角色名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="角色类型">
          <a-select
            v-model:value="searchForm.roleType"
            placeholder="请选择角色类型"
            allow-clear
            style="width: 150px"
          >
            <a-select-option
              v-for="option in ROLE_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in ROLE_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 角色列表 -->
    <a-card :bordered="false">
      <template #title>
        <span>角色列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增角色
          </a-button>
          <a-button
            type="primary"
            danger
            :disabled="!hasSelected"
            @click="handleBatchDelete"
          >
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="roleList"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 角色类型 -->
        <template #roleType="{ record }">
          <a-tag :color="getRoleTypeColor(record.roleType)">
            {{ getRoleTypeDesc(record.roleType) }}
          </a-tag>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getRoleStatusColor(record.status)">
            {{ getRoleStatusDesc(record.status) }}
          </a-tag>
        </template>

        <!-- 系统标识 -->
        <template #isSystem="{ record }">
          <a-tag v-if="record.isSystem" color="red">系统</a-tag>
          <a-tag v-else color="blue">自定义</a-tag>
        </template>

        <!-- 创建时间 -->
        <template #createTime="{ record }">
          {{ formatDateTime(record.createTime) }}
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              <template #icon><EyeOutlined /></template>
              查看
            </a-button>
            <a-button
              type="link"
              size="small"
              :disabled="record.isSystem"
              @click="handleEdit(record)"
            >
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-button type="link" size="small" @click="handlePermission(record)">
              <template #icon><KeyOutlined /></template>
              权限
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-if="record.status === 0"
                    key="enable"
                    @click="handleEnable(record)"
                  >
                    <CheckOutlined />
                    启用
                  </a-menu-item>
                  <a-menu-item
                    v-else
                    key="disable"
                    @click="handleDisable(record)"
                  >
                    <StopOutlined />
                    禁用
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item
                    key="delete"
                    danger
                    :disabled="record.isSystem"
                    @click="handleDelete(record)"
                  >
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 角色表单模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="角色编码" name="roleCode">
          <a-input
            v-model:value="formData.roleCode"
            placeholder="请输入角色编码"
            :disabled="isEdit"
          />
        </a-form-item>
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="formData.roleName" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="角色类型" name="roleType">
          <a-select v-model:value="formData.roleType" placeholder="请选择角色类型">
            <a-select-option
              v-for="option in ROLE_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数据权限" name="dataScope">
          <a-select v-model:value="formData.dataScope" placeholder="请选择数据权限范围">
            <a-select-option
              v-for="option in DATA_SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否默认">
          <a-switch v-model:checked="formData.isDefault" />
        </a-form-item>
        <a-form-item label="排序" name="sortOrder">
          <a-input-number
            v-model:value="formData.sortOrder"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="角色描述">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  KeyOutlined,
  CheckOutlined,
  StopOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import RoleApi from '@/api/role'
import type {
  Role,
  RoleCreateRequest,
  RoleUpdateRequest,
  RoleQueryParams,
  RoleType,
  DataScope
} from '@/types/role'
import {
  ROLE_TYPE_OPTIONS,
  DATA_SCOPE_OPTIONS,
  ROLE_STATUS_OPTIONS,
  getRoleTypeDesc,
  getRoleStatusDesc,
  getRoleStatusColor
} from '@/types/role'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const roleList = ref<Role[]>([])
const selectedRowKeys = ref<number[]>([])

// 搜索表单
const searchForm = reactive<RoleQueryParams>({
  keyword: '',
  roleType: undefined,
  status: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '角色编码',
    dataIndex: 'roleCode',
    key: 'roleCode',
    width: 150
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    width: 150
  },
  {
    title: '角色类型',
    dataIndex: 'roleType',
    key: 'roleType',
    width: 120,
    slots: { customRender: 'roleType' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '系统标识',
    dataIndex: 'isSystem',
    key: 'isSystem',
    width: 100,
    slots: { customRender: 'isSystem' }
  },
  {
    title: '角色描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
    slots: { customRender: 'createTime' }
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Role) => ({
    disabled: record.isSystem
  })
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 模态框相关
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<RoleCreateRequest>({
  roleCode: '',
  roleName: '',
  roleType: 3, // 默认普通角色
  description: '',
  isDefault: false,
  dataScope: 1, // 默认全部数据权限
  sortOrder: 0,
  remark: ''
})

// 表单验证规则
const formRules = {
  roleCode: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '角色编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { max: 100, message: '角色名称不能超过100个字符', trigger: 'blur' }
  ],
  roleType: [
    { required: true, message: '请选择角色类型', trigger: 'change' }
  ],
  dataScope: [
    { required: true, message: '请选择数据权限范围', trigger: 'change' }
  ]
}

// 计算模态框标题
const modalTitle = computed(() => {
  return isEdit.value ? '编辑角色' : '新增角色'
})

// 获取角色类型颜色
function getRoleTypeColor(roleType: RoleType): string {
  const colorMap = {
    [RoleType.SYSTEM_ADMIN]: 'red',
    [RoleType.TENANT_ADMIN]: 'orange',
    [RoleType.NORMAL]: 'blue'
  }
  return colorMap[roleType] || 'default'
}

// 加载角色列表
async function loadRoleList() {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.current,
      size: pagination.pageSize
    }

    const response = await RoleApi.getList(params)
    if (response.success && response.data) {
      roleList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
    message.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
function handleSearch() {
  pagination.current = 1
  loadRoleList()
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    roleType: undefined,
    status: undefined,
    page: 1,
    size: 10
  })
  pagination.current = 1
  loadRoleList()
}

// 表格变化处理
function handleTableChange(pag: any) {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRoleList()
}

// 新增角色
function handleCreate() {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑角色
function handleEdit(record: Role) {
  isEdit.value = true
  Object.assign(formData, {
    id: record.id,
    roleCode: record.roleCode,
    roleName: record.roleName,
    roleType: record.roleType,
    description: record.description || '',
    isDefault: record.isDefault,
    dataScope: record.dataScope,
    sortOrder: record.sortOrder || 0,
    remark: record.remark || '',
    version: record.version
  })
  modalVisible.value = true
}

// 查看角色详情
function handleView(record: Role) {
  // TODO: 实现查看详情功能
  message.info('查看功能待实现')
}

// 权限管理
function handlePermission(record: Role) {
  // TODO: 实现权限分配功能
  message.info('权限分配功能待实现')
}

// 启用角色
async function handleEnable(record: Role) {
  try {
    await RoleApi.enable(record.id!)
    message.success('启用成功')
    loadRoleList()
  } catch (error) {
    console.error('启用角色失败:', error)
    message.error('启用角色失败')
  }
}

// 禁用角色
async function handleDisable(record: Role) {
  try {
    await RoleApi.disable(record.id!)
    message.success('禁用成功')
    loadRoleList()
  } catch (error) {
    console.error('禁用角色失败:', error)
    message.error('禁用角色失败')
  }
}

// 删除角色
function handleDelete(record: Role) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除角色"${record.roleName}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await RoleApi.delete(record.id!)
        message.success('删除成功')
        loadRoleList()
      } catch (error) {
        console.error('删除角色失败:', error)
        message.error('删除角色失败')
      }
    }
  })
}

// 批量删除
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的角色')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个角色吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await RoleApi.batchDelete(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadRoleList()
      } catch (error) {
        console.error('批量删除角色失败:', error)
        message.error('批量删除角色失败')
      }
    }
  })
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      // 更新角色
      await RoleApi.update(formData.id!, formData as RoleUpdateRequest)
      message.success('更新成功')
    } else {
      // 创建角色
      await RoleApi.create(formData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadRoleList()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
function handleCancel() {
  modalVisible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    roleCode: '',
    roleName: '',
    roleType: 3,
    description: '',
    isDefault: false,
    dataScope: 1,
    sortOrder: 0,
    remark: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoleList()
})
</script>

<style scoped lang="less">
.role-management {
  .search-card {
    margin-bottom: 16px;
  }

  .ant-table {
    .ant-tag {
      margin: 0;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
