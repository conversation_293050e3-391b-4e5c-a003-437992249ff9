<!--
  社交账号绑定管理
  @description 用户个人资料页面的社交账号绑定和解绑功能
-->
<template>
  <div class="social-binding">
    <a-card title="社交账号绑定" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="showBindModal = true">
          <PlusOutlined />
          绑定账号
        </a-button>
      </template>

      <!-- 已绑定的社交账号列表 -->
      <div class="bound-accounts">
        <div
          v-for="account in boundAccounts"
          :key="account.id"
          class="account-item"
        >
          <div class="account-info">
            <div class="account-avatar">
              <a-avatar :size="48" :src="account.avatar">
                <Icon :icon="getSocialTypeInfo(account.type).icon" :size="24" />
              </a-avatar>
            </div>
            <div class="account-details">
              <div class="account-name">{{ account.nickname }}</div>
              <div class="account-type">{{ getSocialTypeInfo(account.type).name }}</div>
              <div class="account-time">绑定时间：{{ formatTime(account.createTime) }}</div>
            </div>
          </div>
          <div class="account-actions">
            <a-popconfirm
              title="确定要解绑该社交账号吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleUnbind(account.type)"
            >
              <a-button type="text" danger>
                <DisconnectOutlined />
                解绑
              </a-button>
            </a-popconfirm>
          </div>
        </div>

        <!-- 空状态 -->
        <a-empty v-if="boundAccounts.length === 0" description="暂未绑定任何社交账号">
          <a-button type="primary" @click="showBindModal = true">
            立即绑定
          </a-button>
        </a-empty>
      </div>
    </a-card>

    <!-- 绑定社交账号弹窗 -->
    <a-modal
      v-model:open="showBindModal"
      title="绑定社交账号"
      :footer="null"
      width="480px"
    >
      <div class="bind-modal-content">
        <p class="bind-desc">选择要绑定的社交平台：</p>
        
        <div class="social-platforms">
          <div
            v-for="platform in availablePlatforms"
            :key="platform.type"
            class="platform-item"
            @click="handleBind(platform.type)"
          >
            <div class="platform-icon">
              <Icon :icon="platform.icon" :size="32" />
            </div>
            <div class="platform-name">{{ platform.name }}</div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 绑定确认弹窗 -->
    <a-modal
      v-model:open="showBindConfirmModal"
      title="确认绑定"
      :confirm-loading="bindLoading"
      @ok="confirmBind"
      @cancel="cancelBind"
    >
      <div v-if="pendingSocialUser" class="bind-confirm-content">
        <div class="social-user-info">
          <a-avatar :size="64" :src="pendingSocialUser.avatar">
            {{ pendingSocialUser.nickname?.charAt(0) }}
          </a-avatar>
          <div class="user-details">
            <div class="user-name">{{ pendingSocialUser.nickname }}</div>
            <div class="user-platform">{{ getSocialTypeInfo(pendingSocialUser.type).name }}</div>
          </div>
        </div>
        <p class="confirm-text">
          确定要将此{{ getSocialTypeInfo(pendingSocialUser.type).name }}账号绑定到当前系统账号吗？
        </p>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DisconnectOutlined } from '@ant-design/icons-vue'
import { Icon } from '@iconify/vue'
import { socialBind, socialUnbind, socialAuthRedirect } from '@/api/system/auth'
import { getSocialUserList } from '@/api/system/social'
import { SocialTypeEnum, getSocialTypeInfo } from '@/enums/socialTypeEnum'
import { formatTime } from '@/utils/dateUtil'

// 响应式数据
const showBindModal = ref(false)
const showBindConfirmModal = ref(false)
const bindLoading = ref(false)
const boundAccounts = ref<any[]>([])
const pendingSocialUser = ref<any>(null)

// 可用的社交平台
const availablePlatforms = computed(() => {
  const allPlatforms = [
    { type: SocialTypeEnum.DINGTALK, ...getSocialTypeInfo(SocialTypeEnum.DINGTALK) },
    { type: SocialTypeEnum.WEWORK, ...getSocialTypeInfo(SocialTypeEnum.WEWORK) },
    { type: SocialTypeEnum.WECHAT_MP, ...getSocialTypeInfo(SocialTypeEnum.WECHAT_MP) },
    { type: SocialTypeEnum.QQ, ...getSocialTypeInfo(SocialTypeEnum.QQ) },
    { type: SocialTypeEnum.GITHUB, ...getSocialTypeInfo(SocialTypeEnum.GITHUB) }
  ]
  
  // 过滤掉已绑定的平台
  const boundTypes = boundAccounts.value.map(account => account.type)
  return allPlatforms.filter(platform => !boundTypes.includes(platform.type))
})

/**
 * 加载已绑定的社交账号
 */
const loadBoundAccounts = async () => {
  try {
    const { data } = await getSocialUserList()
    boundAccounts.value = data || []
  } catch (error) {
    console.error('加载社交账号失败:', error)
  }
}

/**
 * 处理绑定社交账号
 */
const handleBind = async (socialType: number) => {
  try {
    showBindModal.value = false
    
    // 获取授权URL
    const redirectUri = `${window.location.origin}/social-bind-callback`
    const { data } = await socialAuthRedirect(socialType, redirectUri)
    
    if (data) {
      // 打开新窗口进行授权
      const authWindow = window.open(
        data,
        'socialAuth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      )
      
      // 监听授权结果
      const checkClosed = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkClosed)
          // 重新加载绑定列表
          loadBoundAccounts()
        }
      }, 1000)
    }
  } catch (error) {
    console.error('绑定失败:', error)
    message.error('绑定失败，请稍后重试')
  }
}

/**
 * 处理解绑社交账号
 */
const handleUnbind = async (socialType: number) => {
  try {
    await socialUnbind(socialType)
    message.success('解绑成功')
    await loadBoundAccounts()
  } catch (error) {
    console.error('解绑失败:', error)
    message.error('解绑失败，请稍后重试')
  }
}

/**
 * 确认绑定
 */
const confirmBind = async () => {
  if (!pendingSocialUser.value) return
  
  try {
    bindLoading.value = true
    
    await socialBind({
      type: pendingSocialUser.value.type,
      code: pendingSocialUser.value.code,
      state: pendingSocialUser.value.state
    })
    
    message.success('绑定成功')
    showBindConfirmModal.value = false
    pendingSocialUser.value = null
    await loadBoundAccounts()
  } catch (error) {
    console.error('绑定失败:', error)
    message.error('绑定失败，请稍后重试')
  } finally {
    bindLoading.value = false
  }
}

/**
 * 取消绑定
 */
const cancelBind = () => {
  showBindConfirmModal.value = false
  pendingSocialUser.value = null
}

// 监听来自授权窗口的消息
const handleMessage = (event: MessageEvent) => {
  if (event.origin !== window.location.origin) return
  
  if (event.data.type === 'SOCIAL_AUTH_SUCCESS') {
    pendingSocialUser.value = event.data.data
    showBindConfirmModal.value = true
  } else if (event.data.type === 'SOCIAL_AUTH_ERROR') {
    message.error(event.data.message || '授权失败')
  }
}

// 页面加载时获取已绑定账号
onMounted(() => {
  loadBoundAccounts()
  window.addEventListener('message', handleMessage)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})
</script>

<style scoped lang="less">
.social-binding {
  .bound-accounts {
    .account-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
      
      .account-info {
        display: flex;
        align-items: center;
        
        .account-avatar {
          margin-right: 16px;
        }
        
        .account-details {
          .account-name {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }
          
          .account-type {
            font-size: 14px;
            color: #1890ff;
            margin-bottom: 4px;
          }
          
          .account-time {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
  
  .bind-modal-content {
    .bind-desc {
      margin-bottom: 20px;
      color: #666;
    }
    
    .social-platforms {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 16px;
      
      .platform-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }
        
        .platform-icon {
          margin-bottom: 8px;
        }
        
        .platform-name {
          font-size: 14px;
          color: #262626;
          text-align: center;
        }
      }
    }
  }
  
  .bind-confirm-content {
    text-align: center;
    
    .social-user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
      
      .user-details {
        margin-top: 12px;
        
        .user-name {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }
        
        .user-platform {
          font-size: 14px;
          color: #1890ff;
        }
      }
    }
    
    .confirm-text {
      color: #666;
      margin: 0;
    }
  }
}
</style>
