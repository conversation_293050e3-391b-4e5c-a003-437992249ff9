<!--
  菜单管理页面
  功能：菜单列表展示、搜索、新增、编辑、删除
-->
<template>
  <div class="menu-management">
    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="菜单名称">
          <a-input
            v-model:value="searchForm.title"
            placeholder="请输入菜单名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 菜单列表 -->
    <a-card :bordered="false">
      <template #title>
        <span>菜单列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增菜单
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="menuList"
        :pagination="false"
        :loading="loading"
        row-key="id"
      >
        <!-- 菜单名称 -->
        <template #title="{ record }">
          <span>{{ record.title }}</span>
        </template>

        <!-- 菜单类型 -->
        <template #type="{ record }">
          <a-tag>{{ getMenuTypeDesc(record.type) }}</a-tag>
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 菜单表单模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="600"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="菜单名称" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入菜单名称" />
        </a-form-item>
        <a-form-item label="菜单类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择菜单类型">
            <a-select-option
              v-for="option in MENU_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="路由路径">
          <a-input v-model:value="formData.path" placeholder="请输入路由路径" />
        </a-form-item>
        <a-form-item label="权限标识">
          <a-input v-model:value="formData.permission" placeholder="请输入权限标识" />
        </a-form-item>
        <a-form-item label="显示排序" name="sort">
          <a-input-number
            v-model:value="formData.sort"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { MenuApi } from '@/api/menu'
import type {
  Menu,
  MenuType,
  MenuStatus,
  MenuCreateRequest,
  MenuUpdateRequest,
  MenuSearchParams
} from '@/types/menu'
import {
  MENU_TYPE_OPTIONS,
  getMenuTypeDesc
} from '@/types/menu'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const modalVisible = ref(false)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive<MenuSearchParams>({
  title: '',
  type: undefined,
  status: undefined
})

// 菜单列表数据
const menuList = ref<Menu[]>([])

// 表单数据
const formData = reactive({
  id: undefined as number | undefined,
  title: '',
  type: 'M' as MenuType,
  path: '',
  permission: '',
  sort: 0
})

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑菜单' : '新增菜单'
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '菜单名称',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    slots: { customRender: 'title' }
  },
  {
    title: '菜单类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    slots: { customRender: 'type' }
  },
  {
    title: '路由路径',
    dataIndex: 'path',
    key: 'path',
    width: 150
  },
  {
    title: '权限标识',
    dataIndex: 'permission',
    key: 'permission',
    width: 150
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入菜单名称' }],
  type: [{ required: true, message: '请选择菜单类型' }],
  sort: [{ required: true, message: '请输入排序值' }]
}

// 获取菜单列表
const fetchMenuList = async () => {
  try {
    loading.value = true
    const response = await MenuApi.getTree(searchForm)
    menuList.value = response.data || []
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    message.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  fetchMenuList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    type: undefined,
    status: undefined
  })
  fetchMenuList()
}

// 新增菜单
const handleCreate = () => {
  resetFormData()
  modalVisible.value = true
}

// 编辑菜单
const handleEdit = (record: Menu) => {
  Object.assign(formData, {
    id: record.id,
    title: record.title,
    type: record.type,
    path: record.path,
    permission: record.permission,
    sort: record.sort
  })
  modalVisible.value = true
}

// 删除菜单
const handleDelete = (record: Menu) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除菜单"${record.title}"吗？`,
    onOk: async () => {
      try {
        await MenuApi.delete(record.id)
        message.success('删除成功')
        fetchMenuList()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true
    
    if (formData.id) {
      // 编辑
      await MenuApi.update(formData.id, formData as MenuUpdateRequest)
      message.success('更新成功')
    } else {
      // 新增
      await MenuApi.create(formData as MenuCreateRequest)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    fetchMenuList()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消表单
const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: undefined,
    title: '',
    type: 'M' as MenuType,
    path: '',
    permission: '',
    sort: 0
  })
  formRef.value?.resetFields()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMenuList()
})
</script>

<style scoped>
.menu-management {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}
</style>
