<template>
  <div class="login-container">
    <div class="login-content">
      <!-- 左侧背景 -->
      <div class="login-banner">
        <div class="banner-content">
          <h1 class="banner-title">VisThink ERP</h1>
          <p class="banner-subtitle">多租户电商管理系统</p>
          <div class="banner-features">
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>多平台集成</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>智能库存管理</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>订单全流程跟踪</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>数据实时同步</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="form-header">
            <h2 class="form-title">{{ $t('login.title') }}</h2>
            <p class="form-subtitle">欢迎使用 VisThink ERP 管理系统</p>
          </div>

          <AForm
            ref="formRef"
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
          >
            <AFormItem name="username">
              <AInput
                v-model:value="loginForm.username"
                size="large"
                :placeholder="$t('login.username')"
                :prefix="h(UserOutlined)"
              />
            </AFormItem>

            <AFormItem name="password">
              <AInputPassword
                v-model:value="loginForm.password"
                size="large"
                :placeholder="$t('login.password')"
                :prefix="h(LockOutlined)"
              />
            </AFormItem>

            <AFormItem>
              <div class="form-options">
                <ACheckbox v-model:checked="loginForm.remember">
                  {{ $t('login.remember') }}
                </ACheckbox>
                <AButton type="link" class="forgot-password">
                  {{ $t('login.forgot') }}
                </AButton>
              </div>
            </AFormItem>

            <AFormItem>
              <AButton
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="loading"
                class="login-button"
              >
                {{ $t('login.login') }}
              </AButton>
            </AFormItem>
          </AForm>

          <!-- 其他登录方式 -->
          <div class="other-login">
            <ADivider>其他登录方式</ADivider>
            <div class="social-login">
              <AButton shape="circle" size="large" class="social-btn">
                <WechatOutlined />
              </AButton>
              <AButton shape="circle" size="large" class="social-btn">
                <AlipayOutlined />
              </AButton>
              <AButton shape="circle" size="large" class="social-btn">
                <GithubOutlined />
              </AButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  UserOutlined,
  LockOutlined,
  CheckCircleOutlined,
  WechatOutlined,
  AlipayOutlined,
  GithubOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const { t } = useI18n()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: 'admin',
  password: '123456',
  remember: false
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 20, message: t('login.usernameRule'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' },
    { min: 6, max: 20, message: t('login.passwordRule'), trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async (values: any) => {
  loading.value = true
  try {
    // TODO: 调用登录 API
    console.log('登录信息:', values)
    
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存 token
    localStorage.setItem('token', 'mock-token-' + Date.now())
    
    message.success(t('login.loginSuccess'))
    
    // 跳转到首页
    router.push('/')
  } catch (error) {
    message.error(t('login.loginFailed'))
  } finally {
    loading.value = false
  }
}

// 登录失败处理
const handleLoginFailed = (errorInfo: any) => {
  console.log('登录验证失败:', errorInfo)
}
</script>

<style lang="less" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-content {
  width: 1000px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  }

  .banner-content {
    text-align: center;
    z-index: 1;
    position: relative;

    .banner-title {
      font-size: 48px;
      font-weight: bold;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .banner-subtitle {
      font-size: 18px;
      margin-bottom: 40px;
      opacity: 0.9;
    }

    .banner-features {
      .feature-item {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 16px;

        .anticon {
          margin-right: 12px;
          color: #52c41a;
        }
      }
    }
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;

  .form-header {
    text-align: center;
    margin-bottom: 40px;

    .form-title {
      font-size: 28px;
      font-weight: bold;
      color: #262626;
      margin-bottom: 8px;
    }

    .form-subtitle {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .forgot-password {
      padding: 0;
      font-size: 14px;
    }
  }

  .login-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    margin-top: 16px;
  }

  .other-login {
    margin-top: 32px;

    .social-login {
      display: flex;
      justify-content: center;
      gap: 16px;

      .social-btn {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #d9d9d9;
        color: #8c8c8c;
        transition: all 0.3s;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    width: 90vw;
    height: 90vh;
    flex-direction: column;
  }

  .login-banner {
    flex: none;
    height: 200px;

    .banner-content {
      .banner-title {
        font-size: 32px;
      }

      .banner-subtitle {
        font-size: 14px;
        margin-bottom: 20px;
      }

      .banner-features {
        display: none;
      }
    }
  }

  .login-form-container {
    padding: 20px;
  }
}
</style>
