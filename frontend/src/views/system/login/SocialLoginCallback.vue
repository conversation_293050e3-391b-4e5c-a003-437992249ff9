<!--
  社交登录回调页面
  @description 处理第三方平台登录回调，支持快捷登录和绑定登录
-->
<template>
  <div class="social-login-callback">
    <div class="callback-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <a-spin size="large" />
        <p class="loading-text">正在处理登录信息...</p>
      </div>

      <!-- 绑定登录表单 -->
      <div v-else-if="needBind" class="bind-section">
        <div class="bind-header">
          <div class="user-avatar">
            <a-avatar :size="64" :src="socialUserInfo.avatar">
              {{ socialUserInfo.nickname?.charAt(0) }}
            </a-avatar>
          </div>
          <h3>账号绑定</h3>
          <p class="bind-desc">
            {{ socialUserInfo.nickname }}，您好！<br>
            该{{ getSocialTypeName(socialUserInfo.type) }}账号尚未绑定系统账号，请输入您的账号密码进行绑定。
          </p>
        </div>

        <a-form
          ref="bindFormRef"
          :model="bindForm"
          :rules="bindRules"
          layout="vertical"
          @finish="handleBindLogin"
        >
          <a-form-item name="username" label="账号">
            <a-input
              v-model:value="bindForm.username"
              size="large"
              placeholder="请输入账号"
              :prefix="h(UserOutlined)"
            />
          </a-form-item>

          <a-form-item name="password" label="密码">
            <a-input-password
              v-model:value="bindForm.password"
              size="large"
              placeholder="请输入密码"
              :prefix="h(LockOutlined)"
            />
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              block
              :loading="bindLoading"
            >
              绑定并登录
            </a-button>
          </a-form-item>
        </a-form>

        <div class="bind-footer">
          <a-button type="link" @click="handleCancel">
            取消绑定
          </a-button>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-section">
        <a-result
          status="error"
          title="登录失败"
          :sub-title="error"
        >
          <template #extra>
            <a-button type="primary" @click="handleRetry">
              重新登录
            </a-button>
          </template>
        </a-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { socialLogin, socialCallback } from '@/api/system/auth'
import { useUserStore } from '@/stores/user'
import { SocialTypeEnum } from '@/enums/socialTypeEnum'
import type { FormInstance } from 'ant-design-vue'

// 路由和状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const needBind = ref(false)
const bindLoading = ref(false)
const error = ref('')
const socialUserInfo = ref<any>({})

// 绑定表单
const bindFormRef = ref<FormInstance>()
const bindForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const bindRules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

/**
 * 获取社交平台名称
 */
const getSocialTypeName = (type: number): string => {
  const typeEnum = Object.values(SocialTypeEnum).find(item => item === type)
  switch (typeEnum) {
    case SocialTypeEnum.DINGTALK:
      return '钉钉'
    case SocialTypeEnum.WEWORK:
      return '企业微信'
    case SocialTypeEnum.WECHAT_MP:
      return '微信'
    case SocialTypeEnum.QQ:
      return 'QQ'
    case SocialTypeEnum.GITHUB:
      return 'GitHub'
    default:
      return '第三方'
  }
}

/**
 * 处理社交登录回调
 */
const handleSocialCallback = async () => {
  try {
    loading.value = true
    error.value = ''

    const { type, code, state } = route.query
    
    if (!type || !code) {
      throw new Error('缺少必要的回调参数')
    }

    // 调用回调接口获取社交用户信息
    const callbackResult = await socialCallback(Number(type), String(code), String(state || ''))
    
    if (callbackResult.data) {
      socialUserInfo.value = callbackResult.data
      
      // 如果已绑定，尝试快捷登录
      if (callbackResult.data.bound) {
        await handleQuickLogin()
      } else {
        // 未绑定，显示绑定表单
        needBind.value = true
      }
    }
  } catch (err: any) {
    console.error('社交登录回调失败:', err)
    error.value = err.message || '登录失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

/**
 * 快捷登录
 */
const handleQuickLogin = async () => {
  try {
    const loginResult = await socialLogin({
      type: socialUserInfo.value.type,
      code: socialUserInfo.value.code,
      state: socialUserInfo.value.state
    })

    if (loginResult.data) {
      // 保存用户信息和token
      await userStore.login(loginResult.data)
      
      message.success('登录成功')
      
      // 跳转到首页或原来的页面
      const redirect = route.query.redirect as string
      router.replace(redirect || '/')
    }
  } catch (err: any) {
    console.error('快捷登录失败:', err)
    error.value = err.message || '登录失败，请稍后重试'
  }
}

/**
 * 绑定登录
 */
const handleBindLogin = async () => {
  try {
    bindLoading.value = true
    
    const loginResult = await socialLogin({
      type: socialUserInfo.value.type,
      code: socialUserInfo.value.code,
      state: socialUserInfo.value.state,
      username: bindForm.username,
      password: bindForm.password
    })

    if (loginResult.data) {
      // 保存用户信息和token
      await userStore.login(loginResult.data)
      
      message.success('绑定并登录成功')
      
      // 跳转到首页或原来的页面
      const redirect = route.query.redirect as string
      router.replace(redirect || '/')
    }
  } catch (err: any) {
    console.error('绑定登录失败:', err)
    message.error(err.message || '绑定失败，请检查账号密码')
  } finally {
    bindLoading.value = false
  }
}

/**
 * 取消绑定
 */
const handleCancel = () => {
  router.replace('/login')
}

/**
 * 重新登录
 */
const handleRetry = () => {
  router.replace('/login')
}

// 页面加载时处理回调
onMounted(() => {
  handleSocialCallback()
})
</script>

<style scoped lang="less">
.social-login-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  
  .callback-container {
    width: 100%;
    max-width: 400px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .loading-section {
    padding: 60px 40px;
    text-align: center;
    
    .loading-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }
  
  .bind-section {
    padding: 40px;
    
    .bind-header {
      text-align: center;
      margin-bottom: 32px;
      
      .user-avatar {
        margin-bottom: 16px;
      }
      
      h3 {
        margin-bottom: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #262626;
      }
      
      .bind-desc {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
      }
    }
    
    .bind-footer {
      text-align: center;
      margin-top: 16px;
    }
  }
  
  .error-section {
    padding: 40px;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .social-login-callback {
    padding: 10px;
    
    .bind-section {
      padding: 30px 20px;
    }
    
    .error-section {
      padding: 30px 20px;
    }
  }
}
</style>
