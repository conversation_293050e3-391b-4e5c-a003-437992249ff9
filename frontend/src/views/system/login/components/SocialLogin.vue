<!--
  社交登录组件
  @description 支持钉钉、企业微信、微信等第三方平台登录
-->
<template>
  <div class="social-login">
    <div class="social-title">
      <span>第三方登录</span>
    </div>
    
    <div class="social-buttons">
      <!-- 钉钉登录 -->
      <a-tooltip title="钉钉登录">
        <div 
          class="social-button dingtalk"
          @click="handleSocialLogin(SocialTypeEnum.DINGTALK)"
        >
          <Icon icon="logos:dingtalk" :size="24" />
        </div>
      </a-tooltip>
      
      <!-- 企业微信登录 -->
      <a-tooltip title="企业微信登录">
        <div 
          class="social-button wework"
          @click="handleSocialLogin(SocialTypeEnum.WEWORK)"
        >
          <Icon icon="logos:wechat-work" :size="24" />
        </div>
      </a-tooltip>
      
      <!-- 微信登录 -->
      <a-tooltip title="微信登录">
        <div 
          class="social-button wechat"
          @click="handleSocialLogin(SocialTypeEnum.WECHAT_MP)"
        >
          <Icon icon="logos:wechat" :size="24" />
        </div>
      </a-tooltip>
      
      <!-- QQ登录 -->
      <a-tooltip title="QQ登录">
        <div 
          class="social-button qq"
          @click="handleSocialLogin(SocialTypeEnum.QQ)"
        >
          <Icon icon="logos:qq" :size="24" />
        </div>
      </a-tooltip>
      
      <!-- GitHub登录 -->
      <a-tooltip title="GitHub登录">
        <div 
          class="social-button github"
          @click="handleSocialLogin(SocialTypeEnum.GITHUB)"
        >
          <Icon icon="logos:github-icon" :size="24" />
        </div>
      </a-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { Icon } from '@iconify/vue'
import { socialAuthRedirect } from '@/api/system/auth'
import { SocialTypeEnum } from '@/enums/socialTypeEnum'

// 组件事件
const emit = defineEmits<{
  socialLogin: [type: number]
}>()

/**
 * 处理社交登录
 */
const handleSocialLogin = async (socialType: number) => {
  try {
    // 获取当前页面URL作为回调地址
    const redirectUri = `${window.location.origin}/social-login`
    
    // 获取授权URL
    const { data } = await socialAuthRedirect(socialType, redirectUri)
    
    if (data) {
      // 跳转到第三方授权页面
      window.location.href = data
    } else {
      message.error('获取授权地址失败')
    }
  } catch (error) {
    console.error('社交登录失败:', error)
    message.error('社交登录失败，请稍后重试')
  }
}

// 暴露方法给父组件
defineExpose({
  handleSocialLogin
})
</script>

<style scoped lang="less">
.social-login {
  margin-top: 24px;
  
  .social-title {
    position: relative;
    text-align: center;
    margin-bottom: 16px;
    
    span {
      background: #fff;
      padding: 0 16px;
      color: #999;
      font-size: 14px;
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e8e8e8;
      z-index: -1;
    }
  }
  
  .social-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
    
    .social-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e8e8e8;
      background: #fff;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.dingtalk {
        &:hover {
          border-color: #0089ff;
          box-shadow: 0 4px 12px rgba(0, 137, 255, 0.2);
        }
      }
      
      &.wework {
        &:hover {
          border-color: #1aad19;
          box-shadow: 0 4px 12px rgba(26, 173, 25, 0.2);
        }
      }
      
      &.wechat {
        &:hover {
          border-color: #07c160;
          box-shadow: 0 4px 12px rgba(7, 193, 96, 0.2);
        }
      }
      
      &.qq {
        &:hover {
          border-color: #12b7f5;
          box-shadow: 0 4px 12px rgba(18, 183, 245, 0.2);
        }
      }
      
      &.github {
        &:hover {
          border-color: #333;
          box-shadow: 0 4px 12px rgba(51, 51, 51, 0.2);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .social-login {
    .social-buttons {
      gap: 12px;
      
      .social-button {
        width: 36px;
        height: 36px;
      }
    }
  }
}
</style>
