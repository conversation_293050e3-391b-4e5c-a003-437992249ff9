<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-image">
        <svg viewBox="0 0 404 404" class="error-svg">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#722ed1;stop-opacity:1" />
            </linearGradient>
          </defs>
          <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" fill="url(#gradient)">
            404
          </text>
        </svg>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">页面不存在</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被删除。
        </p>
        <p class="error-suggestion">
          请检查网址是否正确，或者返回首页继续浏览。
        </p>
        
        <div class="error-actions">
          <AButton type="primary" size="large" @click="goHome">
            <HomeOutlined />
            返回首页
          </AButton>
          <AButton size="large" @click="goBack" class="ml-3">
            <ArrowLeftOutlined />
            返回上页
          </AButton>
        </div>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="decoration">
      <div class="floating-element element-1"></div>
      <div class="floating-element element-2"></div>
      <div class="floating-element element-3"></div>
      <div class="floating-element element-4"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style lang="less" scoped>
.not-found-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.not-found-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.error-image {
  margin-bottom: 40px;

  .error-svg {
    width: 300px;
    height: 200px;
    font-size: 120px;
    font-weight: bold;
    font-family: 'Arial', sans-serif;
  }
}

.error-info {
  .error-title {
    font-size: 48px;
    font-weight: bold;
    color: #262626;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .error-description {
    font-size: 18px;
    color: #595959;
    margin-bottom: 8px;
    line-height: 1.6;
  }

  .error-suggestion {
    font-size: 16px;
    color: #8c8c8c;
    margin-bottom: 40px;
    line-height: 1.6;
  }

  .error-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .ant-btn {
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  .floating-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(114, 46, 209, 0.1) 100%);
    animation: float 6s ease-in-out infinite;

    &.element-1 {
      width: 80px;
      height: 80px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.element-2 {
      width: 120px;
      height: 120px;
      top: 20%;
      right: 15%;
      animation-delay: 2s;
    }

    &.element-3 {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    &.element-4 {
      width: 100px;
      height: 100px;
      bottom: 15%;
      right: 10%;
      animation-delay: 1s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-image {
    .error-svg {
      width: 250px;
      height: 150px;
      font-size: 80px;
    }
  }

  .error-info {
    .error-title {
      font-size: 36px;
    }

    .error-description {
      font-size: 16px;
    }

    .error-suggestion {
      font-size: 14px;
    }

    .error-actions {
      flex-direction: column;

      .ant-btn {
        width: 200px;
      }
    }
  }

  .decoration {
    .floating-element {
      &.element-1 {
        width: 50px;
        height: 50px;
      }

      &.element-2 {
        width: 70px;
        height: 70px;
      }

      &.element-3 {
        width: 40px;
        height: 40px;
      }

      &.element-4 {
        width: 60px;
        height: 60px;
      }
    }
  }
}

@media (max-width: 480px) {
  .not-found-container {
    padding: 20px;
  }

  .error-image {
    margin-bottom: 30px;

    .error-svg {
      width: 200px;
      height: 120px;
      font-size: 60px;
    }
  }

  .error-info {
    .error-title {
      font-size: 28px;
    }

    .error-description,
    .error-suggestion {
      font-size: 14px;
      padding: 0 20px;
    }
  }
}</style>
