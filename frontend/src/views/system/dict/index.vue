<!--
  字典管理页面
  功能：字典类型和字典数据的统一管理界面
-->
<template>
  <div class="dict-management">
    <!-- 字典类型管理 -->
    <a-row :gutter="16">
      <!-- 左侧：字典类型列表 -->
      <a-col :span="8">
        <a-card title="字典类型" :bordered="false" class="dict-type-card">
          <template #extra>
            <a-button type="primary" size="small" @click="handleCreateType">
              <template #icon><PlusOutlined /></template>
              新增
            </a-button>
          </template>

          <!-- 字典类型搜索 -->
          <a-form layout="inline" :model="typeSearchForm" class="search-form">
            <a-form-item>
              <a-input
                v-model:value="typeSearchForm.dictName"
                placeholder="字典名称"
                allow-clear
                @press-enter="handleTypeSearch"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleTypeSearch">
                <template #icon><SearchOutlined /></template>
              </a-button>
              <a-button @click="handleTypeReset" style="margin-left: 8px">
                <template #icon><ReloadOutlined /></template>
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 字典类型表格 -->
          <a-table
            :columns="typeColumns"
            :data-source="typeList"
            :pagination="typePagination"
            :loading="typeLoading"
            row-key="id"
            size="small"
            :scroll="{ y: 400 }"
            @change="handleTypeTableChange"
          >
            <!-- 状态列 -->
            <template #status="{ record }">
              <a-tag :color="getDictStatusColor(record.status)">
                {{ getDictStatusDesc(record.status) }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template #action="{ record }">
              <a-space size="small">
                <a-button type="link" size="small" @click="handleEditType(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" danger @click="handleDeleteType(record)">
                  删除
                </a-button>
              </a-space>
            </template>

            <!-- 自定义行点击 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key !== 'action'">
                <div
                  @click="handleSelectType(record)"
                  :class="{ 'selected-row': selectedType?.id === record.id }"
                  style="cursor: pointer; padding: 4px 0;"
                >
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getDictStatusColor(record.status)">
                      {{ getDictStatusDesc(record.status) }}
                    </a-tag>
                  </template>
                  <template v-else>
                    {{ record[column.dataIndex] }}
                  </template>
                </div>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 右侧：字典数据列表 -->
      <a-col :span="16">
        <a-card :bordered="false" class="dict-data-card">
          <template #title>
            <span>字典数据</span>
            <span v-if="selectedType" class="selected-type-info">
              （{{ selectedType.dictName }} - {{ selectedType.dictType }}）
            </span>
          </template>
          <template #extra>
            <a-space>
              <a-button
                type="primary"
                size="small"
                :disabled="!selectedType"
                @click="handleCreateData"
              >
                <template #icon><PlusOutlined /></template>
                新增
              </a-button>
              <a-button size="small" @click="handleRefreshCache">
                <template #icon><ReloadOutlined /></template>
                刷新缓存
              </a-button>
            </a-space>
          </template>

          <!-- 字典数据搜索 -->
          <a-form layout="inline" :model="dataSearchForm" class="search-form">
            <a-form-item>
              <a-input
                v-model:value="dataSearchForm.dictLabel"
                placeholder="字典标签"
                allow-clear
                @press-enter="handleDataSearch"
              />
            </a-form-item>
            <a-form-item>
              <a-select
                v-model:value="dataSearchForm.status"
                placeholder="状态"
                allow-clear
                style="width: 100px"
              >
                <a-select-option
                  v-for="option in DICT_STATUS_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleDataSearch">
                <template #icon><SearchOutlined /></template>
              </a-button>
              <a-button @click="handleDataReset" style="margin-left: 8px">
                <template #icon><ReloadOutlined /></template>
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 字典数据表格 -->
          <a-table
            :columns="dataColumns"
            :data-source="dataList"
            :pagination="dataPagination"
            :loading="dataLoading"
            row-key="id"
            size="small"
            @change="handleDataTableChange"
          >
            <!-- 是否默认列 -->
            <template #isDefault="{ record }">
              <a-tag :color="record.isDefault ? 'success' : 'default'">
                {{ record.isDefault ? '是' : '否' }}
              </a-tag>
            </template>

            <!-- 状态列 -->
            <template #status="{ record }">
              <a-tag :color="getDictStatusColor(record.status)">
                {{ getDictStatusDesc(record.status) }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template #action="{ record }">
              <a-space size="small">
                <a-button type="link" size="small" @click="handleEditData(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" danger @click="handleDeleteData(record)">
                  删除
                </a-button>
              </a-space>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 字典类型表单模态框 -->
    <a-modal
      v-model:open="typeModalVisible"
      :title="typeModalTitle"
      :width="600"
      :confirm-loading="typeSubmitLoading"
      @ok="handleTypeSubmit"
      @cancel="handleTypeCancel"
    >
      <a-form
        ref="typeFormRef"
        :model="typeFormData"
        :rules="typeFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="字典名称" name="dictName">
          <a-input v-model:value="typeFormData.dictName" placeholder="请输入字典名称" />
        </a-form-item>
        <a-form-item label="字典类型" name="dictType">
          <a-input
            v-model:value="typeFormData.dictType"
            placeholder="请输入字典类型"
            :disabled="!!typeFormData.id"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="typeFormData.status">
            <a-radio
              v-for="option in DICT_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="typeFormData.remark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 字典数据表单模态框 -->
    <a-modal
      v-model:open="dataModalVisible"
      :title="dataModalTitle"
      :width="600"
      :confirm-loading="dataSubmitLoading"
      @ok="handleDataSubmit"
      @cancel="handleDataCancel"
    >
      <a-form
        ref="dataFormRef"
        :model="dataFormData"
        :rules="dataFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="字典标签" name="dictLabel">
          <a-input v-model:value="dataFormData.dictLabel" placeholder="请输入字典标签" />
        </a-form-item>
        <a-form-item label="字典键值" name="dictValue">
          <a-input v-model:value="dataFormData.dictValue" placeholder="请输入字典键值" />
        </a-form-item>
        <a-form-item label="显示排序" name="dictSort">
          <a-input-number
            v-model:value="dataFormData.dictSort"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="回显样式">
          <a-select v-model:value="dataFormData.listClass" placeholder="请选择回显样式">
            <a-select-option
              v-for="option in LIST_CLASS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否默认">
          <a-switch v-model:checked="dataFormData.isDefault" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="dataFormData.status">
            <a-radio
              v-for="option in DICT_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="dataFormData.remark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance, TableColumnsType, TableProps } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { DictTypeApi, DictDataApi, dictCache } from '@/api/dict'
import type {
  DictType,
  DictData,
  DictTypeQueryParams,
  DictDataQueryParams,
  DictTypeCreateRequest,
  DictTypeUpdateRequest,
  DictDataCreateRequest,
  DictDataUpdateRequest
} from '@/types/dict'
import {
  DICT_STATUS_OPTIONS,
  LIST_CLASS_OPTIONS,
  getDictStatusDesc,
  getDictStatusColor,
  DEFAULT_DICT_TYPE,
  DEFAULT_DICT_DATA,
  validateDictType
} from '@/types/dict'

// 响应式数据
const typeLoading = ref(false)
const dataLoading = ref(false)
const typeSubmitLoading = ref(false)
const dataSubmitLoading = ref(false)
const typeModalVisible = ref(false)
const dataModalVisible = ref(false)
const typeFormRef = ref<FormInstance>()
const dataFormRef = ref<FormInstance>()

// 选中的字典类型
const selectedType = ref<DictType | null>(null)

// 字典类型搜索表单
const typeSearchForm = reactive<DictTypeQueryParams>({
  dictName: '',
  dictType: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
})

// 字典数据搜索表单
const dataSearchForm = reactive<Omit<DictDataQueryParams, 'dictType'>>({
  dictLabel: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
})

// 字典类型列表数据
const typeList = ref<DictType[]>([])
const typePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`
})

// 字典数据列表数据
const dataList = ref<DictData[]>([])
const dataPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`
})

// 字典类型表单数据
const typeFormData = reactive({
  id: undefined as number | undefined,
  dictName: '',
  dictType: '',
  status: 1,
  remark: ''
})

// 字典数据表单数据
const dataFormData = reactive({
  id: undefined as number | undefined,
  dictSort: 0,
  dictLabel: '',
  dictValue: '',
  dictType: '',
  listClass: 'default',
  isDefault: false,
  status: 1,
  remark: ''
})

// 计算属性
const typeModalTitle = computed(() => {
  return typeFormData.id ? '编辑字典类型' : '新增字典类型'
})

const dataModalTitle = computed(() => {
  return dataFormData.id ? '编辑字典数据' : '新增字典数据'
})

// 字典类型表格列配置
const typeColumns: TableColumnsType = [
  {
    title: '字典名称',
    dataIndex: 'dictName',
    key: 'dictName',
    width: 120,
    ellipsis: true
  },
  {
    title: '字典类型',
    dataIndex: 'dictType',
    key: 'dictType',
    width: 100,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 字典数据表格列配置
const dataColumns: TableColumnsType = [
  {
    title: '字典标签',
    dataIndex: 'dictLabel',
    key: 'dictLabel',
    width: 120
  },
  {
    title: '字典键值',
    dataIndex: 'dictValue',
    key: 'dictValue',
    width: 120
  },
  {
    title: '排序',
    dataIndex: 'dictSort',
    key: 'dictSort',
    width: 80
  },
  {
    title: '默认',
    dataIndex: 'isDefault',
    key: 'isDefault',
    width: 80,
    slots: { customRender: 'isDefault' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 表单验证规则
const typeFormRules = {
  dictName: [{ required: true, message: '请输入字典名称' }],
  dictType: [
    { required: true, message: '请输入字典类型' },
    {
      validator: (_: any, value: string) => {
        if (value && !validateDictType(value)) {
          return Promise.reject('字典类型格式不正确，只能包含字母、数字和下划线，且以字母开头')
        }
        return Promise.resolve()
      }
    }
  ],
  status: [{ required: true, message: '请选择状态' }]
}

const dataFormRules = {
  dictLabel: [{ required: true, message: '请输入字典标签' }],
  dictValue: [{ required: true, message: '请输入字典键值' }],
  dictSort: [{ required: true, message: '请输入排序值' }],
  status: [{ required: true, message: '请选择状态' }]
}

// 获取字典类型列表
const fetchTypeList = async () => {
  try {
    typeLoading.value = true
    const params = {
      ...typeSearchForm,
      pageNum: typePagination.current,
      pageSize: typePagination.pageSize
    }
    const response = await DictTypeApi.getList(params)
    typeList.value = response.data.records || []
    typePagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取字典类型列表失败:', error)
    message.error('获取字典类型列表失败')
  } finally {
    typeLoading.value = false
  }
}

// 获取字典数据列表
const fetchDataList = async () => {
  if (!selectedType.value) {
    dataList.value = []
    dataPagination.total = 0
    return
  }

  try {
    dataLoading.value = true
    const params = {
      ...dataSearchForm,
      dictType: selectedType.value.dictType,
      pageNum: dataPagination.current,
      pageSize: dataPagination.pageSize
    }
    const response = await DictDataApi.getList(params)
    dataList.value = response.data.records || []
    dataPagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取字典数据列表失败:', error)
    message.error('获取字典数据列表失败')
  } finally {
    dataLoading.value = false
  }
}

// 字典类型搜索
const handleTypeSearch = () => {
  typePagination.current = 1
  fetchTypeList()
}

// 重置字典类型搜索
const handleTypeReset = () => {
  Object.assign(typeSearchForm, {
    dictName: '',
    dictType: '',
    status: undefined
  })
  typePagination.current = 1
  fetchTypeList()
}

// 字典数据搜索
const handleDataSearch = () => {
  dataPagination.current = 1
  fetchDataList()
}

// 重置字典数据搜索
const handleDataReset = () => {
  Object.assign(dataSearchForm, {
    dictLabel: '',
    status: undefined
  })
  dataPagination.current = 1
  fetchDataList()
}

// 字典类型表格变化处理
const handleTypeTableChange: TableProps['onChange'] = (pagination) => {
  if (pagination) {
    typePagination.current = pagination.current || 1
    typePagination.pageSize = pagination.pageSize || 10
  }
  fetchTypeList()
}

// 字典数据表格变化处理
const handleDataTableChange: TableProps['onChange'] = (pagination) => {
  if (pagination) {
    dataPagination.current = pagination.current || 1
    dataPagination.pageSize = pagination.pageSize || 10
  }
  fetchDataList()
}

// 选择字典类型
const handleSelectType = (record: DictType) => {
  selectedType.value = record
  // 重置字典数据搜索条件和分页
  Object.assign(dataSearchForm, {
    dictLabel: '',
    status: undefined
  })
  dataPagination.current = 1
  fetchDataList()
}

// 新增字典类型
const handleCreateType = () => {
  resetTypeFormData()
  typeModalVisible.value = true
}

// 编辑字典类型
const handleEditType = (record: DictType) => {
  Object.assign(typeFormData, {
    id: record.id,
    dictName: record.dictName,
    dictType: record.dictType,
    status: record.status,
    remark: record.remark || ''
  })
  typeModalVisible.value = true
}

// 删除字典类型
const handleDeleteType = (record: DictType) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除字典类型"${record.dictName}"吗？删除后相关的字典数据也将被删除！`,
    onOk: async () => {
      try {
        await DictTypeApi.delete(record.id)
        message.success('删除成功')
        fetchTypeList()
        // 如果删除的是当前选中的类型，清空选中状态
        if (selectedType.value?.id === record.id) {
          selectedType.value = null
          dataList.value = []
          dataPagination.total = 0
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 字典类型表单提交
const handleTypeSubmit = async () => {
  try {
    await typeFormRef.value?.validate()
    typeSubmitLoading.value = true

    if (typeFormData.id) {
      // 编辑
      await DictTypeApi.update(typeFormData.id, typeFormData as DictTypeUpdateRequest)
      message.success('更新成功')
    } else {
      // 新增
      await DictTypeApi.create(typeFormData as DictTypeCreateRequest)
      message.success('创建成功')
    }

    typeModalVisible.value = false
    fetchTypeList()
    // 清除字典缓存
    dictCache.clearCache()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    typeSubmitLoading.value = false
  }
}

// 取消字典类型表单
const handleTypeCancel = () => {
  typeModalVisible.value = false
  resetTypeFormData()
}

// 重置字典类型表单数据
const resetTypeFormData = () => {
  Object.assign(typeFormData, {
    id: undefined,
    dictName: '',
    dictType: '',
    status: DEFAULT_DICT_TYPE.status,
    remark: ''
  })
  typeFormRef.value?.resetFields()
}

// 新增字典数据
const handleCreateData = () => {
  if (!selectedType.value) {
    message.warning('请先选择字典类型')
    return
  }
  resetDataFormData()
  dataFormData.dictType = selectedType.value.dictType
  dataModalVisible.value = true
}

// 编辑字典数据
const handleEditData = (record: DictData) => {
  Object.assign(dataFormData, {
    id: record.id,
    dictSort: record.dictSort,
    dictLabel: record.dictLabel,
    dictValue: record.dictValue,
    dictType: record.dictType,
    listClass: record.listClass || 'default',
    isDefault: record.isDefault,
    status: record.status,
    remark: record.remark || ''
  })
  dataModalVisible.value = true
}

// 删除字典数据
const handleDeleteData = (record: DictData) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除字典数据"${record.dictLabel}"吗？`,
    onOk: async () => {
      try {
        await DictDataApi.delete(record.id)
        message.success('删除成功')
        fetchDataList()
        // 清除相关字典缓存
        dictCache.clearCache(record.dictType)
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 字典数据表单提交
const handleDataSubmit = async () => {
  try {
    await dataFormRef.value?.validate()
    dataSubmitLoading.value = true

    if (dataFormData.id) {
      // 编辑
      await DictDataApi.update(dataFormData.id, dataFormData as DictDataUpdateRequest)
      message.success('更新成功')
    } else {
      // 新增
      await DictDataApi.create(dataFormData as DictDataCreateRequest)
      message.success('创建成功')
    }

    dataModalVisible.value = false
    fetchDataList()
    // 清除相关字典缓存
    dictCache.clearCache(dataFormData.dictType)
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    dataSubmitLoading.value = false
  }
}

// 取消字典数据表单
const handleDataCancel = () => {
  dataModalVisible.value = false
  resetDataFormData()
}

// 重置字典数据表单数据
const resetDataFormData = () => {
  Object.assign(dataFormData, {
    id: undefined,
    dictSort: DEFAULT_DICT_DATA.dictSort,
    dictLabel: '',
    dictValue: '',
    dictType: '',
    listClass: DEFAULT_DICT_DATA.listClass,
    isDefault: DEFAULT_DICT_DATA.isDefault,
    status: DEFAULT_DICT_DATA.status,
    remark: ''
  })
  dataFormRef.value?.resetFields()
}

// 刷新字典缓存
const handleRefreshCache = async () => {
  try {
    await DictTypeApi.refreshCache()
    dictCache.clearCache()
    message.success('缓存刷新成功')
  } catch (error) {
    console.error('刷新缓存失败:', error)
    message.error('刷新缓存失败')
  }
}

// 监听选中的字典类型变化
watch(
  () => selectedType.value,
  (newType) => {
    if (newType) {
      fetchDataList()
    } else {
      dataList.value = []
      dataPagination.total = 0
    }
  }
)

// 组件挂载时获取数据
onMounted(() => {
  fetchTypeList()
})
</script>

<style scoped>
.dict-management {
  padding: 16px;
}

.dict-type-card,
.dict-data-card {
  height: 100%;
}

.search-form {
  margin-bottom: 16px;
}

.search-form .ant-form-item {
  margin-bottom: 8px;
}

.selected-type-info {
  color: #1890ff;
  font-size: 12px;
  margin-left: 8px;
}

.selected-row {
  background-color: #e6f7ff;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.selected-row:hover) {
  background-color: #bae7ff !important;
}
</style>
