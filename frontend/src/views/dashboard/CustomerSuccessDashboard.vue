<template>
  <div class="customer-success-dashboard">
    <!-- 顶部指标卡片 -->
    <div class="metrics-cards">
      <a-row :gutter="16">
        <a-col :span="6" v-for="metric in keyMetrics" :key="metric.key">
          <MetricCard
            :title="metric.title"
            :value="metric.value"
            :unit="metric.unit"
            :trend="metric.trend"
            :status="metric.status"
            :target="metric.target"
            @click="handleMetricClick(metric.key)"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 主要图表区域 -->
    <div class="main-charts">
      <a-row :gutter="16">
        <!-- 健康度分布 -->
        <a-col :span="8">
          <a-card title="客户健康度分布" class="chart-card">
            <HealthDistributionChart
              :data="healthDistributionData"
              @segment-click="handleHealthSegmentClick"
            />
          </a-card>
        </a-col>

        <!-- 趋势分析 -->
        <a-col :span="16">
          <a-card title="关键指标趋势" class="chart-card">
            <div class="chart-controls">
              <a-radio-group v-model:value="trendTimeRange" @change="handleTimeRangeChange">
                <a-radio-button value="7d">7天</a-radio-button>
                <a-radio-button value="30d">30天</a-radio-button>
                <a-radio-button value="90d">90天</a-radio-button>
                <a-radio-button value="1y">1年</a-radio-button>
              </a-radio-group>
              
              <a-select
                v-model:value="selectedMetrics"
                mode="multiple"
                placeholder="选择指标"
                style="width: 300px; margin-left: 16px"
              >
                <a-select-option value="healthScore">健康度</a-select-option>
                <a-select-option value="retentionRate">留存率</a-select-option>
                <a-select-option value="nps">NPS评分</a-select-option>
                <a-select-option value="expansionRate">扩展率</a-select-option>
              </a-select>
            </div>
            
            <TrendAnalysisChart
              :data="trendData"
              :metrics="selectedMetrics"
              :time-range="trendTimeRange"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 客户列表和详情 -->
    <div class="customer-section">
      <a-row :gutter="16">
        <!-- 客户列表 -->
        <a-col :span="16">
          <a-card title="客户列表" class="customer-list-card">
            <template #extra>
              <a-space>
                <a-button @click="handleRefresh">
                  <template #icon><ReloadOutlined /></template>
                  刷新
                </a-button>
                <a-button @click="handleExport">
                  <template #icon><ExportOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </template>

            <!-- 筛选器 -->
            <div class="filters-section">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-select
                    v-model:value="filters.healthLevel"
                    placeholder="健康等级"
                    allow-clear
                    style="width: 100%"
                  >
                    <a-select-option value="excellent">优秀</a-select-option>
                    <a-select-option value="good">良好</a-select-option>
                    <a-select-option value="fair">一般</a-select-option>
                    <a-select-option value="poor">较差</a-select-option>
                    <a-select-option value="critical">危险</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-select
                    v-model:value="filters.segment"
                    placeholder="客户分层"
                    allow-clear
                    style="width: 100%"
                  >
                    <a-select-option value="enterprise">大客户</a-select-option>
                    <a-select-option value="mid_market">中型客户</a-select-option>
                    <a-select-option value="smb">小微客户</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-select
                    v-model:value="filters.riskLevel"
                    placeholder="风险等级"
                    allow-clear
                    style="width: 100%"
                  >
                    <a-select-option value="low">低风险</a-select-option>
                    <a-select-option value="medium">中风险</a-select-option>
                    <a-select-option value="high">高风险</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-input
                    v-model:value="filters.searchText"
                    placeholder="搜索客户名称"
                    allow-clear
                  >
                    <template #prefix><SearchOutlined /></template>
                  </a-input>
                </a-col>
              </a-row>
            </div>

            <!-- 客户表格 -->
            <CustomerTable
              :data="customerList"
              :loading="loading"
              :pagination="pagination"
              @row-click="handleCustomerSelect"
              @page-change="handlePageChange"
              @sort-change="handleSortChange"
            />
          </a-card>
        </a-col>

        <!-- 客户详情 -->
        <a-col :span="8">
          <a-card title="客户详情" class="customer-detail-card">
            <CustomerDetailPanel
              v-if="selectedCustomer"
              :customer="selectedCustomer"
              :health-score="selectedCustomerHealth"
              @action="handleCustomerAction"
            />
            <a-empty v-else description="请选择客户查看详情" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 预警中心 -->
    <div class="alert-section">
      <a-card title="预警中心" class="alert-card">
        <template #extra>
          <a-badge :count="alertCount" :offset="[10, 0]">
            <a-button @click="handleViewAllAlerts">
              查看全部预警
            </a-button>
          </a-badge>
        </template>

        <AlertCenter
          :alerts="recentAlerts"
          @alert-action="handleAlertAction"
          @alert-dismiss="handleAlertDismiss"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { customerSuccessApi } from '@/api/customerSuccess'
import MetricCard from './components/MetricCard.vue'
import HealthDistributionChart from './components/HealthDistributionChart.vue'
import TrendAnalysisChart from './components/TrendAnalysisChart.vue'
import CustomerTable from './components/CustomerTable.vue'
import CustomerDetailPanel from './components/CustomerDetailPanel.vue'
import AlertCenter from './components/AlertCenter.vue'

// 响应式数据
const loading = ref(false)
const selectedCustomer = ref(null)
const selectedCustomerHealth = ref(null)
const trendTimeRange = ref('30d')
const selectedMetrics = ref(['healthScore', 'retentionRate', 'nps'])

// 关键指标数据
const keyMetrics = ref([
  {
    key: 'healthScore',
    title: '整体健康度',
    value: 78.5,
    unit: '分',
    trend: '****%',
    status: 'good',
    target: '≥80分'
  },
  {
    key: 'retentionRate',
    title: '客户留存率',
    value: 94.2,
    unit: '%',
    trend: '****%',
    status: 'excellent',
    target: '≥95%'
  },
  {
    key: 'nps',
    title: 'NPS评分',
    value: 52,
    unit: '分',
    trend: '+5',
    status: 'good',
    target: '≥50分'
  },
  {
    key: 'riskCustomers',
    title: '风险客户数',
    value: 23,
    unit: '个',
    trend: '-3',
    status: 'warning',
    target: '≤20个'
  }
])

// 健康度分布数据
const healthDistributionData = ref({
  excellent: 35,
  good: 40,
  fair: 18,
  poor: 5,
  critical: 2
})

// 趋势数据
const trendData = ref([])

// 筛选器
const filters = reactive({
  healthLevel: undefined,
  segment: undefined,
  riskLevel: undefined,
  searchText: ''
})

// 客户列表
const customerList = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 预警数据
const recentAlerts = ref([])
const alertCount = computed(() => recentAlerts.value.length)

// 方法定义
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 并行加载各种数据
    const [
      metricsData,
      healthData,
      trendDataResult,
      customerData,
      alertData
    ] = await Promise.all([
      customerSuccessApi.getKeyMetrics(),
      customerSuccessApi.getHealthDistribution(),
      customerSuccessApi.getTrendData(trendTimeRange.value, selectedMetrics.value),
      customerSuccessApi.getCustomerList({
        ...filters,
        page: pagination.current,
        pageSize: pagination.pageSize
      }),
      customerSuccessApi.getRecentAlerts()
    ])

    // 更新数据
    keyMetrics.value = metricsData.data
    healthDistributionData.value = healthData.data
    trendData.value = trendDataResult.data
    customerList.value = customerData.data.list
    pagination.total = customerData.data.total
    recentAlerts.value = alertData.data

  } catch (error) {
    message.error('加载仪表板数据失败')
  } finally {
    loading.value = false
  }
}

const handleMetricClick = (metricKey: string) => {
  // 点击指标卡片，跳转到详细分析页面
  console.log('Metric clicked:', metricKey)
}

const handleHealthSegmentClick = (segment: string) => {
  // 点击健康度分布，筛选对应客户
  filters.healthLevel = segment
  loadCustomerList()
}

const handleTimeRangeChange = () => {
  loadTrendData()
}

const handleCustomerSelect = async (customer: any) => {
  selectedCustomer.value = customer
  
  try {
    const healthData = await customerSuccessApi.getCustomerHealthScore(customer.id)
    selectedCustomerHealth.value = healthData.data
  } catch (error) {
    message.error('加载客户健康度数据失败')
  }
}

const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.pageSize = pageSize
  loadCustomerList()
}

const handleSortChange = (sorter: any) => {
  // 处理排序变化
  loadCustomerList()
}

const handleRefresh = () => {
  loadDashboardData()
}

const handleExport = async () => {
  try {
    await customerSuccessApi.exportCustomerData(filters)
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

const handleCustomerAction = (action: string, customer: any) => {
  // 处理客户操作
  console.log('Customer action:', action, customer)
}

const handleAlertAction = (action: string, alert: any) => {
  // 处理预警操作
  console.log('Alert action:', action, alert)
}

const handleAlertDismiss = (alertId: string) => {
  // 忽略预警
  recentAlerts.value = recentAlerts.value.filter(alert => alert.id !== alertId)
}

const handleViewAllAlerts = () => {
  // 跳转到预警管理页面
  console.log('View all alerts')
}

const loadCustomerList = async () => {
  loading.value = true
  try {
    const response = await customerSuccessApi.getCustomerList({
      ...filters,
      page: pagination.current,
      pageSize: pagination.pageSize
    })
    
    customerList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    message.error('加载客户列表失败')
  } finally {
    loading.value = false
  }
}

const loadTrendData = async () => {
  try {
    const response = await customerSuccessApi.getTrendData(
      trendTimeRange.value, 
      selectedMetrics.value
    )
    trendData.value = response.data
  } catch (error) {
    message.error('加载趋势数据失败')
  }
}

// 监听筛选器变化
watch(filters, () => {
  pagination.current = 1
  loadCustomerList()
}, { deep: true })

// 监听选中指标变化
watch(selectedMetrics, () => {
  loadTrendData()
})

// 生命周期
onMounted(() => {
  loadDashboardData()
  
  // 设置定时刷新
  setInterval(() => {
    loadDashboardData()
  }, 5 * 60 * 1000) // 每5分钟刷新一次
})
</script>

<style scoped>
.customer-success-dashboard {
  padding: 24px;
  background: #f0f2f5;
}

.metrics-cards {
  margin-bottom: 24px;
}

.main-charts {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-controls {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-section {
  margin-bottom: 24px;
}

.customer-list-card {
  height: 600px;
}

.customer-detail-card {
  height: 600px;
}

.filters-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.alert-section {
  margin-bottom: 24px;
}

.alert-card {
  min-height: 300px;
}
</style>
