<!--
  电商数据看板
  @description 基于ECharts的电商核心数据可视化分析
-->
<template>
  <div class="ecommerce-dashboard">
    <!-- 核心指标卡片 -->
    <div class="metrics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic
              title="今日销售额"
              :value="metrics.todaySales"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#3f8600' }"
            >
              <template #suffix>
                <span class="metric-trend">
                  <CaretUpOutlined v-if="metrics.todaySalesGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(metrics.todaySalesGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic
              title="今日订单数"
              :value="metrics.todayOrders"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <span class="metric-trend">
                  <CaretUpOutlined v-if="metrics.todayOrdersGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(metrics.todayOrdersGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic
              title="新增会员"
              :value="metrics.newMembers"
              :value-style="{ color: '#722ed1' }"
            >
              <template #suffix>
                <span class="metric-trend">
                  <CaretUpOutlined v-if="metrics.newMembersGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(metrics.newMembersGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="metric-card">
            <a-statistic
              title="客单价"
              :value="metrics.avgOrderValue"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <span class="metric-trend">
                  <CaretUpOutlined v-if="metrics.avgOrderValueGrowth > 0" style="color: #3f8600" />
                  <CaretDownOutlined v-else style="color: #cf1322" />
                  {{ Math.abs(metrics.avgOrderValueGrowth) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 销售趋势图 -->
        <a-col :span="12">
          <a-card title="销售趋势" :bordered="false">
            <template #extra>
              <a-radio-group v-model:value="salesTrendPeriod" @change="loadSalesTrend">
                <a-radio-button value="7">近7天</a-radio-button>
                <a-radio-button value="30">近30天</a-radio-button>
                <a-radio-button value="90">近90天</a-radio-button>
              </a-radio-group>
            </template>
            <EChartsComponent
              ref="salesTrendChartRef"
              :option="salesTrendOption"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 订单状态分布 -->
        <a-col :span="12">
          <a-card title="订单状态分布" :bordered="false">
            <EChartsComponent
              ref="orderStatusChartRef"
              :option="orderStatusOption"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- 商品销量排行 -->
        <a-col :span="12">
          <a-card title="商品销量排行" :bordered="false">
            <template #extra>
              <a-select v-model:value="productRankingType" @change="loadProductRanking" style="width: 120px">
                <a-select-option value="sales">销量</a-select-option>
                <a-select-option value="revenue">销售额</a-select-option>
              </a-select>
            </template>
            <EChartsComponent
              ref="productRankingChartRef"
              :option="productRankingOption"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 会员增长趋势 -->
        <a-col :span="12">
          <a-card title="会员增长趋势" :bordered="false">
            <EChartsComponent
              ref="memberGrowthChartRef"
              :option="memberGrowthOption"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- 营销活动效果 -->
        <a-col :span="12">
          <a-card title="营销活动效果" :bordered="false">
            <EChartsComponent
              ref="promotionEffectChartRef"
              :option="promotionEffectOption"
              height="300px"
            />
          </a-card>
        </a-col>

        <!-- 地区销售分布 -->
        <a-col :span="12">
          <a-card title="地区销售分布" :bordered="false">
            <EChartsComponent
              ref="regionSalesChartRef"
              :option="regionSalesOption"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 实时数据表格 -->
    <div class="realtime-data">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="实时订单" :bordered="false">
            <template #extra>
              <a-badge :count="realtimeOrders.length" :overflow-count="99">
                <ReloadOutlined @click="loadRealtimeOrders" />
              </a-badge>
            </template>
            <a-list
              :data-source="realtimeOrders"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <span>订单号：{{ item.orderNo }}</span>
                      <a-tag :color="getOrderStatusColor(item.status)" style="margin-left: 8px">
                        {{ getOrderStatusText(item.status) }}
                      </a-tag>
                    </template>
                    <template #description>
                      <div>
                        <span>金额：¥{{ item.amount }}</span>
                        <span style="margin-left: 16px">时间：{{ item.createTime }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="热门商品" :bordered="false">
            <a-list
              :data-source="hotProducts"
              size="small"
              :pagination="false"
            >
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-badge :count="index + 1" :number-style="{ backgroundColor: getRankColor(index) }">
                        <a-avatar :src="item.image" shape="square" />
                      </a-badge>
                    </template>
                    <template #title>
                      <span>{{ item.name }}</span>
                    </template>
                    <template #description>
                      <div>
                        <span>销量：{{ item.sales }}</span>
                        <span style="margin-left: 16px">价格：¥{{ item.price }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { CaretUpOutlined, CaretDownOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import EChartsComponent from '@/components/ECharts/EChartsComponent.vue'
import type { EChartsOption } from 'echarts'

// 响应式数据
const metrics = reactive({
  todaySales: 125680.50,
  todaySalesGrowth: 12.5,
  todayOrders: 1256,
  todayOrdersGrowth: 8.3,
  newMembers: 89,
  newMembersGrowth: -2.1,
  avgOrderValue: 186.50,
  avgOrderValueGrowth: 5.7
})

const salesTrendPeriod = ref('30')
const productRankingType = ref('sales')
const realtimeOrders = ref<any[]>([])
const hotProducts = ref<any[]>([])

// 图表引用
const salesTrendChartRef = ref()
const orderStatusChartRef = ref()
const productRankingChartRef = ref()
const memberGrowthChartRef = ref()
const promotionEffectChartRef = ref()
const regionSalesChartRef = ref()

// 图表配置
const salesTrendOption = ref<EChartsOption>({})
const orderStatusOption = ref<EChartsOption>({})
const productRankingOption = ref<EChartsOption>({})
const memberGrowthOption = ref<EChartsOption>({})
const promotionEffectOption = ref<EChartsOption>({})
const regionSalesOption = ref<EChartsOption>({})

// 定时器
let realtimeTimer: NodeJS.Timeout | null = null

/**
 * 获取订单状态颜色
 */
const getOrderStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'orange',   // 待付款
    10: 'blue',    // 待发货
    20: 'cyan',    // 待收货
    30: 'green',   // 已完成
    40: 'red',     // 已取消
    50: 'default'  // 已关闭
  }
  return colorMap[status] || 'default'
}

/**
 * 获取订单状态文本
 */
const getOrderStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    0: '待付款',
    10: '待发货',
    20: '待收货',
    30: '已完成',
    40: '已取消',
    50: '已关闭'
  }
  return textMap[status] || '未知'
}

/**
 * 获取排名颜色
 */
const getRankColor = (index: number): string => {
  const colors = ['#f5222d', '#fa8c16', '#fadb14', '#52c41a', '#1890ff']
  return colors[index] || '#d9d9d9'
}

/**
 * 加载销售趋势数据
 */
const loadSalesTrend = () => {
  // 模拟数据
  const days = parseInt(salesTrendPeriod.value)
  const dates = []
  const salesData = []
  const ordersData = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    salesData.push(Math.floor(Math.random() * 50000) + 80000)
    ordersData.push(Math.floor(Math.random() * 500) + 800)
  }

  salesTrendOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['销售额', '订单数']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额(元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'line',
        data: salesData,
        smooth: true,
        itemStyle: {
          color: '#1890ff'
        }
      },
      {
        name: '订单数',
        type: 'bar',
        yAxisIndex: 1,
        data: ordersData,
        itemStyle: {
          color: '#52c41a'
        }
      }
    ]
  }
}

/**
 * 加载订单状态分布
 */
const loadOrderStatus = () => {
  orderStatusOption.value = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '待付款' },
          { value: 735, name: '待发货' },
          { value: 580, name: '待收货' },
          { value: 484, name: '已完成' },
          { value: 300, name: '已取消' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

/**
 * 加载商品销量排行
 */
const loadProductRanking = () => {
  const products = ['iPhone 15 Pro', '华为 Mate 60', '小米14', 'OPPO Find X7', 'vivo X100']
  const salesData = [2340, 1980, 1654, 1432, 1234]
  const revenueData = [468000, 356400, 264960, 229120, 197440]
  
  const data = productRankingType.value === 'sales' ? salesData : revenueData
  const unit = productRankingType.value === 'sales' ? '件' : '元'

  productRankingOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: products
    },
    series: [
      {
        name: productRankingType.value === 'sales' ? '销量' : '销售额',
        type: 'bar',
        data: data,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
}

/**
 * 加载会员增长趋势
 */
const loadMemberGrowth = () => {
  const dates = []
  const newMembersData = []
  const totalMembersData = []
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    newMembersData.push(Math.floor(Math.random() * 100) + 50)
    totalMembersData.push(10000 + (29 - i) * 75)
  }

  memberGrowthOption.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增会员', '累计会员']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '新增会员',
        position: 'left'
      },
      {
        type: 'value',
        name: '累计会员',
        position: 'right'
      }
    ],
    series: [
      {
        name: '新增会员',
        type: 'bar',
        data: newMembersData,
        itemStyle: {
          color: '#52c41a'
        }
      },
      {
        name: '累计会员',
        type: 'line',
        yAxisIndex: 1,
        data: totalMembersData,
        smooth: true,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
}

/**
 * 加载营销活动效果
 */
const loadPromotionEffect = () => {
  promotionEffectOption.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['优惠券', '满减活动', '秒杀活动', '拼团活动']
    },
    radar: {
      indicator: [
        { name: '参与人数', max: 10000 },
        { name: '转化率', max: 100 },
        { name: '客单价', max: 500 },
        { name: '复购率', max: 100 },
        { name: 'ROI', max: 10 }
      ]
    },
    series: [
      {
        name: '营销活动效果',
        type: 'radar',
        data: [
          {
            value: [8500, 85, 320, 75, 8.5],
            name: '优惠券'
          },
          {
            value: [6200, 78, 280, 68, 7.2],
            name: '满减活动'
          },
          {
            value: [4800, 92, 180, 45, 6.8],
            name: '秒杀活动'
          },
          {
            value: [3200, 88, 220, 82, 7.8],
            name: '拼团活动'
          }
        ]
      }
    ]
  }
}

/**
 * 加载地区销售分布
 */
const loadRegionSales = () => {
  regionSalesOption.value = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '地区销售',
        type: 'treemap',
        data: [
          {
            name: '广东省',
            value: 1200000,
            children: [
              { name: '深圳市', value: 500000 },
              { name: '广州市', value: 400000 },
              { name: '东莞市', value: 300000 }
            ]
          },
          {
            name: '江苏省',
            value: 800000,
            children: [
              { name: '南京市', value: 350000 },
              { name: '苏州市', value: 280000 },
              { name: '无锡市', value: 170000 }
            ]
          },
          {
            name: '浙江省',
            value: 600000,
            children: [
              { name: '杭州市', value: 300000 },
              { name: '宁波市', value: 200000 },
              { name: '温州市', value: 100000 }
            ]
          }
        ]
      }
    ]
  }
}

/**
 * 加载实时订单
 */
const loadRealtimeOrders = () => {
  realtimeOrders.value = [
    {
      orderNo: 'T202401010001',
      status: 10,
      amount: 299.00,
      createTime: '10:30:25'
    },
    {
      orderNo: 'T202401010002',
      status: 20,
      amount: 156.80,
      createTime: '10:28:15'
    },
    {
      orderNo: 'T202401010003',
      status: 30,
      amount: 89.90,
      createTime: '10:25:42'
    }
  ]
}

/**
 * 加载热门商品
 */
const loadHotProducts = () => {
  hotProducts.value = [
    {
      name: 'iPhone 15 Pro',
      sales: 2340,
      price: 7999,
      image: ''
    },
    {
      name: '华为 Mate 60',
      sales: 1980,
      price: 5999,
      image: ''
    },
    {
      name: '小米14',
      sales: 1654,
      price: 3999,
      image: ''
    }
  ]
}

/**
 * 启动实时数据更新
 */
const startRealtimeUpdate = () => {
  realtimeTimer = setInterval(() => {
    loadRealtimeOrders()
    // 更新核心指标
    metrics.todaySales += Math.random() * 1000
    metrics.todayOrders += Math.floor(Math.random() * 5)
  }, 30000) // 30秒更新一次
}

/**
 * 停止实时数据更新
 */
const stopRealtimeUpdate = () => {
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
    realtimeTimer = null
  }
}

// 页面加载时初始化数据
onMounted(() => {
  loadSalesTrend()
  loadOrderStatus()
  loadProductRanking()
  loadMemberGrowth()
  loadPromotionEffect()
  loadRegionSales()
  loadRealtimeOrders()
  loadHotProducts()
  startRealtimeUpdate()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopRealtimeUpdate()
})
</script>

<style scoped lang="less">
.ecommerce-dashboard {
  .metrics-cards {
    margin-bottom: 24px;
    
    .metric-card {
      text-align: center;
      
      .metric-trend {
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
  
  .charts-section {
    margin-bottom: 24px;
  }
  
  .realtime-data {
    .ant-list-item {
      padding: 8px 0;
    }
  }
}
</style>
