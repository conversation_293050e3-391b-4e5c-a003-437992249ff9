<template>
  <div class="dashboard">
    <ARow :gutter="24">
      <!-- 统计卡片 -->
      <ACol :xs="24" :sm="12" :lg="6">
        <ACard class="stat-card">
          <AStat
            title="总用户数"
            :value="stats.totalUsers"
            :precision="0"
            suffix="人"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </AStat>
        </ACard>
      </ACol>
      
      <ACol :xs="24" :sm="12" :lg="6">
        <ACard class="stat-card">
          <AStat
            title="商品总数"
            :value="stats.totalProducts"
            :precision="0"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ShoppingOutlined />
            </template>
          </AStat>
        </ACard>
      </ACol>
      
      <ACol :xs="24" :sm="12" :lg="6">
        <ACard class="stat-card">
          <AStat
            title="今日订单"
            :value="stats.todayOrders"
            :precision="0"
            suffix="单"
            :value-style="{ color: '#cf1322' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </AStat>
        </ACard>
      </ACol>
      
      <ACol :xs="24" :sm="12" :lg="6">
        <ACard class="stat-card">
          <AStat
            title="今日销售额"
            :value="stats.todaySales"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <DollarOutlined />
            </template>
          </AStat>
        </ACard>
      </ACol>
    </ARow>

    <ARow :gutter="24" class="mt-4">
      <!-- 销售趋势图 -->
      <ACol :xs="24" :lg="16">
        <ACard title="销售趋势" :bordered="false">
          <div class="chart-container">
            <div class="chart-placeholder">
              <BarChartOutlined style="font-size: 48px; color: #d9d9d9;" />
              <p>销售趋势图表区域</p>
              <p class="text-muted">可集成 ECharts 或其他图表库</p>
            </div>
          </div>
        </ACard>
      </ACol>
      
      <!-- 快捷操作 -->
      <ACol :xs="24" :lg="8">
        <ACard title="快捷操作" :bordered="false">
          <div class="quick-actions">
            <AButton type="primary" block class="mb-3" @click="handleQuickAction('addProduct')">
              <PlusOutlined />
              新增商品
            </AButton>
            <AButton block class="mb-3" @click="handleQuickAction('addOrder')">
              <FileAddOutlined />
              创建订单
            </AButton>
            <AButton block class="mb-3" @click="handleQuickAction('inventory')">
              <DatabaseOutlined />
              库存管理
            </AButton>
            <AButton block @click="handleQuickAction('reports')">
              <BarChartOutlined />
              查看报表
            </AButton>
          </div>
        </ACard>
      </ACol>
    </ARow>

    <ARow :gutter="24" class="mt-4">
      <!-- 最近订单 -->
      <ACol :xs="24" :lg="12">
        <ACard title="最近订单" :bordered="false">
          <AList
            :data-source="recentOrders"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <AListItem>
                <AListItemMeta
                  :title="item.orderNo"
                  :description="`客户：${item.customer} | 金额：¥${item.amount}`"
                >
                  <template #avatar>
                    <AAvatar :style="{ backgroundColor: getStatusColor(item.status) }">
                      {{ item.status }}
                    </AAvatar>
                  </template>
                </AListItemMeta>
                <template #actions>
                  <AButton type="link" size="small" @click="viewOrder(item.id)">
                    查看
                  </AButton>
                </template>
              </AListItem>
            </template>
          </AList>
        </ACard>
      </ACol>
      
      <!-- 库存预警 -->
      <ACol :xs="24" :lg="12">
        <ACard title="库存预警" :bordered="false">
          <AList
            :data-source="stockAlerts"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <AListItem>
                <AListItemMeta
                  :title="item.productName"
                  :description="`当前库存：${item.stock} | 预警值：${item.alertValue}`"
                >
                  <template #avatar>
                    <AAvatar style="background-color: #f56a00;">
                      <ExclamationOutlined />
                    </AAvatar>
                  </template>
                </AListItemMeta>
                <template #actions>
                  <AButton type="link" size="small" @click="handleStock(item.id)">
                    处理
                  </AButton>
                </template>
              </AListItem>
            </template>
          </AList>
        </ACard>
      </ACol>
    </ARow>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  UserOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  DollarOutlined,
  BarChartOutlined,
  PlusOutlined,
  FileAddOutlined,
  DatabaseOutlined,
  ExclamationOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)

// 统计数据
const stats = ref({
  totalUsers: 1234,
  totalProducts: 5678,
  todayOrders: 89,
  todaySales: 123456.78
})

// 最近订单
const recentOrders = ref([
  {
    id: 1,
    orderNo: 'ORD202412230001',
    customer: '张三',
    amount: 299.00,
    status: '已付款'
  },
  {
    id: 2,
    orderNo: 'ORD202412230002',
    customer: '李四',
    amount: 599.00,
    status: '待付款'
  },
  {
    id: 3,
    orderNo: 'ORD202412230003',
    customer: '王五',
    amount: 899.00,
    status: '已发货'
  }
])

// 库存预警
const stockAlerts = ref([
  {
    id: 1,
    productName: 'iPhone 15 Pro',
    stock: 5,
    alertValue: 10
  },
  {
    id: 2,
    productName: 'MacBook Pro',
    stock: 2,
    alertValue: 5
  },
  {
    id: 3,
    productName: 'AirPods Pro',
    stock: 8,
    alertValue: 15
  }
])

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '已付款': '#52c41a',
    '待付款': '#faad14',
    '已发货': '#1890ff',
    '已完成': '#722ed1',
    '已取消': '#f5222d'
  }
  return colorMap[status] || '#d9d9d9'
}

// 快捷操作处理
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'addProduct':
      router.push('/product/list')
      break
    case 'addOrder':
      router.push('/order/list')
      break
    case 'inventory':
      router.push('/inventory/stock')
      break
    case 'reports':
      message.info('报表功能开发中...')
      break
    default:
      break
  }
}

// 查看订单
const viewOrder = (id: number) => {
  router.push(`/order/detail/${id}`)
}

// 处理库存
const handleStock = (id: number) => {
  router.push('/inventory/alert')
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用 API 获取数据
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.dashboard {
  .stat-card {
    margin-bottom: 16px;
  }

  .chart-container {
    height: 300px;
    
    .chart-placeholder {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      
      p {
        margin: 8px 0;
        
        &.text-muted {
          font-size: 12px;
          color: #ccc;
        }
      }
    }
  }

  .quick-actions {
    .ant-btn {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
