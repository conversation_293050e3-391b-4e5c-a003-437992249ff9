<template>
  <div class="product-management">
    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        class="search-form"
      >
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="产品名称" name="productName">
              <a-input
                v-model:value="searchForm.productName"
                placeholder="请输入产品名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品编码" name="productCode">
              <a-input
                v-model:value="searchForm.productCode"
                placeholder="请输入产品编码"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品分类" name="categoryId">
              <a-tree-select
                v-model:value="searchForm.categoryId"
                :tree-data="categoryOptions"
                placeholder="请选择产品分类"
                allow-clear
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="产品类型" name="productType">
              <a-select
                v-model:value="searchForm.productType"
                placeholder="请选择产品类型"
                allow-clear
              >
                <a-select-option
                  v-for="option in PRODUCT_TYPE_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="品牌" name="brand">
              <a-input
                v-model:value="searchForm.brand"
                placeholder="请输入品牌"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态" name="status">
              <a-select
                v-model:value="searchForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option
                  v-for="option in PRODUCT_STATUS_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="库存预警" name="stockAlertType">
              <a-select
                v-model:value="searchForm.stockAlertType"
                placeholder="请选择库存预警类型"
                allow-clear
              >
                <a-select-option
                  v-for="option in STOCK_ALERT_TYPE_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 操作按钮 -->
    <a-card class="action-card" :bordered="false">
      <a-space>
        <a-button type="primary" @click="handleCreate">
          <template #icon><PlusOutlined /></template>
          新增产品
        </a-button>
        <a-button
          type="primary"
          ghost
          :disabled="!selectedRowKeys.length"
          @click="handleBatchEnable"
        >
          批量上架
        </a-button>
        <a-button
          danger
          ghost
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDisable"
        >
          批量下架
        </a-button>
        <a-button
          danger
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          批量删除
        </a-button>
        <a-button @click="handleExport">
          导出数据
        </a-button>
        <a-upload
          :show-upload-list="false"
          :before-upload="handleImport"
          accept=".xlsx,.xls"
        >
          <a-button>
            导入数据
          </a-button>
        </a-upload>
      </a-space>
    </a-card>

    <!-- 产品列表 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1800 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 产品图片 -->
        <template #images="{ record }">
          <div class="product-images">
            <a-image
              v-if="record.images && record.images.length > 0"
              :src="record.images[0]"
              :width="50"
              :height="50"
              :preview="{ src: record.images[0] }"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
            <span v-else class="no-image">无图片</span>
          </div>
        </template>

        <!-- 产品信息 -->
        <template #productInfo="{ record }">
          <div class="product-info">
            <div class="product-name">{{ record.productName }}</div>
            <div class="product-code">{{ record.productCode }}</div>
            <div class="product-category">{{ record.categoryName }}</div>
          </div>
        </template>

        <!-- 价格信息 -->
        <template #priceInfo="{ record }">
          <div class="price-info">
            <div class="sale-price">售价: {{ formatPrice(record.salePrice) }}</div>
            <div class="purchase-price">进价: {{ formatPrice(record.purchasePrice) }}</div>
          </div>
        </template>

        <!-- 库存信息 -->
        <template #stockInfo="{ record }">
          <div class="stock-info">
            <div class="stock-quantity">
              库存: {{ formatStock(record.stockQuantity, record.unit) }}
            </div>
            <div class="min-stock">
              最小库存: {{ formatStock(record.minStock, record.unit) }}
            </div>
            <a-tag
              v-if="record.stockAlertType !== 'NONE'"
              :color="getStockAlertTypeColor(record.stockAlertType)"
              size="small"
            >
              {{ getStockAlertTypeDesc(record.stockAlertType) }}
            </a-tag>
          </div>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getProductStatusColor(record.status)">
            {{ getProductStatusDesc(record.status) }}
          </a-tag>
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="handleViewStock(record)">
              库存
            </a-button>
            <a-popconfirm
              title="确定要删除这个产品吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>
    <!-- 产品表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品名称" name="productName">
              <a-input v-model:value="formData.productName" placeholder="请输入产品名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品编码" name="productCode">
              <a-input v-model:value="formData.productCode" placeholder="请输入产品编码" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品分类" name="categoryId">
              <a-tree-select
                v-model:value="formData.categoryId"
                :tree-data="categoryOptions"
                placeholder="请选择产品分类"
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品类型" name="productType">
              <a-select v-model:value="formData.productType" placeholder="请选择产品类型">
                <a-select-option
                  v-for="option in PRODUCT_TYPE_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="品牌" name="brand">
              <a-input v-model:value="formData.brand" placeholder="请输入品牌" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="型号" name="model">
              <a-input v-model:value="formData.model" placeholder="请输入型号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="单位" name="unit">
              <a-select v-model:value="formData.unit" placeholder="请选择单位">
                <a-select-option v-for="unit in UNIT_OPTIONS" :key="unit" :value="unit">
                  {{ unit }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="采购价格" name="purchasePrice">
              <a-input-number
                v-model:value="formData.purchasePrice"
                :min="0"
                :precision="2"
                placeholder="请输入采购价格"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="销售价格" name="salePrice">
              <a-input-number
                v-model:value="formData.salePrice"
                :min="0"
                :precision="2"
                placeholder="请输入销售价格"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="库存数量" name="stockQuantity">
              <a-input-number
                v-model:value="formData.stockQuantity"
                :min="0"
                placeholder="请输入库存数量"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="最小库存" name="minStock">
              <a-input-number
                v-model:value="formData.minStock"
                :min="0"
                placeholder="请输入最小库存"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="库存预警" name="stockAlertType">
              <a-select v-model:value="formData.stockAlertType" placeholder="请选择库存预警类型">
                <a-select-option
                  v-for="option in STOCK_ALERT_TYPE_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="产品描述" name="description">
              <a-textarea
                v-model:value="formData.description"
                :rows="3"
                placeholder="请输入产品描述"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="formData.status">
                <a-radio
                  v-for="option in PRODUCT_STATUS_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 库存管理弹窗 -->
    <a-modal
      v-model:open="stockModalVisible"
      title="库存管理"
      :width="600"
      :footer="null"
    >
      <div v-if="currentProduct">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="产品名称">
            {{ currentProduct.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="产品编码">
            {{ currentProduct.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            {{ formatStock(currentProduct.stockQuantity, currentProduct.unit) }}
          </a-descriptions-item>
          <a-descriptions-item label="最小库存">
            {{ formatStock(currentProduct.minStock, currentProduct.unit) }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>库存调整</a-divider>

        <a-form layout="inline">
          <a-form-item label="调整数量">
            <a-input-number
              v-model:value="stockAdjustment.quantity"
              placeholder="正数增加，负数减少"
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item label="调整类型">
            <a-select v-model:value="stockAdjustment.type" style="width: 120px">
              <a-select-option value="IN">入库</a-select-option>
              <a-select-option value="OUT">出库</a-select-option>
              <a-select-option value="ADJUST">调整</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleStockAdjust">
              确认调整
            </a-button>
          </a-form-item>
        </a-form>

        <a-form-item label="备注">
          <a-textarea
            v-model:value="stockAdjustment.remark"
            placeholder="请输入调整备注"
            :rows="2"
          />
        </a-form-item>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance, TableColumnsType, TableProps } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { ProductApi, ProductCategoryApi, ProductStockManager } from '@/api/product'
import type {
  Product,
  ProductQueryParams,
  ProductCreateRequest,
  ProductUpdateRequest
} from '@/types/product'
import {
  PRODUCT_STATUS_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
  STOCK_ALERT_TYPE_OPTIONS,
  UNIT_OPTIONS,
  getProductStatusDesc,
  getProductStatusColor,
  getProductTypeDesc,
  getStockAlertTypeDesc,
  getStockAlertTypeColor,
  formatPrice,
  formatStock,
  validateProductCode,
  validatePrice,
  validateStock,
  DEFAULT_PRODUCT,
  ProductStatus,
  ProductType,
  StockAlertType
} from '@/types/product'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const modalVisible = ref(false)
const stockModalVisible = ref(false)
const searchFormRef = ref<FormInstance>()
const formRef = ref<FormInstance>()

// 选中的行
const selectedRowKeys = ref<number[]>()
const selectedRows = ref<Product[]>([])

// 当前操作的产品
const currentProduct = ref<Product | null>(null)

// 产品分类选项
const categoryOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive<ProductQueryParams>({
  productName: '',
  productCode: '',
  categoryId: undefined,
  productType: undefined,
  brand: '',
  status: undefined,
  stockAlertType: undefined,
  pageNum: 1,
  pageSize: 10
})

// 产品列表数据
const dataList = ref<Product[]>([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`
})

// 产品表单数据
const formData = reactive({
  id: undefined as number | undefined,
  productName: '',
  productCode: '',
  categoryId: undefined as number | undefined,
  productType: ProductType.PHYSICAL,
  brand: '',
  model: '',
  specification: '',
  unit: '个',
  purchasePrice: 0,
  salePrice: 0,
  marketPrice: undefined as number | undefined,
  costPrice: undefined as number | undefined,
  weight: undefined as number | undefined,
  volume: undefined as number | undefined,
  barcode: '',
  qrcode: '',
  images: [] as string[],
  description: '',
  status: ProductStatus.ENABLED,
  stockQuantity: 0,
  minStock: 10,
  maxStock: undefined as number | undefined,
  stockAlertType: StockAlertType.LOW_STOCK,
  remark: ''
})

// 库存调整数据
const stockAdjustment = reactive({
  quantity: 0,
  type: 'ADJUST' as 'IN' | 'OUT' | 'ADJUST',
  remark: ''
})

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑产品' : '新增产品'
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[], rows: Product[]) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
  }
}

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '产品图片',
    key: 'images',
    width: 80,
    slots: { customRender: 'images' }
  },
  {
    title: '产品信息',
    key: 'productInfo',
    width: 200,
    slots: { customRender: 'productInfo' }
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    key: 'productType',
    width: 100,
    customRender: ({ record }) => getProductTypeDesc(record.productType)
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
    width: 100,
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 60
  },
  {
    title: '价格信息',
    key: 'priceInfo',
    width: 150,
    slots: { customRender: 'priceInfo' }
  },
  {
    title: '库存信息',
    key: 'stockInfo',
    width: 180,
    slots: { customRender: 'stockInfo' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 表单验证规则
const formRules = {
  productName: [{ required: true, message: '请输入产品名称' }],
  productCode: [
    { required: true, message: '请输入产品编码' },
    {
      validator: (_: any, value: string) => {
        if (value && !validateProductCode(value)) {
          return Promise.reject('产品编码格式不正确，只能包含大写字母、数字、下划线和连字符，且以大写字母或数字开头')
        }
        return Promise.resolve()
      }
    }
  ],
  categoryId: [{ required: true, message: '请选择产品分类' }],
  productType: [{ required: true, message: '请选择产品类型' }],
  unit: [{ required: true, message: '请选择单位' }],
  purchasePrice: [
    { required: true, message: '请输入采购价格' },
    {
      validator: (_: any, value: number) => {
        if (value !== undefined && !validatePrice(value)) {
          return Promise.reject('价格必须在0-999999.99之间')
        }
        return Promise.resolve()
      }
    }
  ],
  salePrice: [
    { required: true, message: '请输入销售价格' },
    {
      validator: (_: any, value: number) => {
        if (value !== undefined && !validatePrice(value)) {
          return Promise.reject('价格必须在0-999999.99之间')
        }
        return Promise.resolve()
      }
    }
  ],
  stockQuantity: [
    { required: true, message: '请输入库存数量' },
    {
      validator: (_: any, value: number) => {
        if (value !== undefined && !validateStock(value)) {
          return Promise.reject('库存数量必须为非负整数')
        }
        return Promise.resolve()
      }
    }
  ],
  minStock: [
    { required: true, message: '请输入最小库存' },
    {
      validator: (_: any, value: number) => {
        if (value !== undefined && !validateStock(value)) {
          return Promise.reject('最小库存必须为非负整数')
        }
        return Promise.resolve()
      }
    }
  ],
  stockAlertType: [{ required: true, message: '请选择库存预警类型' }],
  status: [{ required: true, message: '请选择状态' }]
}

// 获取产品列表
const fetchDataList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    const response = await ProductApi.getList(params)
    dataList.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取产品列表失败:', error)
    message.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 获取产品分类选项
const fetchCategoryOptions = async () => {
  try {
    const options = await ProductCategoryApi.getOptions()
    categoryOptions.value = options
  } catch (error) {
    console.error('获取产品分类选项失败:', error)
    message.error('获取产品分类选项失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchDataList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    productName: '',
    productCode: '',
    categoryId: undefined,
    productType: undefined,
    brand: '',
    status: undefined,
    stockAlertType: undefined
  })
  pagination.current = 1
  fetchDataList()
}

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (paginationInfo) => {
  if (paginationInfo) {
    pagination.current = paginationInfo.current || 1
    pagination.pageSize = paginationInfo.pageSize || 10
  }
  fetchDataList()
}

// 新增产品
const handleCreate = () => {
  resetFormData()
  modalVisible.value = true
}

// 编辑产品
const handleEdit = (record: Product) => {
  Object.assign(formData, {
    id: record.id,
    productName: record.productName,
    productCode: record.productCode,
    categoryId: record.categoryId,
    productType: record.productType,
    brand: record.brand || '',
    model: record.model || '',
    specification: record.specification || '',
    unit: record.unit,
    purchasePrice: record.purchasePrice,
    salePrice: record.salePrice,
    marketPrice: record.marketPrice,
    costPrice: record.costPrice,
    weight: record.weight,
    volume: record.volume,
    barcode: record.barcode || '',
    qrcode: record.qrcode || '',
    images: record.images || [],
    description: record.description || '',
    status: record.status,
    stockQuantity: record.stockQuantity,
    minStock: record.minStock,
    maxStock: record.maxStock,
    stockAlertType: record.stockAlertType,
    remark: record.remark || ''
  })
  modalVisible.value = true
}

// 删除产品
const handleDelete = (record: Product) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除产品"${record.productName}"吗？`,
    onOk: async () => {
      try {
        await ProductApi.delete(record.id)
        message.success('删除成功')
        fetchDataList()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 批量上架
const handleBatchEnable = () => {
  if (!selectedRowKeys.value?.length) {
    message.warning('请选择要上架的产品')
    return
  }

  Modal.confirm({
    title: '确认批量上架',
    content: `确定要上架选中的 ${selectedRowKeys.value.length} 个产品吗？`,
    onOk: async () => {
      try {
        await ProductApi.batchUpdateStatus(selectedRowKeys.value!, ProductStatus.ENABLED)
        message.success('批量上架成功')
        selectedRowKeys.value = []
        selectedRows.value = []
        fetchDataList()
      } catch (error) {
        console.error('批量上架失败:', error)
        message.error('批量上架失败')
      }
    }
  })
}

// 批量下架
const handleBatchDisable = () => {
  if (!selectedRowKeys.value?.length) {
    message.warning('请选择要下架的产品')
    return
  }

  Modal.confirm({
    title: '确认批量下架',
    content: `确定要下架选中的 ${selectedRowKeys.value.length} 个产品吗？`,
    onOk: async () => {
      try {
        await ProductApi.batchUpdateStatus(selectedRowKeys.value!, ProductStatus.DISABLED)
        message.success('批量下架成功')
        selectedRowKeys.value = []
        selectedRows.value = []
        fetchDataList()
      } catch (error) {
        console.error('批量下架失败:', error)
        message.error('批量下架失败')
      }
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value?.length) {
    message.warning('请选择要删除的产品')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个产品吗？删除后不可恢复！`,
    onOk: async () => {
      try {
        await ProductApi.batchDelete(selectedRowKeys.value!)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        selectedRows.value = []
        fetchDataList()
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败')
      }
    }
  })
}

// 导出数据
const handleExport = async () => {
  try {
    const blob = await ProductApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `产品数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 导入数据
const handleImport = (file: File) => {
  Modal.confirm({
    title: '确认导入',
    content: '导入数据将覆盖现有数据，确定要继续吗？',
    onOk: async () => {
      try {
        await ProductApi.import(file)
        message.success('导入成功')
        fetchDataList()
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败')
      }
    }
  })
  return false // 阻止自动上传
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true

    if (formData.id) {
      // 编辑
      await ProductApi.update(formData.id, formData as ProductUpdateRequest)
      message.success('更新成功')
    } else {
      // 新增
      await ProductApi.create(formData as ProductCreateRequest)
      message.success('创建成功')
    }

    modalVisible.value = false
    fetchDataList()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消表单
const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: undefined,
    productName: '',
    productCode: '',
    categoryId: undefined,
    productType: DEFAULT_PRODUCT.productType,
    brand: '',
    model: '',
    specification: '',
    unit: DEFAULT_PRODUCT.unit,
    purchasePrice: DEFAULT_PRODUCT.purchasePrice,
    salePrice: DEFAULT_PRODUCT.salePrice,
    marketPrice: undefined,
    costPrice: undefined,
    weight: undefined,
    volume: undefined,
    barcode: '',
    qrcode: '',
    images: [],
    description: '',
    status: DEFAULT_PRODUCT.status,
    stockQuantity: DEFAULT_PRODUCT.stockQuantity,
    minStock: DEFAULT_PRODUCT.minStock,
    maxStock: undefined,
    stockAlertType: DEFAULT_PRODUCT.stockAlertType,
    remark: ''
  })
  formRef.value?.resetFields()
}

// 查看库存
const handleViewStock = (record: Product) => {
  currentProduct.value = record
  stockAdjustment.quantity = 0
  stockAdjustment.type = 'ADJUST'
  stockAdjustment.remark = ''
  stockModalVisible.value = true
}

// 库存调整
const handleStockAdjust = async () => {
  if (!currentProduct.value || stockAdjustment.quantity === 0) {
    message.warning('请输入调整数量')
    return
  }

  try {
    await ProductStockManager.updateStock(
      currentProduct.value.id,
      stockAdjustment.quantity,
      stockAdjustment.type,
      stockAdjustment.remark
    )
    message.success('库存调整成功')
    stockModalVisible.value = false
    fetchDataList()
  } catch (error) {
    console.error('库存调整失败:', error)
    message.error('库存调整失败')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategoryOptions()
  fetchDataList()
})
</script>

<style scoped>
.product-management {
  padding: 16px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 0;
}

.search-form .ant-form-item {
  margin-bottom: 16px;
}

.product-images {
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image {
  color: #999;
  font-size: 12px;
}

.product-info {
  .product-name {
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .product-code {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 2px;
  }

  .product-category {
    font-size: 12px;
    color: #1890ff;
  }
}

.price-info {
  .sale-price {
    color: #f5222d;
    font-weight: 500;
    margin-bottom: 2px;
  }

  .purchase-price {
    color: #52c41a;
    font-size: 12px;
  }
}

.stock-info {
  .stock-quantity {
    font-weight: 500;
    margin-bottom: 2px;
  }

  .min-stock {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 4px;
  }
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-upload) {
  display: inline-block;
}
</style>
