<!--
  商品管理页面
  @description 商品列表、添加、编辑、删除、状态管理等功能
-->
<template>
  <div class="product-management">
    <a-card :bordered="false">
      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="商品名称" name="name">
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入商品名称"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="SPU编码" name="spuCode">
            <a-input
              v-model:value="searchForm.spuCode"
              placeholder="请输入SPU编码"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="商品分类" name="categoryId">
            <a-tree-select
              v-model:value="searchForm.categoryId"
              :tree-data="categoryTree"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              placeholder="请选择商品分类"
              allow-clear
              tree-default-expand-all
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="商品状态" name="status">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择商品状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option
                v-for="status in productStatusOptions"
                :key="status.value"
                :value="status.value"
              >
                {{ status.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <PlusOutlined />
            添加商品
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchOnSale"
          >
            <ShopOutlined />
            批量上架
          </a-button>
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchOffSale"
          >
            <ShopOutlined />
            批量下架
          </a-button>
          <a-button
            danger
            :disabled="!hasSelected"
            @click="handleBatchDelete"
          >
            <DeleteOutlined />
            批量删除
          </a-button>
          <a-button @click="handleExport">
            <ExportOutlined />
            导出
          </a-button>
        </a-space>
      </div>

      <!-- 商品表格 -->
      <a-table
        ref="tableRef"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1500 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 商品信息 -->
        <template #productInfo="{ record }">
          <div class="product-info">
            <div class="product-image">
              <a-image
                :width="60"
                :height="60"
                :src="record.images?.[0]"
                :preview="false"
                fallback="/images/no-image.png"
              />
            </div>
            <div class="product-details">
              <div class="product-name">{{ record.name }}</div>
              <div class="product-code">SPU: {{ record.spuCode }}</div>
              <div v-if="record.subTitle" class="product-subtitle">
                {{ record.subTitle }}
              </div>
            </div>
          </div>
        </template>

        <!-- 价格信息 -->
        <template #priceInfo="{ record }">
          <div class="price-info">
            <div class="price-range">
              ¥{{ record.minPrice }}
              <span v-if="record.minPrice !== record.maxPrice">
                - ¥{{ record.maxPrice }}
              </span>
            </div>
            <div v-if="record.marketPrice" class="market-price">
              市场价: ¥{{ record.marketPrice }}
            </div>
          </div>
        </template>

        <!-- 库存信息 -->
        <template #stockInfo="{ record }">
          <div class="stock-info">
            <div class="stock-count">库存: {{ record.totalStock || 0 }}</div>
            <div class="sales-count">销量: {{ record.totalSales || 0 }}</div>
          </div>
        </template>

        <!-- 商品状态 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 商品标签 -->
        <template #tags="{ record }">
          <a-space>
            <a-tag v-if="record.isHot" color="red">热门</a-tag>
            <a-tag v-if="record.isNew" color="green">新品</a-tag>
            <a-tag v-if="record.isRecommend" color="blue">推荐</a-tag>
          </a-space>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                更多
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handleCopy(record)">
                    <CopyOutlined />
                    复制
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status !== 1"
                    @click="handleOnSale(record)"
                  >
                    <ShopOutlined />
                    上架
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status === 1"
                    @click="handleOffSale(record)"
                  >
                    <ShopOutlined />
                    下架
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item danger @click="handleDelete(record)">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 商品详情抽屉 -->
    <ProductDetailDrawer
      v-model:open="detailDrawerVisible"
      :product-id="currentProductId"
    />

    <!-- 商品编辑抽屉 -->
    <ProductEditDrawer
      v-model:open="editDrawerVisible"
      :product-id="currentProductId"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ShopOutlined,
  DeleteOutlined,
  ExportOutlined,
  DownOutlined,
  CopyOutlined
} from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import {
  getProductPage,
  deleteProduct,
  deleteProductBatch,
  onSaleProduct,
  offSaleProduct,
  onSaleProductBatch,
  offSaleProductBatch,
  copyProduct
} from '@/api/product/product'
import { getCategoryTree } from '@/api/product/category'
import ProductDetailDrawer from './components/ProductDetailDrawer.vue'
import ProductEditDrawer from './components/ProductEditDrawer.vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const dataSource = ref<any[]>([])
const selectedRowKeys = ref<string[]>([])
const categoryTree = ref<any[]>([])
const detailDrawerVisible = ref(false)
const editDrawerVisible = ref(false)
const currentProductId = ref<number | null>(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  name: '',
  spuCode: '',
  categoryId: undefined,
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 商品状态选项
const productStatusOptions = [
  { label: '出售中', value: 1 },
  { label: '待上架', value: 2 },
  { label: '库存中', value: 3 },
  { label: '已售罄', value: 4 },
  { label: '库存警戒', value: 5 },
  { label: '回收站', value: 6 }
]

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '商品信息',
    key: 'productInfo',
    width: 300,
    fixed: 'left',
    slots: { customRender: 'productInfo' }
  },
  {
    title: '分类',
    dataIndex: 'categoryName',
    width: 120
  },
  {
    title: '价格',
    key: 'priceInfo',
    width: 150,
    slots: { customRender: 'priceInfo' }
  },
  {
    title: '库存/销量',
    key: 'stockInfo',
    width: 120,
    slots: { customRender: 'stockInfo' }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '标签',
    key: 'tags',
    width: 150,
    slots: { customRender: 'tags' }
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

/**
 * 获取状态颜色
 */
const getStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    1: 'green',    // 出售中
    2: 'orange',   // 待上架
    3: 'blue',     // 库存中
    4: 'red',      // 已售罄
    5: 'volcano',  // 库存警戒
    6: 'default'   // 回收站
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number): string => {
  const textMap: Record<number, string> = {
    1: '出售中',
    2: '待上架',
    3: '库存中',
    4: '已售罄',
    5: '库存警戒',
    6: '回收站'
  }
  return textMap[status] || '未知'
}

/**
 * 加载商品列表
 */
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      pageNo: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const { data } = await getProductPage(params)
    dataSource.value = data.list || []
    pagination.total = data.total || 0
  } catch (error) {
    console.error('加载商品列表失败:', error)
    message.error('加载商品列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载分类树
 */
const loadCategoryTree = async () => {
  try {
    const { data } = await getCategoryTree()
    categoryTree.value = data || []
  } catch (error) {
    console.error('加载分类树失败:', error)
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

/**
 * 表格变化处理
 */
const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  loadData()
}

/**
 * 添加商品
 */
const handleAdd = () => {
  router.push('/product/product/add')
}

/**
 * 查看商品
 */
const handleView = (record: any) => {
  currentProductId.value = record.id
  detailDrawerVisible.value = true
}

/**
 * 编辑商品
 */
const handleEdit = (record: any) => {
  router.push(`/product/product/edit/${record.id}`)
}

/**
 * 编辑成功回调
 */
const handleEditSuccess = () => {
  loadData()
}

/**
 * 删除商品
 */
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除商品"${record.name}"吗？`,
    onOk: async () => {
      try {
        await deleteProduct(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

/**
 * 批量删除
 */
const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个商品吗？`,
    onOk: async () => {
      try {
        await deleteProductBatch(selectedRowKeys.value.map(Number))
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败')
      }
    }
  })
}

/**
 * 上架商品
 */
const handleOnSale = async (record: any) => {
  try {
    await onSaleProduct(record.id)
    message.success('上架成功')
    loadData()
  } catch (error) {
    console.error('上架失败:', error)
    message.error('上架失败')
  }
}

/**
 * 下架商品
 */
const handleOffSale = async (record: any) => {
  try {
    await offSaleProduct(record.id)
    message.success('下架成功')
    loadData()
  } catch (error) {
    console.error('下架失败:', error)
    message.error('下架失败')
  }
}

/**
 * 批量上架
 */
const handleBatchOnSale = async () => {
  try {
    await onSaleProductBatch(selectedRowKeys.value.map(Number))
    message.success('批量上架成功')
    selectedRowKeys.value = []
    loadData()
  } catch (error) {
    console.error('批量上架失败:', error)
    message.error('批量上架失败')
  }
}

/**
 * 批量下架
 */
const handleBatchOffSale = async () => {
  try {
    await offSaleProductBatch(selectedRowKeys.value.map(Number))
    message.success('批量下架成功')
    selectedRowKeys.value = []
    loadData()
  } catch (error) {
    console.error('批量下架失败:', error)
    message.error('批量下架失败')
  }
}

/**
 * 复制商品
 */
const handleCopy = async (record: any) => {
  try {
    const { data } = await copyProduct(record.id)
    message.success('复制成功')
    loadData()
    // 跳转到编辑页面
    router.push(`/product/product/edit/${data}`)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

/**
 * 导出
 */
const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中...')
}

// 页面加载时初始化数据
onMounted(() => {
  loadData()
  loadCategoryTree()
})
</script>

<style scoped lang="less">
.product-management {
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .action-buttons {
    margin-bottom: 16px;
  }
  
  .product-info {
    display: flex;
    align-items: center;
    
    .product-image {
      margin-right: 12px;
      
      .ant-image {
        border-radius: 4px;
        overflow: hidden;
      }
    }
    
    .product-details {
      flex: 1;
      
      .product-name {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
        line-height: 1.4;
      }
      
      .product-code {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 2px;
      }
      
      .product-subtitle {
        font-size: 12px;
        color: #595959;
        line-height: 1.3;
      }
    }
  }
  
  .price-info {
    .price-range {
      font-weight: 500;
      color: #f5222d;
      margin-bottom: 4px;
    }
    
    .market-price {
      font-size: 12px;
      color: #8c8c8c;
      text-decoration: line-through;
    }
  }
  
  .stock-info {
    .stock-count {
      margin-bottom: 4px;
    }
    
    .sales-count {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}
</style>
