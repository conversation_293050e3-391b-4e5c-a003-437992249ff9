<!--
  商品状态管理组件
  @description 实现商品状态的完整生命周期管理
-->
<template>
  <div class="product-status-manager">
    <a-card title="商品状态管理" :bordered="false">
      <!-- 状态统计 -->
      <div class="status-statistics">
        <a-row :gutter="16">
          <a-col
            v-for="stat in statusStats"
            :key="stat.status"
            :span="4"
          >
            <a-card
              size="small"
              :class="['status-card', `status-${stat.status}`]"
              @click="handleFilterByStatus(stat.status)"
            >
              <div class="status-info">
                <div class="status-icon">
                  <Icon :icon="stat.icon" :size="24" />
                </div>
                <div class="status-details">
                  <div class="status-name">{{ stat.name }}</div>
                  <div class="status-count">{{ stat.count }}</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 状态流转图 -->
      <div class="status-flow">
        <h4>状态流转图</h4>
        <div class="flow-diagram">
          <div
            v-for="(flow, index) in statusFlows"
            :key="index"
            class="flow-item"
          >
            <div class="flow-node">
              <a-tag :color="getStatusColor(flow.from)">
                {{ getStatusName(flow.from) }}
              </a-tag>
            </div>
            <div class="flow-arrow">
              <ArrowRightOutlined />
            </div>
            <div class="flow-node">
              <a-tag :color="getStatusColor(flow.to)">
                {{ getStatusName(flow.to) }}
              </a-tag>
            </div>
            <div class="flow-action">
              <a-button
                size="small"
                type="link"
                @click="handleStatusChange(flow)"
              >
                {{ flow.action }}
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 批量状态操作 -->
      <div class="batch-operations">
        <h4>批量操作</h4>
        <a-space>
          <a-select
            v-model:value="batchStatus"
            placeholder="选择目标状态"
            style="width: 150px"
          >
            <a-select-option
              v-for="status in statusOptions"
              :key="status.value"
              :value="status.value"
            >
              {{ status.label }}
            </a-select-option>
          </a-select>
          
          <a-button
            type="primary"
            :disabled="!batchStatus || selectedProducts.length === 0"
            @click="handleBatchStatusChange"
          >
            批量更新状态
          </a-button>
          
          <a-button @click="handleBatchOnSale">
            批量上架
          </a-button>
          
          <a-button @click="handleBatchOffSale">
            批量下架
          </a-button>
        </a-space>
      </div>

      <!-- 状态变更记录 -->
      <div class="status-history">
        <h4>状态变更记录</h4>
        <a-table
          :columns="historyColumns"
          :data-source="statusHistory"
          :pagination="false"
          size="small"
        >
          <template #status="{ record }">
            <a-space>
              <a-tag :color="getStatusColor(record.fromStatus)">
                {{ getStatusName(record.fromStatus) }}
              </a-tag>
              <ArrowRightOutlined />
              <a-tag :color="getStatusColor(record.toStatus)">
                {{ getStatusName(record.toStatus) }}
              </a-tag>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 状态变更确认弹窗 -->
    <a-modal
      v-model:open="statusChangeModalVisible"
      title="确认状态变更"
      @ok="confirmStatusChange"
      @cancel="cancelStatusChange"
    >
      <div v-if="pendingStatusChange">
        <p>
          确定要将商品状态从
          <a-tag :color="getStatusColor(pendingStatusChange.from)">
            {{ getStatusName(pendingStatusChange.from) }}
          </a-tag>
          变更为
          <a-tag :color="getStatusColor(pendingStatusChange.to)">
            {{ getStatusName(pendingStatusChange.to) }}
          </a-tag>
          吗？
        </p>
        
        <a-form layout="vertical">
          <a-form-item label="变更原因">
            <a-textarea
              v-model:value="statusChangeReason"
              placeholder="请输入状态变更原因"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
import { Icon } from '@iconify/vue'
import {
  getProductStatusStats,
  updateProductStatus,
  updateProductStatusBatch,
  getStatusChangeHistory
} from '@/api/product/product'

// 组件属性
interface Props {
  selectedProducts?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  selectedProducts: () => []
})

// 组件事件
const emit = defineEmits<{
  statusFilter: [status: number | null]
  statusChanged: []
}>()

// 响应式数据
const statusStats = ref<any[]>([])
const statusHistory = ref<any[]>([])
const batchStatus = ref<number | undefined>()
const statusChangeModalVisible = ref(false)
const pendingStatusChange = ref<any>(null)
const statusChangeReason = ref('')

// 状态选项
const statusOptions = [
  { label: '出售中', value: 1 },
  { label: '待上架', value: 2 },
  { label: '库存中', value: 3 },
  { label: '已售罄', value: 4 },
  { label: '库存警戒', value: 5 },
  { label: '回收站', value: 6 }
]

// 状态流转配置
const statusFlows = [
  { from: 2, to: 1, action: '上架', icon: 'mdi:arrow-up' },
  { from: 1, to: 2, action: '下架', icon: 'mdi:arrow-down' },
  { from: 3, to: 1, action: '上架销售', icon: 'mdi:cart' },
  { from: 1, to: 4, action: '售罄', icon: 'mdi:close-circle' },
  { from: 4, to: 3, action: '补货', icon: 'mdi:package-variant' },
  { from: 1, to: 5, action: '库存预警', icon: 'mdi:alert' },
  { from: 5, to: 1, action: '补货上架', icon: 'mdi:check-circle' },
  { from: 2, to: 6, action: '删除', icon: 'mdi:delete' },
  { from: 6, to: 2, action: '恢复', icon: 'mdi:restore' }
]

// 状态历史表格列
const historyColumns = [
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 200
  },
  {
    title: '状态变更',
    key: 'status',
    width: 200,
    slots: { customRender: 'status' }
  },
  {
    title: '变更原因',
    dataIndex: 'reason',
    width: 150
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 100
  },
  {
    title: '变更时间',
    dataIndex: 'createTime',
    width: 180
  }
]

/**
 * 获取状态颜色
 */
const getStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    1: 'green',    // 出售中
    2: 'orange',   // 待上架
    3: 'blue',     // 库存中
    4: 'red',      // 已售罄
    5: 'volcano',  // 库存警戒
    6: 'default'   // 回收站
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态名称
 */
const getStatusName = (status: number): string => {
  const nameMap: Record<number, string> = {
    1: '出售中',
    2: '待上架',
    3: '库存中',
    4: '已售罄',
    5: '库存警戒',
    6: '回收站'
  }
  return nameMap[status] || '未知'
}

/**
 * 加载状态统计
 */
const loadStatusStats = async () => {
  try {
    const { data } = await getProductStatusStats()
    statusStats.value = [
      { status: 1, name: '出售中', count: data.onSale || 0, icon: 'mdi:cart' },
      { status: 2, name: '待上架', count: data.pending || 0, icon: 'mdi:clock' },
      { status: 3, name: '库存中', count: data.inStock || 0, icon: 'mdi:package' },
      { status: 4, name: '已售罄', count: data.soldOut || 0, icon: 'mdi:close-circle' },
      { status: 5, name: '库存警戒', count: data.lowStock || 0, icon: 'mdi:alert' },
      { status: 6, name: '回收站', count: data.recycled || 0, icon: 'mdi:delete' }
    ]
  } catch (error) {
    console.error('加载状态统计失败:', error)
  }
}

/**
 * 加载状态变更历史
 */
const loadStatusHistory = async () => {
  try {
    const { data } = await getStatusChangeHistory({ pageSize: 10 })
    statusHistory.value = data.list || []
  } catch (error) {
    console.error('加载状态历史失败:', error)
  }
}

/**
 * 按状态筛选
 */
const handleFilterByStatus = (status: number) => {
  emit('statusFilter', status)
}

/**
 * 状态变更
 */
const handleStatusChange = (flow: any) => {
  pendingStatusChange.value = flow
  statusChangeModalVisible.value = true
}

/**
 * 确认状态变更
 */
const confirmStatusChange = async () => {
  if (!pendingStatusChange.value) return
  
  try {
    // 这里需要根据实际业务逻辑处理状态变更
    // 可能需要选择具体的商品进行状态变更
    message.success('状态变更成功')
    statusChangeModalVisible.value = false
    pendingStatusChange.value = null
    statusChangeReason.value = ''
    
    // 刷新数据
    loadStatusStats()
    loadStatusHistory()
    emit('statusChanged')
  } catch (error) {
    console.error('状态变更失败:', error)
    message.error('状态变更失败')
  }
}

/**
 * 取消状态变更
 */
const cancelStatusChange = () => {
  statusChangeModalVisible.value = false
  pendingStatusChange.value = null
  statusChangeReason.value = ''
}

/**
 * 批量状态变更
 */
const handleBatchStatusChange = async () => {
  if (!batchStatus.value || props.selectedProducts.length === 0) {
    message.warning('请选择商品和目标状态')
    return
  }
  
  try {
    await updateProductStatusBatch({
      ids: props.selectedProducts,
      status: batchStatus.value
    })
    
    message.success('批量状态更新成功')
    batchStatus.value = undefined
    
    // 刷新数据
    loadStatusStats()
    loadStatusHistory()
    emit('statusChanged')
  } catch (error) {
    console.error('批量状态更新失败:', error)
    message.error('批量状态更新失败')
  }
}

/**
 * 批量上架
 */
const handleBatchOnSale = async () => {
  if (props.selectedProducts.length === 0) {
    message.warning('请选择要上架的商品')
    return
  }
  
  try {
    await updateProductStatusBatch({
      ids: props.selectedProducts,
      status: 1 // 出售中
    })
    
    message.success('批量上架成功')
    
    // 刷新数据
    loadStatusStats()
    loadStatusHistory()
    emit('statusChanged')
  } catch (error) {
    console.error('批量上架失败:', error)
    message.error('批量上架失败')
  }
}

/**
 * 批量下架
 */
const handleBatchOffSale = async () => {
  if (props.selectedProducts.length === 0) {
    message.warning('请选择要下架的商品')
    return
  }
  
  try {
    await updateProductStatusBatch({
      ids: props.selectedProducts,
      status: 2 // 待上架
    })
    
    message.success('批量下架成功')
    
    // 刷新数据
    loadStatusStats()
    loadStatusHistory()
    emit('statusChanged')
  } catch (error) {
    console.error('批量下架失败:', error)
    message.error('批量下架失败')
  }
}

// 页面加载时初始化数据
onMounted(() => {
  loadStatusStats()
  loadStatusHistory()
})
</script>

<style scoped lang="less">
.product-status-manager {
  .status-statistics {
    margin-bottom: 24px;
    
    .status-card {
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .status-info {
        display: flex;
        align-items: center;
        
        .status-icon {
          margin-right: 12px;
          color: #1890ff;
        }
        
        .status-details {
          .status-name {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }
          
          .status-count {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
          }
        }
      }
      
      &.status-1 .status-icon { color: #52c41a; }
      &.status-2 .status-icon { color: #fa8c16; }
      &.status-3 .status-icon { color: #1890ff; }
      &.status-4 .status-icon { color: #f5222d; }
      &.status-5 .status-icon { color: #fa541c; }
      &.status-6 .status-icon { color: #8c8c8c; }
    }
  }
  
  .status-flow {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .flow-diagram {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .flow-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        background: #fafafa;
        
        .flow-node {
          margin: 0 8px;
        }
        
        .flow-arrow {
          color: #8c8c8c;
        }
        
        .flow-action {
          margin-left: 12px;
        }
      }
    }
  }
  
  .batch-operations {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .status-history {
    h4 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
