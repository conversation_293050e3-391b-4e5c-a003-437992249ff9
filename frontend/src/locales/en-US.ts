export default {
  // Common
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    save: 'Save',
    reset: 'Reset',
    search: 'Search',
    clear: 'Clear',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    upload: 'Upload',
    download: 'Download',
    view: 'View',
    detail: 'Detail',
    back: 'Back',
    submit: 'Submit',
    loading: 'Loading...',
    noData: 'No Data',
    total: 'Total {total} items',
    selected: '{count} items selected',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    remark: 'Remark'
  },

  // Menu
  menu: {
    dashboard: 'Dashboard',
    member: 'Member Management',
    memberUser: 'User List',
    memberRole: 'Role Management',
    memberPermission: 'Permission Management',
    product: 'Product Management',
    productList: 'Product List',
    productCategory: 'Product Category',
    productBrand: 'Brand Management',
    inventory: 'Inventory Management',
    inventoryStock: 'Stock Query',
    inventoryAlert: 'Stock Alert',
    inventoryLog: 'Inventory Log',
    order: 'Order Management',
    orderList: 'Order List',
    orderDetail: 'Order Detail',
    platform: 'Platform Integration',
    platformIntegration: 'Platform Config',
    platformSync: 'Data Sync',
    system: 'System Management',
    systemTenant: 'Tenant Management',
    systemConfig: 'System Config'
  },

  // Login
  login: {
    title: 'User Login',
    username: 'Username',
    password: 'Password',
    remember: 'Remember Password',
    forgot: 'Forgot Password',
    login: 'Login',
    loginSuccess: 'Login Success',
    loginFailed: 'Login Failed',
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    usernameRule: 'Username length should be 3 to 20 characters',
    passwordRule: 'Password length should be 6 to 20 characters'
  },

  // User Management
  user: {
    list: 'User List',
    add: 'Add User',
    edit: 'Edit User',
    delete: 'Delete User',
    username: 'Username',
    nickname: 'Nickname',
    email: 'Email',
    phone: 'Phone',
    status: 'Status',
    role: 'Role',
    lastLoginTime: 'Last Login Time',
    active: 'Active',
    inactive: 'Inactive',
    locked: 'Locked',
    deleteConfirm: 'Are you sure to delete this user?',
    deleteSuccess: 'Delete Success',
    saveSuccess: 'Save Success'
  },

  // Product Management
  product: {
    list: 'Product List',
    add: 'Add Product',
    edit: 'Edit Product',
    delete: 'Delete Product',
    name: 'Product Name',
    code: 'Product Code',
    category: 'Category',
    brand: 'Brand',
    price: 'Price',
    stock: 'Stock',
    status: 'Status',
    onSale: 'On Sale',
    offSale: 'Off Sale',
    draft: 'Draft',
    deleteConfirm: 'Are you sure to delete this product?'
  },

  // Inventory Management
  inventory: {
    stock: 'Stock Query',
    alert: 'Stock Alert',
    log: 'Inventory Log',
    warehouse: 'Warehouse',
    quantity: 'Quantity',
    availableQuantity: 'Available Quantity',
    lockedQuantity: 'Locked Quantity',
    alertType: 'Alert Type',
    lowStock: 'Low Stock',
    overStock: 'Over Stock',
    zeroStock: 'Zero Stock',
    negativeStock: 'Negative Stock'
  },

  // Order Management
  order: {
    list: 'Order List',
    detail: 'Order Detail',
    orderNo: 'Order No',
    customer: 'Customer',
    amount: 'Amount',
    status: 'Status',
    createTime: 'Create Time',
    payTime: 'Pay Time',
    pending: 'Pending',
    paid: 'Paid',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled'
  },

  // System Management
  system: {
    tenant: 'Tenant Management',
    config: 'System Config',
    tenantName: 'Tenant Name',
    tenantCode: 'Tenant Code',
    contactPerson: 'Contact Person',
    contactPhone: 'Contact Phone',
    expireTime: 'Expire Time',
    configKey: 'Config Key',
    configValue: 'Config Value',
    configDesc: 'Config Description'
  },

  // Messages
  message: {
    success: 'Operation Success',
    error: 'Operation Failed',
    warning: 'Warning',
    info: 'Info',
    confirm: 'Confirm',
    deleteConfirm: 'Are you sure to delete?',
    saveConfirm: 'Are you sure to save?',
    networkError: 'Network error, please try again later',
    serverError: 'Server error, please contact administrator',
    unauthorized: 'Unauthorized, please login again',
    forbidden: 'Insufficient permissions',
    notFound: 'Resource not found'
  }
}
