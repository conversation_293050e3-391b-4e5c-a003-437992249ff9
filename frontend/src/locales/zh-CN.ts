export default {
  // 通用
  common: {
    ok: '确定',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    add: '新增',
    save: '保存',
    reset: '重置',
    search: '搜索',
    clear: '清空',
    refresh: '刷新',
    export: '导出',
    import: '导入',
    upload: '上传',
    download: '下载',
    view: '查看',
    detail: '详情',
    back: '返回',
    submit: '提交',
    loading: '加载中...',
    noData: '暂无数据',
    total: '共 {total} 条',
    selected: '已选择 {count} 项',
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    remark: '备注'
  },

  // 菜单
  menu: {
    dashboard: '仪表盘',
    member: '用户管理',
    memberUser: '用户列表',
    memberRole: '角色管理',
    memberPermission: '权限管理',
    product: '商品管理',
    productList: '商品列表',
    productCategory: '商品分类',
    productBrand: '品牌管理',
    inventory: '库存管理',
    inventoryStock: '库存查询',
    inventoryAlert: '库存预警',
    inventoryLog: '库存日志',
    order: '订单管理',
    orderList: '订单列表',
    orderDetail: '订单详情',
    platform: '平台集成',
    platformIntegration: '平台配置',
    platformSync: '数据同步',
    system: '系统管理',
    systemTenant: '租户管理',
    systemConfig: '系统配置'
  },

  // 登录
  login: {
    title: '用户登录',
    username: '用户名',
    password: '密码',
    remember: '记住密码',
    forgot: '忘记密码',
    login: '登录',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    usernameRequired: '请输入用户名',
    passwordRequired: '请输入密码',
    usernameRule: '用户名长度在 3 到 20 个字符',
    passwordRule: '密码长度在 6 到 20 个字符'
  },

  // 用户管理
  user: {
    list: '用户列表',
    add: '新增用户',
    edit: '编辑用户',
    delete: '删除用户',
    username: '用户名',
    nickname: '昵称',
    email: '邮箱',
    phone: '手机号',
    status: '状态',
    role: '角色',
    lastLoginTime: '最后登录时间',
    active: '正常',
    inactive: '禁用',
    locked: '锁定',
    deleteConfirm: '确定要删除该用户吗？',
    deleteSuccess: '删除成功',
    saveSuccess: '保存成功'
  },

  // 商品管理
  product: {
    list: '商品列表',
    add: '新增商品',
    edit: '编辑商品',
    delete: '删除商品',
    name: '商品名称',
    code: '商品编码',
    category: '商品分类',
    brand: '品牌',
    price: '价格',
    stock: '库存',
    status: '状态',
    onSale: '在售',
    offSale: '下架',
    draft: '草稿',
    deleteConfirm: '确定要删除该商品吗？'
  },

  // 库存管理
  inventory: {
    stock: '库存查询',
    alert: '库存预警',
    log: '库存日志',
    warehouse: '仓库',
    quantity: '数量',
    availableQuantity: '可用数量',
    lockedQuantity: '锁定数量',
    alertType: '预警类型',
    lowStock: '库存不足',
    overStock: '库存过多',
    zeroStock: '零库存',
    negativeStock: '负库存'
  },

  // 订单管理
  order: {
    list: '订单列表',
    detail: '订单详情',
    orderNo: '订单号',
    customer: '客户',
    amount: '订单金额',
    status: '订单状态',
    createTime: '下单时间',
    payTime: '支付时间',
    pending: '待支付',
    paid: '已支付',
    shipped: '已发货',
    delivered: '已送达',
    cancelled: '已取消'
  },

  // 系统管理
  system: {
    tenant: '租户管理',
    config: '系统配置',
    tenantName: '租户名称',
    tenantCode: '租户编码',
    contactPerson: '联系人',
    contactPhone: '联系电话',
    expireTime: '到期时间',
    configKey: '配置键',
    configValue: '配置值',
    configDesc: '配置描述'
  },

  // 消息提示
  message: {
    success: '操作成功',
    error: '操作失败',
    warning: '警告',
    info: '提示',
    confirm: '确认',
    deleteConfirm: '确定要删除吗？',
    saveConfirm: '确定要保存吗？',
    networkError: '网络错误，请稍后重试',
    serverError: '服务器错误，请联系管理员',
    unauthorized: '未授权，请重新登录',
    forbidden: '权限不足',
    notFound: '资源不存在'
  }
}
