<!--
  权限守卫组件
  用于包装需要权限控制的内容
-->
<template>
  <div v-if="hasPermission" class="permission-guard">
    <slot />
  </div>
  <div v-else-if="showFallback" class="permission-fallback">
    <slot name="fallback">
      <AEmpty
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
        :description="fallbackText"
      >
        <template v-if="showLoginButton && !isLoggedIn">
          <AButton type="primary" @click="handleLogin">
            立即登录
          </AButton>
        </template>
      </AEmpty>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { Empty } from 'ant-design-vue'
import { usePermission } from '@/composables/usePermission'
import type { PermissionConfig } from '@/composables/usePermission'

/**
 * 组件属性
 */
interface Props {
  /** 权限编码 */
  permission?: string | string[]
  /** 角色编码 */
  role?: string | string[]
  /** 检查模式 */
  mode?: 'all' | 'any'
  /** 是否需要登录 */
  requireLogin?: boolean
  /** 是否允许管理员绕过 */
  allowAdmin?: boolean
  /** 是否显示无权限提示 */
  showFallback?: boolean
  /** 无权限提示文本 */
  fallbackText?: string
  /** 是否显示登录按钮 */
  showLoginButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'any',
  requireLogin: true,
  allowAdmin: true,
  showFallback: true,
  fallbackText: '暂无权限访问',
  showLoginButton: true
})

const router = useRouter()
const { checkAuth, isLoggedIn } = usePermission()

// 权限配置
const permissionConfig = computed<PermissionConfig>(() => {
  const { permission, role, mode, requireLogin, allowAdmin } = toRefs(props)
  return {
    permission: permission.value,
    role: role.value,
    mode: mode.value,
    requireLogin: requireLogin.value,
    allowAdmin: allowAdmin.value
  }
})

// 是否有权限
const hasPermission = computed(() => {
  return checkAuth(permissionConfig.value)
})

// 处理登录
const handleLogin = () => {
  router.push({
    path: '/login',
    query: {
      redirect: router.currentRoute.value.fullPath
    }
  })
}
</script>

<style lang="less" scoped>
.permission-guard {
  width: 100%;
  height: 100%;
}

.permission-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 24px;
  
  :deep(.ant-empty) {
    .ant-empty-description {
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
