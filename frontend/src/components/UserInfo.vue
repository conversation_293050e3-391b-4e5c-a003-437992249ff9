<template>
  <div class="user-info-container">
    <!-- 用户头像和基本信息 -->
    <div class="user-header">
      <AAvatar 
        :size="64" 
        :src="authStore.userAvatarUrl"
        class="user-avatar"
      >
        {{ authStore.userDisplayName.charAt(0) }}
      </AAvatar>
      
      <div class="user-basic">
        <h3 class="user-name">{{ authStore.userDisplayName }}</h3>
        <p class="user-email">{{ userInfo?.email }}</p>
        <ATag 
          :color="getStatusColor(userInfo?.accountStatus)"
          class="user-status"
        >
          {{ getStatusText(userInfo?.accountStatus) }}
        </ATag>
      </div>
      
      <div class="user-actions">
        <AButton 
          type="primary" 
          size="small" 
          @click="showUserModal = true"
          :loading="authStore.userInfoLoading"
        >
          查看详情
        </AButton>
        <AButton 
          size="small" 
          @click="handleLogout"
          :loading="authStore.logoutLoading"
        >
          退出登录
        </AButton>
      </div>
    </div>

    <!-- 租户信息 -->
    <div class="tenant-info" v-if="tenantInfo">
      <ADivider>租户信息</ADivider>
      <div class="tenant-item">
        <span class="label">租户名称:</span>
        <span class="value">{{ tenantInfo.name }}</span>
      </div>
      <div class="tenant-item" v-if="tenantInfo.id">
        <span class="label">租户ID:</span>
        <span class="value">{{ tenantInfo.id }}</span>
      </div>
    </div>

    <!-- 权限信息 -->
    <div class="permission-info" v-if="showPermissions && (roles.length > 0 || permissions.length > 0)">
      <ADivider>权限信息</ADivider>
      
      <!-- 角色列表 -->
      <div class="roles-section" v-if="roles.length > 0">
        <div class="section-title">角色:</div>
        <div class="tags-container">
          <ATag 
            v-for="role in roles" 
            :key="role" 
            color="blue"
            class="role-tag"
          >
            {{ role }}
          </ATag>
        </div>
      </div>
      
      <!-- 权限列表 -->
      <div class="permissions-section" v-if="permissions.length > 0">
        <div class="section-title">权限:</div>
        <div class="tags-container">
          <ATag 
            v-for="permission in permissions.slice(0, showAllPermissions ? permissions.length : 5)" 
            :key="permission" 
            color="green"
            class="permission-tag"
          >
            {{ permission }}
          </ATag>
          <AButton 
            v-if="permissions.length > 5"
            type="link" 
            size="small"
            @click="showAllPermissions = !showAllPermissions"
          >
            {{ showAllPermissions ? '收起' : `查看全部(${permissions.length})` }}
          </AButton>
        </div>
      </div>
    </div>

    <!-- 用户详情弹窗 -->
    <AModal
      v-model:open="showUserModal"
      title="用户详细信息"
      :footer="null"
      width="600px"
    >
      <div class="user-detail-content" v-if="userInfo">
        <ADescriptions :column="2" bordered>
          <ADescriptionsItem label="用户ID">
            {{ userInfo.id }}
          </ADescriptionsItem>
          <ADescriptionsItem label="用户名">
            {{ userInfo.username }}
          </ADescriptionsItem>
          <ADescriptionsItem label="昵称">
            {{ userInfo.nickname || '未设置' }}
          </ADescriptionsItem>
          <ADescriptionsItem label="邮箱">
            {{ userInfo.email }}
          </ADescriptionsItem>
          <ADescriptionsItem label="手机号">
            {{ userInfo.phone || '未设置' }}
          </ADescriptionsItem>
          <ADescriptionsItem label="用户类型">
            {{ getUserTypeText(userInfo.userType) }}
          </ADescriptionsItem>
          <ADescriptionsItem label="账户状态">
            <ATag :color="getStatusColor(userInfo.accountStatus)">
              {{ getStatusText(userInfo.accountStatus) }}
            </ATag>
          </ADescriptionsItem>
          <ADescriptionsItem label="租户ID" v-if="userInfo.tenantId">
            {{ userInfo.tenantId }}
          </ADescriptionsItem>
          <ADescriptionsItem label="最后登录" v-if="userInfo.lastLoginTime">
            {{ formatDateTime(userInfo.lastLoginTime) }}
          </ADescriptionsItem>
          <ADescriptionsItem label="创建时间" v-if="userInfo.createTime">
            {{ formatDateTime(userInfo.createTime) }}
          </ADescriptionsItem>
        </ADescriptions>
        
        <!-- 操作按钮 -->
        <div class="user-detail-actions">
          <AButton type="primary" @click="handleEditProfile">
            编辑资料
          </AButton>
          <AButton @click="handleChangePassword">
            修改密码
          </AButton>
          <AButton @click="handleRefreshUserInfo" :loading="authStore.userInfoLoading">
            刷新信息
          </AButton>
        </div>
      </div>
    </AModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { useAuthStore } from '@/stores/auth'
import { getUserTypeText, getAccountStatusText } from '@/api/auth'
import type { UserInfo, TenantInfo } from '@/types/auth'

// ========== Props定义 ==========
interface Props {
  // 是否显示权限信息
  showPermissions?: boolean
  // 是否紧凑模式
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPermissions: true,
  compact: false
})

// ========== 组合式API ==========
const router = useRouter()
const authStore = useAuthStore()

// ========== 响应式数据 ==========
const showUserModal = ref(false)
const showAllPermissions = ref(false)

// ========== 计算属性 ==========
const userInfo = computed(() => authStore.userInfo)
const tenantInfo = computed(() => authStore.tenantInfo)
const roles = computed(() => authStore.roles)
const permissions = computed(() => authStore.permissions)

// ========== 方法定义 ==========

/**
 * 获取账户状态颜色
 */
const getStatusColor = (status?: number): string => {
  const colorMap: Record<number, string> = {
    1: 'success',  // 正常
    2: 'warning',  // 锁定
    3: 'error',    // 禁用
    4: 'processing' // 待激活
  }
  return colorMap[status || 1] || 'default'
}

/**
 * 获取账户状态文本
 */
const getStatusText = (status?: number): string => {
  return getAccountStatusText(status || 1)
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string): string => {
  try {
    return new Date(dateTime).toLocaleString('zh-CN')
  } catch {
    return dateTime
  }
}

/**
 * 处理登出
 */
const handleLogout = async () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await authStore.logout()
      router.push('/login')
    }
  })
}

/**
 * 刷新用户信息
 */
const handleRefreshUserInfo = async () => {
  const success = await authStore.fetchUserInfo()
  if (success) {
    message.success('用户信息已刷新')
  }
}

/**
 * 编辑用户资料
 */
const handleEditProfile = () => {
  message.info('编辑资料功能开发中...')
  // TODO: 跳转到用户资料编辑页面
  // router.push('/profile/edit')
}

/**
 * 修改密码
 */
const handleChangePassword = () => {
  message.info('修改密码功能开发中...')
  // TODO: 跳转到密码修改页面
  // router.push('/profile/change-password')
}
</script>

<style lang="less" scoped>
.user-info-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;

  .user-avatar {
    flex-shrink: 0;
  }

  .user-basic {
    flex: 1;

    .user-name {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }

    .user-email {
      margin: 0 0 8px 0;
      color: #8c8c8c;
      font-size: 14px;
    }

    .user-status {
      margin: 0;
    }
  }

  .user-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.tenant-info {
  .tenant-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #8c8c8c;
      font-size: 14px;
    }

    .value {
      color: #262626;
      font-weight: 500;
    }
  }
}

.permission-info {
  .section-title {
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
  }

  .tags-container {
    margin-bottom: 16px;

    .role-tag,
    .permission-tag {
      margin-bottom: 4px;
    }
  }

  .roles-section {
    margin-bottom: 16px;
  }
}

.user-detail-content {
  .user-detail-actions {
    margin-top: 24px;
    text-align: center;

    .ant-btn {
      margin: 0 8px;
    }
  }
}

// 紧凑模式样式
.compact {
  .user-header {
    margin-bottom: 8px;

    .user-basic {
      .user-name {
        font-size: 16px;
      }
    }

    .user-actions {
      flex-direction: row;
    }
  }

  .tenant-info,
  .permission-info {
    margin-top: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    text-align: center;

    .user-actions {
      flex-direction: row;
      justify-content: center;
    }
  }

  .tenant-info {
    .tenant-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
