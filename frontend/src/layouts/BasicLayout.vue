<template>
  <ALayout class="basic-layout">
    <!-- 侧边栏 -->
    <ALayoutSider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="siderWidth"
      :collapsed-width="collapsedWidth"
      class="layout-sider"
    >
      <!-- Logo -->
      <div class="logo">
        <div class="logo-icon">📊</div>
        <span v-show="!collapsed" class="logo-text">VisThink ERP</span>
      </div>

      <!-- 菜单 -->
      <AMenu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        :inline-collapsed="collapsed"
        class="layout-menu"
      >
        <template v-for="route in menuRoutes" :key="route.name">
          <ASubMenu v-if="route.children && route.children.length > 0" :key="route.name">
            <template #title>
              <component :is="getIcon(route.meta?.icon)" />
              <span>{{ route.meta?.title }}</span>
            </template>
            <AMenuItem
              v-for="child in route.children"
              :key="child.name"
              @click="handleMenuClick(child.path)"
            >
              {{ child.meta?.title }}
            </AMenuItem>
          </ASubMenu>
          <AMenuItem v-else :key="route.name" @click="handleMenuClick(route.path)">
            <component :is="getIcon(route.meta?.icon)" />
            <span>{{ route.meta?.title }}</span>
          </AMenuItem>
        </template>
      </AMenu>
    </ALayoutSider>

    <!-- 主内容区 -->
    <ALayout class="layout-content">
      <!-- 头部 -->
      <ALayoutHeader class="layout-header">
        <div class="header-left">
          <MenuUnfoldOutlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <MenuFoldOutlined
            v-else
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />

          <!-- 面包屑导航 -->
          <ABreadcrumb class="breadcrumb">
            <ABreadcrumbItem v-for="item in breadcrumbItems" :key="item.path">
              {{ item.title }}
            </ABreadcrumbItem>
          </ABreadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息组件 -->
          <UserInfo :compact="true" />
        </div>
      </ALayoutHeader>

      <!-- 主要内容 -->
      <ALayoutContent class="layout-main">
        <div class="content-wrapper">
          <RouterView />
        </div>
      </ALayoutContent>

      <!-- 底部 -->
      <ALayoutFooter class="layout-footer">
        <div class="footer-content">
          VisThink ERP ©2024 Created by VisThink Team
        </div>
      </ALayoutFooter>
    </ALayout>
  </ALayout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import UserInfo from '@/components/UserInfo.vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  ApiOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 侧边栏折叠状态
const collapsed = ref(false)
const siderWidth = 200
const collapsedWidth = 80

// 菜单状态
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 获取菜单路由（过滤掉不需要显示的路由）
const menuRoutes = computed(() => {
  return router.getRoutes().filter(route => {
    // 过滤条件：
    // 1. 不在菜单中隐藏
    // 2. 需要认证（业务页面）
    // 3. 排除特殊路由
    return !route.meta?.hideInMenu &&
           route.meta?.requiresAuth &&
           route.path !== '/' &&
           route.path !== '/login' &&
           route.path !== '/test/auth' &&
           !route.path.includes('*') &&
           !route.path.includes('403') &&
           !route.path.includes('404')
  }).filter(route => {
    // 权限过滤：检查用户是否有访问权限
    if (route.meta?.permissions && Array.isArray(route.meta.permissions)) {
      return authStore.hasAnyPermission(route.meta.permissions)
    }
    return true
  })
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title
  }))
})

// 图标映射
const iconMap: Record<string, any> = {
  home: HomeOutlined,
  dashboard: DashboardOutlined,
  user: UserOutlined,
  shopping: ShoppingOutlined,
  database: DatabaseOutlined,
  'file-text': FileTextOutlined,
  api: ApiOutlined,
  setting: SettingOutlined
}

const getIcon = (iconName?: string) => {
  return iconName ? iconMap[iconName] || HomeOutlined : HomeOutlined
}

// 菜单点击处理
const handleMenuClick = (path: string) => {
  router.push(path)
}

// 监听路由变化，更新菜单选中状态
watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [route.name as string]

    // 设置展开的子菜单
    const pathSegments = newPath.split('/').filter(Boolean)
    if (pathSegments.length > 1) {
      openKeys.value = [pathSegments[0]]
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.basic-layout {
  height: 100vh;
}

.layout-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;

  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    margin: 16px;
    border-radius: 6px;

    .logo-icon {
      font-size: 24px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .logo-text {
      color: white;
      font-weight: bold;
      margin-left: 12px;
      font-size: 16px;
    }
  }

  .layout-menu {
    border-right: none;
  }
}

.layout-content {
  margin-left: 200px;
  transition: margin-left 0.2s;

  :global(.ant-layout-sider-collapsed) + & {
    margin-left: 80px;
  }
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 10;

  .header-left {
    display: flex;
    align-items: center;

    .trigger {
      font-size: 18px;
      line-height: 64px;
      padding: 0 24px;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }

    .breadcrumb {
      margin-left: 24px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}

.layout-main {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  min-height: calc(100vh - 64px - 70px - 48px);

  .content-wrapper {
    min-height: 100%;
  }
}

.layout-footer {
  text-align: center;
  background: #f0f2f5;
  padding: 12px 0;

  .footer-content {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
