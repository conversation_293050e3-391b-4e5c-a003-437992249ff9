/**
 * 路由守卫
 * 处理JWT认证、权限验证、路由跳转等逻辑
 */
import type { Router } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAuthStore } from '@/stores/auth'
import { StorageKeys } from '@/types/auth'

// 白名单路由（不需要认证的路由）
const WHITE_LIST = ['/login', '/register', '/forgot-password', '/404', '/403']

// 需要权限验证的路由配置
const PERMISSION_ROUTES: Record<string, string[]> = {
  '/admin': ['admin'],
  '/user-management': ['user:list', 'user:view'],
  '/role-management': ['role:list', 'role:view'],
  '/permission-management': ['permission:list', 'permission:view']
}

/**
 * 设置路由守卫
 * @param router Vue Router实例
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 开始页面加载进度条
    startPageLoading()
    
    try {
      const authStore = useAuthStore()
      
      // 恢复认证状态（仅在应用启动时）
      if (!authStore.isLoggedIn && hasStoredAuth()) {
        authStore.restoreAuthState()
      }
      
      // 检查是否在白名单中
      if (isInWhiteList(to.path)) {
        // 如果已登录且访问登录页，重定向到首页
        if (authStore.isLoggedIn && to.path === '/login') {
          next('/')
          return
        }
        next()
        return
      }
      
      // 检查是否已登录
      if (!authStore.isLoggedIn) {
        message.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
      
      // 检查Token是否即将过期
      if (authStore.isTokenExpiringSoon) {
        console.log('Token即将过期，尝试刷新...')
        const refreshSuccess = await authStore.refreshToken()
        if (!refreshSuccess) {
          // Token刷新失败，跳转到登录页
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }
      
      // 检查用户信息是否存在
      if (!authStore.userInfo) {
        const fetchSuccess = await authStore.fetchUserInfo()
        if (!fetchSuccess) {
          message.error('获取用户信息失败')
          await authStore.logout(false)
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }
      
      // 检查路由权限
      const hasPermission = checkRoutePermission(to.path, authStore)
      if (!hasPermission) {
        message.error('没有权限访问该页面')
        next('/403')
        return
      }
      
      // 记录页面访问日志
      logPageAccess(to, authStore)
      
      next()
      
    } catch (error) {
      console.error('路由守卫错误:', error)
      message.error('页面访问异常')
      next('/404')
    }
  })
  
  // 全局后置守卫
  router.afterEach((to, from) => {
    // 结束页面加载进度条
    finishPageLoading()
    
    // 设置页面标题
    setPageTitle(to)
    
    // 记录路由变化
    logRouteChange(to, from)
  })
  
  // 路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    message.error('页面加载失败')
    finishPageLoading()
  })
}

/**
 * 检查是否在白名单中
 */
function isInWhiteList(path: string): boolean {
  return WHITE_LIST.some(whitePath => {
    if (whitePath.endsWith('*')) {
      return path.startsWith(whitePath.slice(0, -1))
    }
    return path === whitePath
  })
}

/**
 * 检查是否有存储的认证信息
 */
function hasStoredAuth(): boolean {
  return !!(
    localStorage.getItem(StorageKeys.TOKEN) &&
    localStorage.getItem(StorageKeys.USER_INFO)
  )
}

/**
 * 检查路由权限
 */
function checkRoutePermission(path: string, authStore: any): boolean {
  // 管理员拥有所有权限
  if (authStore.isAdmin) {
    return true
  }
  
  // 检查特定路由的权限要求
  for (const [routePath, requiredPermissions] of Object.entries(PERMISSION_ROUTES)) {
    if (path.startsWith(routePath)) {
      // 检查是否有任一所需权限
      return requiredPermissions.some(permission => 
        authStore.hasPermission(permission)
      )
    }
  }
  
  // 默认允许访问（对于没有特殊权限要求的路由）
  return true
}

/**
 * 记录页面访问日志
 */
function logPageAccess(to: any, authStore: any): void {
  if (import.meta.env.DEV) {
    console.log('页面访问:', {
      path: to.path,
      fullPath: to.fullPath,
      user: authStore.userDisplayName,
      tenantId: authStore.userInfo?.tenantId,
      timestamp: new Date().toISOString()
    })
  }
  
  // 发送页面访问统计（生产环境）
  if (import.meta.env.PROD) {
    // TODO: 发送访问统计到后端
    // sendPageAccessLog(to, authStore.userInfo)
  }
}

/**
 * 记录路由变化
 */
function logRouteChange(to: any, from: any): void {
  if (import.meta.env.DEV) {
    console.log('路由变化:', {
      from: from.path,
      to: to.path,
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * 设置页面标题
 */
function setPageTitle(to: any): void {
  const baseTitle = 'VisThink ERP'
  let pageTitle = baseTitle
  
  if (to.meta?.title) {
    pageTitle = `${to.meta.title} - ${baseTitle}`
  } else if (to.name) {
    pageTitle = `${to.name} - ${baseTitle}`
  }
  
  document.title = pageTitle
}

/**
 * 开始页面加载进度条
 */
function startPageLoading(): void {
  // 这里可以集成进度条组件，如 nprogress
  if (window.NProgress) {
    window.NProgress.start()
  }
}

/**
 * 结束页面加载进度条
 */
function finishPageLoading(): void {
  // 延迟结束，确保页面渲染完成
  setTimeout(() => {
    if (window.NProgress) {
      window.NProgress.done()
    }
  }, 100)
}

/**
 * 权限指令工具函数
 */
export const PermissionUtils = {
  /**
   * 检查是否有权限
   */
  hasPermission(permission: string): boolean {
    const authStore = useAuthStore()
    return authStore.hasPermission(permission)
  },
  
  /**
   * 检查是否有角色
   */
  hasRole(role: string): boolean {
    const authStore = useAuthStore()
    return authStore.hasRole(role)
  },
  
  /**
   * 检查是否有任一权限
   */
  hasAnyPermission(permissions: string[]): boolean {
    const authStore = useAuthStore()
    return authStore.hasAnyPermission(permissions)
  },
  
  /**
   * 检查是否有所有权限
   */
  hasAllPermissions(permissions: string[]): boolean {
    const authStore = useAuthStore()
    return authStore.hasAllPermissions(permissions)
  }
}

/**
 * 路由元信息类型扩展
 */
declare module 'vue-router' {
  interface RouteMeta {
    // 页面标题
    title?: string
    // 所需权限
    permissions?: string[]
    // 所需角色
    roles?: string[]
    // 是否需要认证
    requireAuth?: boolean
    // 是否缓存页面
    keepAlive?: boolean
    // 页面图标
    icon?: string
    // 是否隐藏在菜单中
    hidden?: boolean
    // 面包屑
    breadcrumb?: boolean
  }
}

// 全局类型声明
declare global {
  interface Window {
    NProgress?: {
      start(): void
      done(): void
    }
  }
}
