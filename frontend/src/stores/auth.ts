/**
 * 用户认证状态管理
 * 使用Pinia管理JWT token、用户信息、权限等状态
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  loginApi, 
  logoutApi, 
  getUserInfoApi, 
  refreshTokenApi,
  shouldRefreshToken,
  formatUserDisplayName,
  getUserAvatarUrl
} from '@/api/auth'
import type { 
  LoginParams, 
  LoginResult, 
  UserInfo, 
  TokenInfo, 
  TenantInfo,
  AuthState,
  AuthEventType
} from '@/types/auth'
import { StorageKeys } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // ========== 状态定义 ==========
  
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)
  
  // Token信息
  const tokenInfo = ref<TokenInfo | null>(null)
  
  // 租户信息
  const tenantInfo = ref<TenantInfo | null>(null)
  
  // 加载状态
  const loginLoading = ref(false)
  const userInfoLoading = ref(false)
  const logoutLoading = ref(false)
  
  // 权限列表
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  
  // ========== 计算属性 ==========
  
  // 是否已登录
  const isLoggedIn = computed(() => {
    return !!(tokenInfo.value?.accessToken && userInfo.value)
  })
  
  // 用户显示名称
  const userDisplayName = computed(() => {
    return userInfo.value ? formatUserDisplayName(userInfo.value) : ''
  })
  
  // 用户头像URL
  const userAvatarUrl = computed(() => {
    return userInfo.value ? getUserAvatarUrl(userInfo.value) : ''
  })
  
  // 是否为管理员
  const isAdmin = computed(() => {
    return userInfo.value?.userType === 1
  })
  
  // 是否为租户管理员
  const isTenantAdmin = computed(() => {
    return userInfo.value?.userType === 2
  })
  
  // Token是否即将过期
  const isTokenExpiringSoon = computed(() => {
    if (!tokenInfo.value?.expiresAt) return false
    return shouldRefreshToken(tokenInfo.value.expiresAt)
  })
  
  // ========== 方法定义 ==========
  
  /**
   * 用户登录
   */
  const login = async (params: LoginParams): Promise<boolean> => {
    try {
      loginLoading.value = true
      
      // 调用登录API
      const response = await loginApi(params)
      const loginResult = response.data
      
      // 保存认证信息
      await setAuthInfo(loginResult)
      
      // 触发登录成功事件
      emitAuthEvent(AuthEventType.LOGIN_SUCCESS, loginResult)
      
      message.success('登录成功')
      return true
      
    } catch (error) {
      console.error('登录失败:', error)
      
      // 触发登录失败事件
      emitAuthEvent(AuthEventType.LOGIN_FAILED, error)
      
      // 错误信息已在HTTP拦截器中处理
      return false
      
    } finally {
      loginLoading.value = false
    }
  }
  
  /**
   * 用户登出
   */
  const logout = async (showMessage = true): Promise<void> => {
    try {
      logoutLoading.value = true
      
      // 如果有token，调用登出API
      if (tokenInfo.value?.accessToken) {
        await logoutApi()
      }
      
      if (showMessage) {
        message.success('已安全退出')
      }
      
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地状态
      
    } finally {
      // 清除认证信息
      clearAuthInfo()
      
      // 触发登出事件
      emitAuthEvent(AuthEventType.LOGOUT)
      
      logoutLoading.value = false
    }
  }
  
  /**
   * 获取用户信息
   */
  const fetchUserInfo = async (): Promise<boolean> => {
    try {
      userInfoLoading.value = true
      
      const response = await getUserInfoApi()
      const userData = response.data
      
      // 更新用户信息
      userInfo.value = userData
      permissions.value = userData.permissions || []
      roles.value = userData.roles || []
      
      // 保存到本地存储
      localStorage.setItem(StorageKeys.USER_INFO, JSON.stringify(userData))
      
      // 触发用户信息更新事件
      emitAuthEvent(AuthEventType.USER_INFO_UPDATED, userData)
      
      return true
      
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return false
      
    } finally {
      userInfoLoading.value = false
    }
  }
  
  /**
   * 刷新访问令牌
   */
  const refreshToken = async (): Promise<boolean> => {
    try {
      const refreshTokenValue = localStorage.getItem(StorageKeys.REFRESH_TOKEN)
      if (!refreshTokenValue) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await refreshTokenApi(refreshTokenValue)
      const loginResult = response.data
      
      // 更新Token信息
      await setAuthInfo(loginResult)
      
      console.log('Token刷新成功')
      return true
      
    } catch (error) {
      console.error('Token刷新失败:', error)
      
      // 刷新失败，清除认证信息并跳转登录
      await logout(false)
      
      // 触发Token过期事件
      emitAuthEvent(AuthEventType.TOKEN_EXPIRED)
      
      return false
    }
  }
  
  /**
   * 设置认证信息
   */
  const setAuthInfo = async (loginResult: LoginResult): Promise<void> => {
    const { accessToken, tokenType, expiresIn, userInfo: userData } = loginResult
    
    // 计算过期时间戳
    const expiresAt = Date.now() + (expiresIn * 1000)
    
    // 设置Token信息
    tokenInfo.value = {
      accessToken,
      tokenType,
      expiresIn,
      expiresAt
    }
    
    // 设置用户信息
    userInfo.value = userData
    permissions.value = userData.permissions || []
    roles.value = userData.roles || []
    
    // 保存到本地存储
    localStorage.setItem(StorageKeys.TOKEN, accessToken)
    localStorage.setItem(StorageKeys.USER_INFO, JSON.stringify(userData))
    
    // 如果有租户信息，保存租户信息
    if (userData.tenantId) {
      const tenant: TenantInfo = {
        id: userData.tenantId,
        name: userData.tenantName || '',
        code: '',
        status: 1
      }
      tenantInfo.value = tenant
      localStorage.setItem(StorageKeys.TENANT_INFO, JSON.stringify(tenant))
    }
  }
  
  /**
   * 清除认证信息
   */
  const clearAuthInfo = (): void => {
    // 清除状态
    userInfo.value = null
    tokenInfo.value = null
    tenantInfo.value = null
    permissions.value = []
    roles.value = []
    
    // 清除本地存储
    localStorage.removeItem(StorageKeys.TOKEN)
    localStorage.removeItem(StorageKeys.REFRESH_TOKEN)
    localStorage.removeItem(StorageKeys.USER_INFO)
    localStorage.removeItem(StorageKeys.TENANT_INFO)
    localStorage.removeItem(StorageKeys.PERMISSIONS)
  }
  
  /**
   * 从本地存储恢复认证状态
   */
  const restoreAuthState = (): void => {
    try {
      // 恢复Token
      const token = localStorage.getItem(StorageKeys.TOKEN)
      if (token) {
        // 这里简化处理，实际应该验证Token有效性
        tokenInfo.value = {
          accessToken: token,
          tokenType: 'Bearer',
          expiresIn: 86400,
          expiresAt: Date.now() + 86400000 // 24小时后过期
        }
      }
      
      // 恢复用户信息
      const userInfoStr = localStorage.getItem(StorageKeys.USER_INFO)
      if (userInfoStr) {
        const userData = JSON.parse(userInfoStr)
        userInfo.value = userData
        permissions.value = userData.permissions || []
        roles.value = userData.roles || []
      }
      
      // 恢复租户信息
      const tenantInfoStr = localStorage.getItem(StorageKeys.TENANT_INFO)
      if (tenantInfoStr) {
        tenantInfo.value = JSON.parse(tenantInfoStr)
      }
      
    } catch (error) {
      console.error('恢复认证状态失败:', error)
      clearAuthInfo()
    }
  }
  
  /**
   * 检查权限
   */
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }
  
  /**
   * 检查角色
   */
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }
  
  /**
   * 检查多个权限（AND关系）
   */
  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }
  
  /**
   * 检查多个权限（OR关系）
   */
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }
  
  /**
   * 触发认证事件
   */
  const emitAuthEvent = (type: AuthEventType, data?: any): void => {
    const event = new CustomEvent('auth-event', {
      detail: {
        type,
        data,
        timestamp: Date.now()
      }
    })
    window.dispatchEvent(event)
  }
  
  // ========== 返回状态和方法 ==========
  
  return {
    // 状态
    userInfo: readonly(userInfo),
    tokenInfo: readonly(tokenInfo),
    tenantInfo: readonly(tenantInfo),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loginLoading: readonly(loginLoading),
    userInfoLoading: readonly(userInfoLoading),
    logoutLoading: readonly(logoutLoading),
    
    // 计算属性
    isLoggedIn,
    userDisplayName,
    userAvatarUrl,
    isAdmin,
    isTenantAdmin,
    isTokenExpiringSoon,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    refreshToken,
    restoreAuthState,
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    clearAuthInfo
  }
})

// 导出类型
export type AuthStore = ReturnType<typeof useAuthStore>
