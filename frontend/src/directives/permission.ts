/**
 * 权限指令
 * 用于在模板中控制元素的显示和隐藏
 */
import type { App, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令类型
 */
interface PermissionBinding {
  value: string | string[] | {
    permission?: string | string[]
    role?: string | string[]
    mode?: 'all' | 'any' // all: 需要所有权限, any: 需要任一权限
  }
  modifiers: {
    hide?: boolean    // 隐藏元素而不是移除
    disable?: boolean // 禁用元素
  }
}

/**
 * 检查权限
 * @param binding 指令绑定对象
 * @returns 是否有权限
 */
function checkPermission(binding: DirectiveBinding): boolean {
  const authStore = useAuthStore()
  
  // 如果未登录，直接返回false
  if (!authStore.isLoggedIn) {
    return false
  }
  
  // 管理员拥有所有权限
  if (authStore.isAdmin) {
    return true
  }
  
  const { value } = binding
  
  // 如果没有指定权限，默认允许
  if (!value) {
    return true
  }
  
  // 字符串类型：单个权限
  if (typeof value === 'string') {
    return authStore.hasPermission(value)
  }
  
  // 数组类型：多个权限
  if (Array.isArray(value)) {
    return authStore.hasAnyPermission(value)
  }
  
  // 对象类型：复杂权限配置
  if (typeof value === 'object') {
    const { permission, role, mode = 'any' } = value
    
    let hasPermission = true
    let hasRole = true
    
    // 检查权限
    if (permission) {
      if (typeof permission === 'string') {
        hasPermission = authStore.hasPermission(permission)
      } else if (Array.isArray(permission)) {
        hasPermission = mode === 'all' 
          ? authStore.hasAllPermissions(permission)
          : authStore.hasAnyPermission(permission)
      }
    }
    
    // 检查角色
    if (role) {
      if (typeof role === 'string') {
        hasRole = authStore.hasRole(role)
      } else if (Array.isArray(role)) {
        hasRole = mode === 'all'
          ? role.every(r => authStore.hasRole(r))
          : role.some(r => authStore.hasRole(r))
      }
    }
    
    return hasPermission && hasRole
  }
  
  return false
}

/**
 * 处理元素显示/隐藏
 * @param el DOM元素
 * @param binding 指令绑定对象
 * @param hasPermission 是否有权限
 */
function handleElementVisibility(
  el: HTMLElement, 
  binding: DirectiveBinding, 
  hasPermission: boolean
) {
  const { modifiers } = binding
  
  if (!hasPermission) {
    if (modifiers.hide) {
      // 隐藏元素
      el.style.display = 'none'
    } else if (modifiers.disable) {
      // 禁用元素
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
      
      // 阻止点击事件
      el.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
      }, true)
    } else {
      // 移除元素
      el.remove()
    }
  } else {
    // 恢复元素状态
    if (modifiers.hide) {
      el.style.display = ''
    } else if (modifiers.disable) {
      el.removeAttribute('disabled')
      el.style.opacity = ''
      el.style.cursor = ''
    }
  }
}

/**
 * v-permission 指令
 * 用法：
 * v-permission="'user:create'"
 * v-permission="['user:create', 'user:update']"
 * v-permission="{ permission: 'user:create', role: 'admin' }"
 * v-permission.hide="'user:create'"
 * v-permission.disable="'user:create'"
 */
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const hasPermission = checkPermission(binding)
    handleElementVisibility(el, binding, hasPermission)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    const hasPermission = checkPermission(binding)
    handleElementVisibility(el, binding, hasPermission)
  }
}

/**
 * v-role 指令
 * 用法：
 * v-role="'admin'"
 * v-role="['admin', 'manager']"
 */
const role = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authStore = useAuthStore()
    const { value } = binding
    
    let hasRole = false
    
    if (typeof value === 'string') {
      hasRole = authStore.hasRole(value)
    } else if (Array.isArray(value)) {
      hasRole = value.some(r => authStore.hasRole(r))
    }
    
    if (!hasRole) {
      el.remove()
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 角色通常不会动态变化，所以updated可以为空
    // 如果需要支持动态角色变化，可以参考permission指令的实现
  }
}

/**
 * v-auth 指令（综合权限和角色检查）
 * 用法：
 * v-auth="{ permission: 'user:create', role: 'admin', mode: 'all' }"
 */
const auth = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const hasPermission = checkPermission(binding)
    handleElementVisibility(el, binding, hasPermission)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    const hasPermission = checkPermission(binding)
    handleElementVisibility(el, binding, hasPermission)
  }
}

/**
 * 安装权限指令
 * @param app Vue应用实例
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permission)
  app.directive('role', role)
  app.directive('auth', auth)
}

/**
 * 权限检查工具函数
 */
export const PermissionUtils = {
  /**
   * 检查是否有权限
   */
  hasPermission(permission: string): boolean {
    const authStore = useAuthStore()
    return authStore.hasPermission(permission)
  },
  
  /**
   * 检查是否有角色
   */
  hasRole(role: string): boolean {
    const authStore = useAuthStore()
    return authStore.hasRole(role)
  },
  
  /**
   * 检查是否有任一权限
   */
  hasAnyPermission(permissions: string[]): boolean {
    const authStore = useAuthStore()
    return authStore.hasAnyPermission(permissions)
  },
  
  /**
   * 检查是否有所有权限
   */
  hasAllPermissions(permissions: string[]): boolean {
    const authStore = useAuthStore()
    return authStore.hasAllPermissions(permissions)
  },
  
  /**
   * 检查复合权限
   */
  checkAuth(config: {
    permission?: string | string[]
    role?: string | string[]
    mode?: 'all' | 'any'
  }): boolean {
    return checkPermission({ value: config } as DirectiveBinding)
  }
}

export default {
  permission,
  role,
  auth,
  install: setupPermissionDirectives
}
