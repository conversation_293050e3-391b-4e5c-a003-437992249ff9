/**
 * 权限管理组合式函数
 * 提供权限检查、角色验证等功能
 */
import { computed, ref, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { UserInfo } from '@/types/auth'

/**
 * 权限配置接口
 */
export interface PermissionConfig {
  /** 权限编码 */
  permission?: string | string[]
  /** 角色编码 */
  role?: string | string[]
  /** 检查模式：all-需要所有权限，any-需要任一权限 */
  mode?: 'all' | 'any'
  /** 是否需要登录 */
  requireLogin?: boolean
  /** 是否允许管理员绕过 */
  allowAdmin?: boolean
}

/**
 * 使用权限管理
 */
export function usePermission() {
  const authStore = useAuthStore()
  
  // 当前用户信息
  const userInfo = computed<UserInfo | null>(() => authStore.userInfo)
  
  // 是否已登录
  const isLoggedIn = computed(() => authStore.isLoggedIn)
  
  // 是否是管理员
  const isAdmin = computed(() => authStore.isAdmin)
  
  // 是否是租户管理员
  const isTenantAdmin = computed(() => authStore.isTenantAdmin)
  
  // 用户权限列表
  const permissions = computed(() => authStore.permissions)
  
  // 用户角色列表
  const roles = computed(() => authStore.roles)
  
  /**
   * 检查是否有指定权限
   * @param permission 权限编码
   * @returns 是否有权限
   */
  const hasPermission = (permission: string): boolean => {
    return authStore.hasPermission(permission)
  }
  
  /**
   * 检查是否有指定角色
   * @param role 角色编码
   * @returns 是否有角色
   */
  const hasRole = (role: string): boolean => {
    return authStore.hasRole(role)
  }
  
  /**
   * 检查是否有任一权限
   * @param permissions 权限编码数组
   * @returns 是否有任一权限
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return authStore.hasAnyPermission(permissions)
  }
  
  /**
   * 检查是否有所有权限
   * @param permissions 权限编码数组
   * @returns 是否有所有权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return authStore.hasAllPermissions(permissions)
  }
  
  /**
   * 检查是否有任一角色
   * @param roles 角色编码数组
   * @returns 是否有任一角色
   */
  const hasAnyRole = (roles: string[]): boolean => {
    if (!userInfo.value?.roles) return false
    return roles.some(role => userInfo.value!.roles!.includes(role))
  }
  
  /**
   * 检查是否有所有角色
   * @param roles 角色编码数组
   * @returns 是否有所有角色
   */
  const hasAllRoles = (roles: string[]): boolean => {
    if (!userInfo.value?.roles) return false
    return roles.every(role => userInfo.value!.roles!.includes(role))
  }
  
  /**
   * 复合权限检查
   * @param config 权限配置
   * @returns 是否有权限
   */
  const checkAuth = (config: PermissionConfig): boolean => {
    const {
      permission,
      role,
      mode = 'any',
      requireLogin = true,
      allowAdmin = true
    } = config
    
    // 检查是否需要登录
    if (requireLogin && !isLoggedIn.value) {
      return false
    }
    
    // 管理员绕过权限检查
    if (allowAdmin && isAdmin.value) {
      return true
    }
    
    let hasPermissionResult = true
    let hasRoleResult = true
    
    // 检查权限
    if (permission) {
      if (typeof permission === 'string') {
        hasPermissionResult = hasPermission(permission)
      } else if (Array.isArray(permission)) {
        hasPermissionResult = mode === 'all' 
          ? hasAllPermissions(permission)
          : hasAnyPermission(permission)
      }
    }
    
    // 检查角色
    if (role) {
      if (typeof role === 'string') {
        hasRoleResult = hasRole(role)
      } else if (Array.isArray(role)) {
        hasRoleResult = mode === 'all'
          ? hasAllRoles(role)
          : hasAnyRole(role)
      }
    }
    
    return hasPermissionResult && hasRoleResult
  }
  
  /**
   * 创建权限计算属性
   * @param config 权限配置
   * @returns 权限计算属性
   */
  const createPermissionComputed = (config: PermissionConfig) => {
    return computed(() => checkAuth(config))
  }
  
  /**
   * 创建权限监听器
   * @param config 权限配置
   * @param callback 回调函数
   * @returns 停止监听函数
   */
  const watchPermission = (
    config: PermissionConfig,
    callback: (hasAuth: boolean) => void
  ) => {
    return watch(
      () => checkAuth(config),
      callback,
      { immediate: true }
    )
  }
  
  /**
   * 获取用户可访问的菜单
   * @param menus 菜单列表
   * @returns 过滤后的菜单列表
   */
  const filterMenusByPermission = <T extends { permission?: string; children?: T[] }>(
    menus: T[]
  ): T[] => {
    return menus.filter(menu => {
      // 如果没有权限要求，直接显示
      if (!menu.permission) {
        return true
      }
      
      // 检查权限
      const hasAuth = hasPermission(menu.permission)
      
      // 递归过滤子菜单
      if (menu.children) {
        menu.children = filterMenusByPermission(menu.children)
      }
      
      return hasAuth
    })
  }
  
  /**
   * 获取用户可用的操作按钮
   * @param buttons 按钮配置列表
   * @returns 过滤后的按钮列表
   */
  const filterButtonsByPermission = <T extends { permission?: string | string[] }>(
    buttons: T[]
  ): T[] => {
    return buttons.filter(button => {
      if (!button.permission) {
        return true
      }
      
      if (typeof button.permission === 'string') {
        return hasPermission(button.permission)
      }
      
      if (Array.isArray(button.permission)) {
        return hasAnyPermission(button.permission)
      }
      
      return false
    })
  }
  
  /**
   * 权限错误处理
   * @param error 错误信息
   */
  const handlePermissionError = (error: string = '权限不足') => {
    console.warn(`[Permission] ${error}`)
    // 可以在这里添加全局错误处理逻辑
    // 比如显示提示信息、跳转到无权限页面等
  }
  
  return {
    // 状态
    userInfo,
    isLoggedIn,
    isAdmin,
    isTenantAdmin,
    permissions,
    roles,
    
    // 权限检查方法
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    hasAllRoles,
    checkAuth,
    
    // 工具方法
    createPermissionComputed,
    watchPermission,
    filterMenusByPermission,
    filterButtonsByPermission,
    handlePermissionError
  }
}

/**
 * 使用页面权限
 * @param config 权限配置
 */
export function usePagePermission(config: PermissionConfig) {
  const { checkAuth, handlePermissionError } = usePermission()
  
  // 页面权限状态
  const hasPagePermission = ref(false)
  const permissionLoading = ref(true)
  
  // 检查页面权限
  const checkPagePermission = async () => {
    try {
      permissionLoading.value = true
      hasPagePermission.value = checkAuth(config)
      
      if (!hasPagePermission.value) {
        handlePermissionError('无权限访问此页面')
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      hasPagePermission.value = false
    } finally {
      permissionLoading.value = false
    }
  }
  
  // 监听认证状态变化
  watch(
    () => useAuthStore().isLoggedIn,
    () => {
      checkPagePermission()
    },
    { immediate: true }
  )
  
  return {
    hasPagePermission,
    permissionLoading,
    checkPagePermission
  }
}

/**
 * 使用操作权限
 * @param permissions 权限配置映射
 */
export function useActionPermissions<T extends Record<string, PermissionConfig>>(
  permissions: T
) {
  const { checkAuth } = usePermission()
  
  // 创建权限计算属性映射
  const actionPermissions = {} as Record<keyof T, ReturnType<typeof computed>>
  
  for (const [key, config] of Object.entries(permissions)) {
    actionPermissions[key as keyof T] = computed(() => checkAuth(config))
  }
  
  return actionPermissions
}

export default usePermission
