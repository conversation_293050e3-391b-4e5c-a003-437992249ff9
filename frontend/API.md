# VisThink ERP API 接口文档

## 📋 目录

- [接口规范](#接口规范)
- [认证授权](#认证授权)
- [用户管理接口](#用户管理接口)
- [角色权限接口](#角色权限接口)
- [租户管理接口](#租户管理接口)
- [系统监控接口](#系统监控接口)
- [消息通知接口](#消息通知接口)
- [错误码说明](#错误码说明)

## 接口规范

### 基础信息
- **协议**: HTTPS
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1

### 请求头
```http
Content-Type: application/json
Authorization: Bearer {token}
X-Tenant-Id: {tenantId}  // 多租户场景下必需
```

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000,
  "traceId": "abc123def456"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  }
}
```

## 认证授权

### 登录接口
```http
POST /api/auth/login
```

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456",
  "captcha": "abcd",
  "captchaKey": "uuid-key",
  "tenantId": "tenant001"
}
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "avatar": "avatar_url",
      "permissions": ["user:list", "user:create"]
    }
  }
}
```

### 刷新Token
```http
POST /api/auth/refresh
```

### 登出接口
```http
POST /api/auth/logout
```

## 用户管理接口

### 获取用户列表
```http
GET /api/member/user/list
```

**查询参数**:
```
username: string     // 用户名
email: string        // 邮箱
status: number       // 状态 1-启用 0-禁用
page: number         // 页码
size: number         // 页面大小
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "realName": "管理员",
        "department": "技术部",
        "status": 1,
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10
  }
}
```

### 创建用户
```http
POST /api/member/user
```

**请求参数**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "realName": "新用户",
  "department": "销售部",
  "password": "123456",
  "status": 1,
  "roleIds": [1, 2]
}
```

### 更新用户
```http
PUT /api/member/user/{id}
```

### 删除用户
```http
DELETE /api/member/user/{id}
```

### 批量删除用户
```http
DELETE /api/member/user/batch
```

**请求参数**:
```json
{
  "ids": [1, 2, 3]
}
```

## 角色权限接口

### 获取角色列表
```http
GET /api/member/role/list
```

### 获取角色权限
```http
GET /api/member/role/{id}/permission
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "menuIds": [1, 2, 3],
    "deptIds": [1, 2],
    "dataScope": "2"
  }
}
```

### 保存角色权限
```http
POST /api/member/role/{id}/permission
```

**请求参数**:
```json
{
  "menuIds": [1, 2, 3],
  "deptIds": [1, 2],
  "dataScope": "2"
}
```

### 获取菜单树
```http
GET /api/system/menu/tree
```

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "系统管理",
      "icon": "Setting",
      "path": "/system",
      "type": "M",
      "children": [
        {
          "id": 2,
          "title": "用户管理",
          "icon": "User",
          "path": "/system/user",
          "type": "C",
          "permission": "user:list"
        }
      ]
    }
  ]
}
```

## 租户管理接口

### 获取租户列表
```http
GET /api/system/tenant/list
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "name": "示例租户",
        "code": "tenant001",
        "contactName": "张三",
        "contactPhone": "13800138000",
        "email": "<EMAIL>",
        "packageName": "标准版",
        "expireTime": "2024-12-31 23:59:59",
        "userCount": 10,
        "userLimit": 50,
        "status": 1,
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "stats": {
      "total": 100,
      "active": 85,
      "expired": 5,
      "thisMonth": 10
    }
  }
}
```

### 创建租户
```http
POST /api/system/tenant
```

**请求参数**:
```json
{
  "name": "新租户",
  "code": "tenant002",
  "contactName": "李四",
  "contactPhone": "13800138001",
  "email": "<EMAIL>",
  "packageId": 1,
  "expireTime": "2024-12-31 23:59:59",
  "userLimit": 100,
  "adminUsername": "admin",
  "adminPassword": "123456",
  "adminRealName": "管理员",
  "adminEmail": "<EMAIL>"
}
```

### 租户续费
```http
POST /api/system/tenant/{id}/renew
```

**请求参数**:
```json
{
  "months": 12,
  "packageId": 1
}
```

## 系统监控接口

### 获取在线用户列表
```http
GET /api/monitor/online-user/list
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "sessionId": "session123",
        "username": "admin",
        "realName": "管理员",
        "ipAddress": "*************",
        "location": "北京市",
        "browser": "Chrome 96",
        "os": "Windows 10",
        "loginTime": "2024-01-01 09:00:00",
        "onlineDuration": 120,
        "status": "online"
      }
    ],
    "stats": {
      "total": 50,
      "today": 100,
      "peak": 80,
      "avgTime": "2小时30分钟"
    }
  }
}
```

### 强制用户下线
```http
POST /api/monitor/online-user/force-logout/{sessionId}
```

### 获取操作日志列表
```http
GET /api/monitor/operation-log/list
```

**查询参数**:
```
username: string     // 操作用户
module: string       // 操作模块
type: string         // 操作类型
status: number       // 操作状态
startTime: string    // 开始时间
endTime: string      // 结束时间
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "id": 1,
        "username": "admin",
        "realName": "管理员",
        "module": "用户管理",
        "title": "新增用户",
        "type": "INSERT",
        "method": "POST",
        "requestUrl": "/api/member/user",
        "requestParams": "{\"username\":\"test\"}",
        "responseResult": "{\"code\":200}",
        "userAgent": "Mozilla/5.0...",
        "ip": "*************",
        "location": "北京市",
        "status": 1,
        "duration": 150,
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "stats": {
      "total": 10000,
      "today": 500,
      "success": 9800,
      "error": 200
    }
  }
}
```

## 消息通知接口

### 短信渠道管理
```http
GET /api/system/sms/channel/list      // 获取渠道列表
POST /api/system/sms/channel          // 创建渠道
PUT /api/system/sms/channel/{id}      // 更新渠道
DELETE /api/system/sms/channel/{id}   // 删除渠道
```

### 发送短信
```http
POST /api/system/sms/send
```

**请求参数**:
```json
{
  "mobile": "13800138000",
  "templateCode": "SMS_001",
  "templateParams": {
    "code": "123456"
  }
}
```

### 短信发送日志
```http
GET /api/system/sms/log/list
```

### 邮件发送
```http
POST /api/system/email/send
```

**请求参数**:
```json
{
  "to": ["<EMAIL>"],
  "cc": ["<EMAIL>"],
  "subject": "邮件主题",
  "content": "邮件内容",
  "templateCode": "EMAIL_001",
  "templateParams": {
    "name": "用户名"
  }
}
```

## 错误码说明

### 通用错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 操作成功 | - |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 重新登录 |
| 403 | 权限不足 | 联系管理员 |
| 404 | 资源不存在 | 检查请求路径 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 业务错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 1001 | 用户名已存在 | 更换用户名 |
| 1002 | 密码错误 | 检查密码 |
| 1003 | 验证码错误 | 重新获取验证码 |
| 2001 | 租户已过期 | 联系管理员续费 |
| 2002 | 用户数量超限 | 升级套餐或删除用户 |
| 3001 | 短信发送失败 | 检查手机号或重试 |
| 3002 | 邮件发送失败 | 检查邮箱地址或重试 |

## 前端API调用示例

### 基础请求封装
```typescript
// utils/request.ts
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    if (userStore.tenantId) {
      config.headers['X-Tenant-Id'] = userStore.tenantId
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    if (code === 200) {
      return { data, message }
    } else {
      ElMessage.error(message)
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      // 处理未授权
      const userStore = useUserStore()
      userStore.logout()
    }
    ElMessage.error(error.message)
    return Promise.reject(error)
  }
)

export default request
```

### API接口定义
```typescript
// api/member/user.ts
import request from '@/utils/request'

export interface UserQuery {
  username?: string
  email?: string
  status?: number
  page: number
  size: number
}

export interface UserForm {
  id?: number
  username: string
  email: string
  phone: string
  realName: string
  department: string
  status: number
  password?: string
  roleIds: number[]
}

export const userApi = {
  // 获取用户列表
  getList: (params: UserQuery) => {
    return request.get('/api/member/user/list', { params })
  },
  
  // 创建用户
  create: (data: UserForm) => {
    return request.post('/api/member/user', data)
  },
  
  // 更新用户
  update: (id: number, data: UserForm) => {
    return request.put(`/api/member/user/${id}`, data)
  },
  
  // 删除用户
  delete: (id: number) => {
    return request.delete(`/api/member/user/${id}`)
  },
  
  // 批量删除用户
  batchDelete: (ids: number[]) => {
    return request.delete('/api/member/user/batch', { data: { ids } })
  }
}
```

### 组件中使用
```typescript
// 在Vue组件中使用
import { userApi } from '@/api/member/user'

const fetchUserList = async () => {
  loading.value = true
  try {
    const response = await userApi.getList({
      username: searchForm.username,
      page: pagination.current,
      size: pagination.size
    })
    userList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}
```

---

> **说明**: 本文档提供了VisThink ERP系统前端与后端交互的API接口规范。实际开发中请根据后端API文档进行调整。
