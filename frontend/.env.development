# 开发环境配置

# 应用标题
VITE_APP_TITLE=VisThink ERP 开发环境

# API基础URL - member-center微服务地址
VITE_API_BASE_URL=http://localhost:8081

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=10000

# 是否启用Mock数据
VITE_USE_MOCK=false

# 是否启用开发工具
VITE_DEV_TOOLS=true

# 是否显示性能监控信息
VITE_SHOW_PERFORMANCE=true

# 是否启用路由缓存
VITE_ROUTE_CACHE=true

# 上传文件大小限制（MB）
VITE_UPLOAD_SIZE_LIMIT=10

# 是否启用PWA
VITE_USE_PWA=false

# 构建时是否生成sourcemap
VITE_BUILD_SOURCEMAP=true

# 是否启用gzip压缩
VITE_BUILD_GZIP=false

# 是否启用分析工具
VITE_BUILD_ANALYZE=false

# 日志级别 (error | warn | info | debug)
VITE_LOG_LEVEL=debug

# 是否启用错误监控
VITE_ERROR_MONITORING=true

# WebSocket连接地址（如果需要）
VITE_WS_URL=ws://localhost:8081/ws

# 文件服务地址（如果有独立的文件服务）
VITE_FILE_SERVICE_URL=http://localhost:8082

# 是否启用多语言
VITE_I18N_ENABLED=true

# 默认语言
VITE_I18N_DEFAULT_LOCALE=zh-CN

# 主题配置
VITE_THEME_COLOR=#1890ff
VITE_THEME_MODE=light
